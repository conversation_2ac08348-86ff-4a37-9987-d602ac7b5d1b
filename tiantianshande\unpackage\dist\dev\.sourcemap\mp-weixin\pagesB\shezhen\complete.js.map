{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?cc3e", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?de76", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?80de", "uni-app:///pagesB/shezhen/complete.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?2127", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?73cd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordId", "analysisData", "isLoading", "loadingText", "healthScore", "constitutionType", "mainSymptom", "subSymptom", "displaySettings", "show_score", "show_score_value", "show_symptoms", "show_tongue_analysis", "show_care_advice", "show_product_recommend", "tongueFeatures", "abnormalFeatures", "typicalSymptoms", "riskData", "warningList", "dietSuggestions", "exerciseSuggestions", "healthSuggestions", "careSuggestions", "recommendProducts", "recommendTitle", "recommendVideos", "showVideoPlayer", "currentVideo", "nutritionData", "carbohydrate", "percent", "amount", "protein", "fat", "totalCalories", "nutritionItems", "name", "color", "recipeData", "effect", "image", "constitutionData", "type", "position", "x", "y", "statusColor", "statusText", "description", "explanation", "note", "scaleMarks", "marks", "angle", "opacity", "analysisApiCompleted", "orderNo", "apiType", "orderId", "computed", "pieChartStyle", "console", "background", "riskProgressStyles", "displayedTongueFeatures", "displayed", "feature", "markerStyle", "zIndex", "offset", "methods", "goBack", "uni", "viewDetailContent", "title", "icon", "viewPrincipleContent", "adjustRecipePlan", "url", "onShowHealthReport", "onShare", "onViewNutritionDetail", "onViewCompleteRegulation", "onStartTongueDetection", "<PERSON><PERSON><PERSON><PERSON>hartGradient", "cumulativePercent", "getAnalysisRecord", "setTimeout", "app", "id", "parseAnalysisData", "reportScore", "reportConstitutionType", "originalScore", "originalType", "parseTongueFeatures", "JSON", "parseDietSuggestions", "parseExerciseSuggestions", "parseRiskAssessment", "percentage", "calculateRiskPercentage", "parseReportData", "rawReportJson", "loadSimulateData", "food", "advice", "music", "sleep", "sport", "treatment", "retakePhoto", "shareReport", "viewTongueImage", "getMarkerStyle", "top", "left", "marginTop", "marginLeft", "transform", "parseCareSuggestions", "suggestions", "getOptimizedMarkerStyle", "baseStyle", "calculateOffsetForSamePosition", "getFeaturePositionClass", "getSimpleMarkerStyle", "parseRecommendProducts", "getRecommendProductsFromApi", "constitution_type", "constitution_score", "getRecommendVideos", "constitutionScore", "hasResponse", "code", "status", "hasData", "hasProducts", "productsLength", "pic", "recommend_reason", "view_num", "zan_num", "playVideo", "processedVideo", "original", "processed", "closeVideoPlayer", "onVideoError", "onVideoPlay", "onVideoPause", "e", "reason", "product", "content", "success", "fail", "duration", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACqC;;;AAG5F;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChFA;AAAA;AAAA;AAAA;AAA6vB,CAAgB,wwBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCipBjxB;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MAEA;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MACAC;MAAA;MACAC;MAAA;;MAEA;MACAC;QACAC;UAAAC;UAAAC;QAAA;QACAC;UAAAF;UAAAC;QAAA;QACAE;UAAAH;UAAAC;QAAA;QACAG;QACAC,iBACA;UAAAC;UAAAN;UAAAC;UAAAM;QAAA,GACA;UAAAD;UAAAN;UAAAC;UAAAM;QAAA,GACA;UAAAD;UAAAN;UAAAC;UAAAM;QAAA;MAEA;MAEA;MACAC;QACAF;QACAG;QACAC;MACA;MAEA;MACAC;QACAC;QACAC;UAAAC;UAAAC;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACA;QACA;UACA;UACA;UACAC;YACAC;YACAC;UACA;QACA;QACA;MACA;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;QAAA;UACAD;QACA;MAAA;IACA;IAEA;IACArB;MACA;QACAC;QACAC;UAAAC;UAAAC;QAAA;QAAA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;UACAC;UACAC;QACA;MACA;MACA;IACA;IAEA;IACAU;MAAA;MACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;;MAEA;MACA;MACA;QACAC;MACA;QACAA;MACA;;MAEA;MACA;QACA;QACA;;QAEA;QACA,uCACAC;UACAC;YACAC;UAAA,GACAC;QACA;MAEA;IACA;EACA;EACAC;IACA;IACAC;MACAV;MACAW;IACA;IAEA;IACAC;MACAZ;MACA;MACAW;QACAE;QACAC;MACA;IACA;IAEA;IACAC;MACAf;MACA;MACAW;QACAE;QACAC;MACA;IACA;IAEA;IACAE;MACAhB;MACAA;MACAW;QACAM;MACA;IACA;IAEA;IACAC;MACAlB;MACAW;QACAM;MACA;IACA;IAEA;IACAE;MACAnB;MACAW;QACAE;QACAC;MACA;IACA;IAEA;IACAM;MACApB;MACAW;QACAM;MACA;IACA;IAEA;IACAI;MACArB;MACAW;QACAM;MACA;IACA;IAEA;IACAK;MACAtB;MACAW;QACAM;MACA;IACA;IAEA;IACAM;MACAvB;MACA;MACA;QACA;QACA;QACAwB;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACAzB;MAEA;QACAA;QACAW;UACAE;UACAC;QACA;QACAY;UACAf;QACA;QACA;MACA;MAEAA;QACAE;MACA;;MAEA;MACA;MACAc;QACAC;MACA;QACA5B;QAEAW;QAEA;UACAX;UACA;UACA;QACA;UACAA;UACAW;YACAE;YACAC;UACA;UACAY;YACAf;UACA;QACA;MACA;QACAX;QACAW;QACAA;UACAE;UACAC;QACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAe;MACA7B;MACAA;MAEA;QACA;QACA;;QAEA;QACA;UACAA;UACA;YACArD;YACAC;YAAA;YACAC;YACAC;YACAC;YACAC;UACA;UACAgD;UACAA;UACAA;QACA;;QAEA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;cACA8B;YACA;YACA;cACAC;YACA;UACA;YACA/B;UACA;QACA;QAEA;QACA;QACA;QAEAA;UACA1D;UACAC;UACAyF;UACAC;UACAH;UACAC;QACA;;QAEA;QACA;UACA/B;QACA;;QAEA;QACA;UACAA;QACA;;QAEA;QACA;UACA;YAAA;UAAA;UACAA;QACA;UACA;UACA;YAAA;UAAA;UACAA;QACA;UACAA;UACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;QACA;QAEAA;MAEA;QACAA;QACAW;UACAE;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAoB;MACAlC;MAEA;QACA;UACA,0DACAmC,mCACAlG;UAEA;YACA;YACA;YACA;cAAA,OACAoE;YAAA,EACA;YACAL;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAoC;MACApC;MAEA;QACA;UACA,8DACAmC,oCACAlG;UAEA;YACA;YACA+D;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAqC;MACArC;MAEA;QACA;UACA,kEACAmC,wCACAlG;UAEA;YACA;YACA+D;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAsC;MAAA;MACAtC;MAEA;QACA;QACA;UACA;UACA;YAAA;UAAA;UAEA;YACA;YACA;YACA;YACA;cACAzB;cACAgE;YACA;UACA;;UAEAvC;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAwC;MACA;MACA;MACA;MACA;MAEA;;MAEA;QAAA;MAAA;QACAD;MACA;QAAA;MAAA;QACAA;MACA;QAAA;MAAA;QACAA;MACA;MAEA;IACA;IAEA;AACA;AACA;IACAE;MACAzC;MAEA;QACA,qDACAmC,4BACAO;;QAEA;QACA;UACA;YAAA;UAAA;UACA1C;QACA;;QAEA;QACA;UACA;UACA;YAAA,OACAK;UAAA,EACA;QACA;;QAEA;QACA;UACA;UACA;UACAL;UAEA;YACA;UACA;UACA;YACA;UACA;QACA;QAEAA;MAEA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACA2C;MACA3C;;MAEA;MACA;QACA4C,OACA;UACAC;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA,EACA;QACAiC,QACA;UACAD;UACAhC;QACA,EACA;QACAkC,QACA;UACAF;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA,EACA;QACAmC,QACA;UACAH;UACAhC;QACA,EACA;QACAoC,YACA;UACAJ;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA,GACA;UACAgC;UACAhC;QACA;MAEA;MAEAb;IACA;IAEA;IACAkD;MACAlD;MACAW;QACAM;MACA;IACA;IAEA;IACAkC;MACAnD;MACAW;QACAE;QACAC;MACA;IACA;IAEA;IACAsC;MACApD;MACA;MACAW;QACAM;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAoC;MACA;QACA;QACA;UAAAC;UAAAC;QAAA;QAEA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QAEA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QAEA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QAEA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QAEA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;QACA;UAAAD;UAAAC;QAAA;MACA;;MAEA;MACA;QAAAD;QAAAC;MAAA;;MAEA;MACA;QACA;QACA;QACA;QAEA;UACAzE;UACAwE;UACAC;UACAC;UAAA;UACAC;UAAA;UACAlD;QACA;MACA;QACA;QACA;UACAzB;UACAwE;UACAC;UACAG;UACAnD;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAoD;MACA3D;MAEA;QACA;QACA;UACA;UACAA;;UAEA;UACA;YACA;UACA;UACA;YACA;UACA;UAEA;QACA;;QAEA;QACA;;QAEA;QACA;UACA;YACA,2DACAmC,oCACAlG;YACA;cACA2H;YACA;UACA;YACA5D;UACA;QACA;QAEA;UACA;YACA,mEACAmC,wCACAlG;YACA;cACA2H;YACA;UACA;YACA5D;UACA;QACA;;QAEA;QACA;UACA;UACAA;QACA;UACAA;QACA;MAEA;QACAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACA6D;MACA;MACA;;MAEA;MACA;QACA;QACA;QACA;UACA,uCACAC;YACAR;YACAC;UAAA;QAEA;MACA;MAEA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;IACAQ;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACA;QACA;UACAP;UACAC;QACA;MACA;QACA;QACA;QACA;UACAD;QACA;MACA;QACA;QACA;QACA;UACAA;QACA;MACA;MAEA;IACA;IACAQ;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;MACAhE;MACA;IACA;IACAiE;MACA;MACA;MAEA;QACA1D;MAAA,GACAC,OACA;MAEAR;;MAEA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAkE;MACAlE;MAEA;QACA;QACA;UACA;UACAA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;UACAA;UACA;QACA;MAEA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAmE;MAAA;MACAnE;MAEA;MACA2B;QACAyC;QACAC;MACA;QACArE;QAEA;UACA;UACA;YACA;YACA;cACA;YACA;YACAA;UACA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAsE;MAAA;MACAtE;;MAEA;MACA;MACA;MAEAA;QACAzD;QACAgI;MACA;;MAEA;MACA;QACAvE;QACA;MACA;MAEA;;MAEA;MACA2B;QACAyC;QACAC;MACA;QACArE;;QAEA;QACA;UACA;UACA;YAAA;UAAA;UACA;UACAA;;UAEA;UACA;YACAA;UACA;QACA;UAAA;UACAA;YACAwE;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;;UAEA;UACA7E;UACA;YACA4B;YACArD;YACAM;YACAoC;YACA6D;YACAC;YACAC;YACAC;UACA;QACA;MACA;QACAjF;;QAEA;QACAA;QACA;UACA4B;UACArD;UACAM;UACAoC;UACA6D;UACAC;UACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAlF;MAEA;QACAW;UACAE;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACAqE;MACA;QACA;QACA;UACA;UACA;YACA;YACA;cACA;YACA;YACA;UACA;UACAA;UACAnF;YACAoF;YACAC;UACA;QACA;UACArF;UACAmF;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACAnF;MACAA;IACA;IAEA;AACA;AACA;IACAsF;MACAtF;MACA;MACA;IACA;IAEA;AACA;AACA;IACAuF;MACAvF;MACAW;QACAE;QACAC;MACA;IACA;IAEA;AACA;AACA;IACA0E;MACAxF;IACA;IAEA;AACA;AACA;IACAyF;MACAzF;IACA;EAAA,+EAKA0F;IACA1F;EACA,kFAKA0F;IACA1F;EACA,0FAKA0F;IACA1F;EACA,sFAKA0F;IACA1F;EACA,4FAKA2F;IACA;IAEA;IACA;IACA;IACA;IACA;EACA,4FAKAC;IACA5F;;IAEA;IACA;MACA;MACAW;QACAM;MACA;IACA;MACA;MACAN;QACAM;MACA;IACA;EACA,8EAKA2E;IACA5F;;IAEA;IACAW;MACAE;MACAgF;MACAC;QACA;UACA;UACA;YACA;YACAnF;cACAM;YACA;UACA;YACA;YACAN;cACAM;YACA;UACA;QACA;MACA;IACA;EACA,4FAKA2E;IACA5F;;IAEA;IACA;IACA;;IAEA;MACA;MACAiB;IACA;MACA;MACAA;IACA;IAEAjB;IAEAW;MACAM;MACA6E;QACA9F;MACA;MACA+F;QACA/F;QACAW;UACAE;UACAC;QACA;MACA;IACA;EACA,4EAKA8E;IACA5F;IAEA;MACA;MACA;IACA;MACA;MACAW;QACAE;QACAC;QACAkF;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA,aACA;EACAC;IAAA;IACAjG;;IAEA;;IAEA;IACAA;;IAOA;IACA;MACA;MACAA;IACA;IAEA;MACA;MACAA;IACA;IAEA;MACA;MACAA;IACA;;IAEA;IACA;MACA;MACAA;IACA;;IAEA;IACA0B;MACA1B;MACA;QACA4B;QACArD;QACAM;QACAoC;QACA6D;QACAC;QACAC;QACAC;MACA;MACAjF;IACA;;IAEA;IACA;MACA;MACA;IACA;MACAA;MACAW;QACAE;QACAC;MACA;MACAY;QACAf;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/gEA;AAAA;AAAA;AAAA;AAAqmC,CAAgB,ilCAAG,EAAC,C;;;;;;;;;;;ACAznC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/shezhen/complete.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/shezhen/complete.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./complete.vue?vue&type=template&id=39446614&scoped=true&\"\nvar renderjs\nimport script from \"./complete.vue?vue&type=script&lang=js&\"\nexport * from \"./complete.vue?vue&type=script&lang=js&\"\nimport style0 from \"./complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"39446614\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/shezhen/complete.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete.vue?vue&type=template&id=39446614&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !!_vm.displaySettings.show_symptoms\n    ? _vm.typicalSymptoms.length\n    : null\n  var l0 = !!_vm.displaySettings.show_tongue_analysis\n    ? _vm.__map(_vm.displayedTongueFeatures, function (feature, index) {\n        var $orig = _vm.__get_orig(feature)\n        var m0 = _vm.getFeaturePositionClass(feature.feature_group)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g1 = !!_vm.displaySettings.show_tongue_analysis\n    ? _vm.tongueFeatures.length\n    : null\n  var g2 = !!_vm.displaySettings.show_symptoms\n    ? _vm.abnormalFeatures.length\n    : null\n  var g3 = !!_vm.displaySettings.show_symptoms\n    ? _vm.abnormalFeatures.length\n    : null\n  var g4 =\n    !!_vm.displaySettings.show_care_advice &&\n    _vm.careSuggestions &&\n    Object.keys(_vm.careSuggestions).length > 0\n  var g5 = g4\n    ? _vm.careSuggestions.food && _vm.careSuggestions.food.length > 0\n    : null\n  var g6 = g4\n    ? _vm.careSuggestions.sport && _vm.careSuggestions.sport.length > 0\n    : null\n  var g7 = g4\n    ? _vm.careSuggestions.sleep && _vm.careSuggestions.sleep.length > 0\n    : null\n  var g8 = g4\n    ? _vm.careSuggestions.music && _vm.careSuggestions.music.length > 0\n    : null\n  var g9 = g4\n    ? _vm.careSuggestions.treatment && _vm.careSuggestions.treatment.length > 0\n    : null\n  var g10 =\n    !!_vm.displaySettings.show_product_recommend &&\n    _vm.recommendProducts &&\n    _vm.recommendProducts.length > 0\n  var g11 = _vm.recommendVideos.length > 0 || true\n  var g12 = g11 ? _vm.recommendVideos.length : null\n  var g13 = g11 && g12 === 0 ? _vm.recommendVideos.length : null\n  var g14 = g11 ? _vm.recommendVideos.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n        g11: g11,\n        g12: g12,\n        g13: g13,\n        g14: g14,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"tongue-complete\">\n\t\t<!-- 2025-01-03 22:55:53,565-INF0-[complete][init_001] 舌诊完整结果页面初始化 -->\n\t\t\n\t\t<!-- 顶部导航 -->\n\t\t<!-- <view class=\"top-nav\">\n\t\t\t<view class=\"nav-left\" @click=\"goBack\">\n\t\t\t\t<text class=\"back-icon\">‹</text>\n\t\t\t</view>\n\t\t\t<view class=\"nav-title\">健康报告</view>\n\t\t\t<view class=\"nav-right\">\n\t\t\t\t<text class=\"menu-icon\">•••</text>\n\t\t\t\t<text class=\"record-icon\">●</text>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 医院标识 -->\n\t\t<view class=\"hospital-info\">\n\t\t\t<text> 国家重点实验室项目 | 国家重大专项</text>\n\t\t</view>\n\t\t\n\t\t<!-- 报告类型选择 -->\n\t\t<view class=\"report-tabs\">\n\t\t\t<view class=\"tab-item active\">\n\t\t\t\t<text class=\"tab-text\">舌象报告</text>\n\t\t\t\t<view class=\"tab-line\"></view>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\">\n\t\t\t\t<text class=\"tab-text\">面诊报告</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 健康得分卡片 -->\n\t\t<view class=\"health-card\" v-if=\"!!displaySettings.show_score\">\n\t\t\t<view class=\"health-content\">\n\t\t\t\t<view class=\"health-left\">\n\t\t\t\t\t<image class=\"health-avatar\" :src=\"analysisData && analysisData.tongue_image ? analysisData.tongue_image : '/static/images/moisture-avatar.png'\"></image>\n\t\t\t\t\t<view class=\"health-symptoms\">\n\t\t\t\t\t\t<view class=\"main-symptom\">\n\t\t\t\t\t\t\t<text class=\"symptom-label\">体质类型：</text>\n\t\t\t\t\t\t\t<text class=\"symptom-value\">{{constitutionType}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"sub-symptom\" v-if=\"analysisData && analysisData.syndrome_name\">\n\t\t\t\t\t\t\t<text class=\"symptom-label\">证候：</text>\n\t\t\t\t\t\t\t<text class=\"symptom-value\">{{analysisData.syndrome_name}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"health-right\">\n\t\t\t\t\t<view class=\"score-circle\">\n\t\t\t\t\t\t<view class=\"score-inner\">\n\t\t\t\t\t\t\t<!-- 2025-01-27 根据show_score_value设置控制分值数字显示 -->\n\t\t\t\t\t\t\t<text class=\"score-number\" v-if=\"!!displaySettings.show_score_value\">{{healthScore}}</text>\n\t\t\t\t\t\t\t<text class=\"score-number no-score\" v-else>--</text>\n\t\t\t\t\t\t\t<text class=\"score-text\">健康得分</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 智能问诊 -->\n\t\t<!-- <view class=\"ai-diagnosis\">\n\t\t\t<view class=\"ai-left\">\n\t\t\t\t<view class=\"ai-title\">智能问诊</view>\n\t\t\t\t<text class=\"ai-subtitle\" v-if=\"healthSuggestions\">体质分析结果</text>\n\t\t\t\t<text class=\"ai-subtitle\" v-else>分析中...</text>\n\t\t\t\t<text class=\"ai-description\" v-if=\"healthSuggestions\">{{healthSuggestions}}</text>\n\t\t\t\t<text class=\"ai-description\" v-else>正在为您生成详细的体质分析报告...</text>\n\t\t\t</view>\n\t\t\t<view class=\"ai-right\" v-if=\"healthSuggestions\">\n\t\t\t\t<view class=\"analysis-complete-btn\">已完成</view>\n\t\t\t</view>\n\t\t\t<view class=\"ai-right\" v-else>\n\t\t\t\t<view class=\"loading-btn\">分析中</view>\n\t\t\t</view>\n\t\t</view>\n\t\t -->\n\t\t<!-- 可能体征 -->\n\t\t<view class=\"symptoms-section\" v-if=\"!!displaySettings.show_symptoms\">\n\t\t\t<text class=\"section-title\">您可能有以下体征</text>\n\t\t\t<view class=\"symptoms-grid\">\n\t\t\t\t<view class=\"symptom-tag\" v-for=\"(symptom, index) in typicalSymptoms\" :key=\"index\">\n\t\t\t\t\t{{symptom}}\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"typicalSymptoms.length === 0\" class=\"no-symptoms\">\n\t\t\t\t\t<text>暂无典型症状数据</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 舌象分析 -->\n\t\t<view class=\"tongue-analysis\" v-if=\"!!displaySettings.show_tongue_analysis\">\n\t\t\t<text class=\"section-title\">舌象分析</text>\n\t\t\t<view class=\"tongue-diagram\">\n\t\t\t\t<!-- CSS绘制的舌头图 -->\n\t\t\t\t<view class=\"tongue-svg\">\n\t\t\t\t\t<view class=\"tongue-shape\"></view>\n\t\t\t\t<view class=\"tongue-markers\">\n\t\t\t\t\t\t<!-- 动态显示检测到的舌象特征（优先显示异常特征） -->\n\t\t\t\t\t\t<view \n\t\t\t\t\t\t\tv-for=\"(feature, index) in displayedTongueFeatures\" \n\t\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t\tclass=\"marker\" \n\t\t\t\t\t\t:class=\"[\n\t\t\t\t\t\t\tfeature.feature_situation === '异常' ? 'abnormal' : 'normal',\n\t\t\t\t\t\t\t'marker-' + getFeaturePositionClass(feature.feature_group)\n\t\t\t\t\t\t]\"\n\t\t\t\t\t\t\t:style=\"feature.markerStyle\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<text class=\"marker-icon\">{{ feature.feature_situation === '异常' ? '!' : '✓' }}</text>\n\t\t\t\t\t\t\t<text class=\"marker-text\">{{ feature.feature_name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t<view class=\"tongue-photo-btn\" @click=\"viewTongueImage\">\n\t\t\t\t\t<text class=\"photo-btn-text\">查看我的舌象</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 详细舌象特征列表 -->\n\t\t\t<view class=\"tongue-features-detail\">\n\t\t\t\t<view class=\"features-header\">\n\t\t\t\t\t<text class=\"features-title\">舌象特征分析</text>\n\t\t\t\t\t<text class=\"features-subtitle\">AI智能识别 · 专业解读</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"features-grid\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tv-for=\"(feature, index) in tongueFeatures\" \n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\tclass=\"feature-card\"\n\t\t\t\t\t\t:class=\"feature.feature_situation === '异常' ? 'abnormal-card' : 'normal-card'\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t\t<view class=\"feature-badge\" :class=\"feature.feature_situation === '异常' ? 'abnormal-badge' : 'normal-badge'\">\n\t\t\t\t\t\t\t\t{{ feature.feature_situation }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"feature-category\">{{ feature.feature_group }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"feature-name\">{{ feature.feature_name }}</text>\n\t\t\t\t\t\t<text class=\"feature-description\">{{ feature.feature_interpret }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"tongueFeatures.length === 0\" class=\"no-features\">\n\t\t\t\t\t<view class=\"empty-icon\">🔍</view>\n\t\t\t\t\t<text class=\"empty-text\">暂无舌象特征数据</text>\n\t\t\t\t\t<text class=\"empty-hint\">请重新拍摄或检查图片质量</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 体征异常 -->\n\t\t<view class=\"abnormal-section\" v-if=\"!!displaySettings.show_symptoms\">\n\t\t\t<view class=\"abnormal-title\">\n\t\t\t\t<text class=\"abnormal-count\">您有{{abnormalFeatures.length}}项表征异常</text>\n\t\t\t</view>\n\t\t\t<view class=\"abnormal-list\">\n\t\t\t\t<view class=\"abnormal-item\" v-for=\"(feature, index) in abnormalFeatures\" :key=\"index\">\n\t\t\t\t\t<view class=\"abnormal-header\">\n\t\t\t\t\t\t<text class=\"abnormal-icon\">!</text>\n\t\t\t\t\t\t<text class=\"abnormal-name\">{{feature.name || feature.feature_name}}：</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"abnormal-desc\">{{feature.interpret || feature.feature_interpret}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"abnormalFeatures.length === 0\" class=\"no-abnormal\">\n\t\t\t\t\t<text>恭喜您！暂无异常特征发现</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 舌象体征论述 - 简约医疗风格 -->\n\t\t<view class=\"theory-section\" v-if=\"!!displaySettings.show_tongue_analysis\">\n\t\t\t<view class=\"theory-header-wrapper\">\n\t\t\t\t<view class=\"theory-icon\">📋</view>\n\t\t\t\t<text class=\"section-title\">舌象体征论述</text>\n\t\t\t\t<text class=\"theory-subtitle\">专业医学解读</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 体质分析卡片 -->\n\t\t\t<view class=\"theory-card constitution-card\" v-if=\"healthSuggestions\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t\t<view class=\"theory-tag constitution-tag\">体质分析</view>\n\t\t\t\t\t\t<text class=\"theory-title\">{{constitutionType}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t\t<view class=\"status-indicator active\">\n\t\t\t\t\t\t\t<text class=\"status-dot\">●</text>\n\t\t\t\t\t\t\t<text class=\"status-text\">已完成</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"content-wrapper\">\n\t\t\t\t\t\t<text class=\"theory-description\">{{healthSuggestions}}</text>\n\t\t\t\t\t\t<view class=\"medical-stamp\">\n\t\t\t\t\t\t\t<image class=\"doctor-avatar-small\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t\t\t\t\t<text class=\"stamp-text\">专业解读</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 证候介绍卡片 -->\n\t\t\t<view class=\"theory-card syndrome-card\" v-if=\"analysisData && analysisData.syndrome_introduction\">\n\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t<view class=\"header-left\">\n\t\t\t\t\t\t<view class=\"theory-tag syndrome-tag\">证候介绍</view>\n\t\t\t\t\t\t<text class=\"theory-title\">{{analysisData.syndrome_name}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"header-right\">\n\t\t\t\t\t\t<view class=\"status-indicator active\">\n\t\t\t\t\t\t\t<text class=\"status-dot\">●</text>\n\t\t\t\t\t\t\t<text class=\"status-text\">已完成</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t<view class=\"content-wrapper\">\n\t\t\t\t\t\t<text class=\"theory-description\">{{analysisData.syndrome_introduction}}</text>\n\t\t\t\t\t\t<view class=\"medical-stamp\">\n\t\t\t\t\t\t\t<image class=\"doctor-avatar-small\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t\t\t\t\t<text class=\"stamp-text\">医学权威</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 空状态 -->\n\t\t\t<view v-if=\"!healthSuggestions && (!analysisData || !analysisData.syndrome_introduction)\" class=\"empty-theory-state\">\n\t\t\t\t<view class=\"empty-icon\">🔍</view>\n\t\t\t\t<text class=\"empty-title\">暂无体征论述数据</text>\n\t\t\t\t<text class=\"empty-description\">正在为您生成专业的医学解读报告</text>\n\t\t\t\t<view class=\"loading-dots\">\n\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t\t<view class=\"dot\"></view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 患病风险 -->\n\t\t<!-- <view class=\"risk-section\">\n\t\t\t<text class=\"section-title\">患病风险</text>\n\t\t\t<view class=\"risk-grid\">\n\t\t\t\t<view class=\"risk-item\" v-for=\"(item, index) in riskData\" :key=\"index\">\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"risk-progress-circle\">\n\t\t\t\t\t\t<view class=\"progress-ring\" :style=\"riskProgressStyles[index]\">\n\t\t\t\t\t\t\t<view class=\"progress-inner\">\n\t\t\t\t\t\t\t\t<text class=\"progress-percentage\">{{item.percentage}}%</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"risk-name\">{{item.name}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t -->\n\t\t<!-- 需要警惕 -->\n\t\t<!-- <view class=\"warning-section\">\n\t\t\t<text class=\"section-title\">需要警惕</text>\n\t\t\t<view class=\"warning-list\">\n\t\t\t\t<view class=\"warning-item\" v-for=\"(warning, index) in warningList\" :key=\"index\">\n\t\t\t\t\t<view class=\"warning-header\">\n\t\t\t\t\t\t<text class=\"warning-bullet\">•</text>\n\t\t\t\t\t\t<text class=\"warning-title\">{{warning.name || warning.title}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"warning-desc\">{{warning.description || warning.advice}}</text>\n\t\t\t\t\t<image v-if=\"warning.showDoctor\" class=\"doctor-avatar\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"analysisData && analysisData.risk_assessment\" class=\"risk-assessment-detail\">\n\t\t\t\t\t<text class=\"risk-detail-text\">{{analysisData.risk_assessment}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"warningList.length === 0 && (!analysisData || !analysisData.risk_assessment)\" class=\"no-warning\">\n\t\t\t\t\t<text>暂无特别需要警惕的风险</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 保养建议 -->\n\t\t<view class=\"tongue-analysis\" v-if=\"!!displaySettings.show_care_advice && careSuggestions && Object.keys(careSuggestions).length > 0\">\n\t\t\t<!-- <text class=\"section-title\">专业保养建议</text> -->\n\t\t\t\n\t\t\t<!-- 饮食建议 -->\n\t\t\t<view class=\"care-category\" v-if=\"careSuggestions.food && careSuggestions.food.length > 0\">\n\t\t\t\t<view class=\"category-header\">\n\t\t\t\t\t<view class=\"category-icon food-icon\">🍽️</view>\n\t\t\t\t\t<text class=\"category-title\">饮食调养</text>\n\t\t\t\t\t<view class=\"category-subtitle\">科学膳食，合理营养</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"suggestion-list\">\n\t\t\t\t\t<view class=\"suggestion-item\" v-for=\"(item, index) in careSuggestions.food\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t\t<view class=\"item-tag\" :class=\"item.title === '禁忌饮食' ? 'forbidden-tag' : 'recommend-tag'\">\n\t\t\t\t\t\t\t\t{{item.title}}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"item-content\">{{item.advice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 运动建议 -->\n\t\t\t<view class=\"care-category\" v-if=\"careSuggestions.sport && careSuggestions.sport.length > 0\">\n\t\t\t\t<view class=\"category-header\">\n\t\t\t\t\t<view class=\"category-icon sport-icon\">🏃</view>\n\t\t\t\t\t<text class=\"category-title\">运动保健</text>\n\t\t\t\t\t<view class=\"category-subtitle\">适度运动，强身健体</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"suggestion-list\">\n\t\t\t\t\t<view class=\"suggestion-item\" v-for=\"(item, index) in careSuggestions.sport\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t\t<view class=\"item-tag recommend-tag\">{{item.title}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"item-content\">{{item.advice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 生活建议 -->\n\t\t\t<view class=\"care-category\" v-if=\"careSuggestions.sleep && careSuggestions.sleep.length > 0\">\n\t\t\t\t<view class=\"category-header\">\n\t\t\t\t\t<view class=\"category-icon life-icon\">🌱</view>\n\t\t\t\t\t<text class=\"category-title\">生活调理</text>\n\t\t\t\t\t<view class=\"category-subtitle\">规律作息，身心平衡</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"suggestion-list\">\n\t\t\t\t\t<view class=\"suggestion-item\" v-for=\"(item, index) in careSuggestions.sleep\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t\t<view class=\"item-tag life-tag\">{{item.title}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"item-content\">{{item.advice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 音乐建议 -->\n\t\t\t<view class=\"care-category\" v-if=\"careSuggestions.music && careSuggestions.music.length > 0\">\n\t\t\t\t<view class=\"category-header\">\n\t\t\t\t\t<view class=\"category-icon music-icon\">🎵</view>\n\t\t\t\t\t<text class=\"category-title\">音乐疗法</text>\n\t\t\t\t\t<view class=\"category-subtitle\">调和阴阳，怡情养性</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"suggestion-list\">\n\t\t\t\t\t<view class=\"suggestion-item\" v-for=\"(item, index) in careSuggestions.music\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t\t<view class=\"item-tag music-tag\">{{item.title}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"item-content\">{{item.advice}}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 治疗建议 -->\n\t\t\t<view class=\"care-category\" v-if=\"careSuggestions.treatment && careSuggestions.treatment.length > 0\">\n\t\t\t\t<view class=\"category-header\">\n\t\t\t\t\t<view class=\"category-icon treatment-icon\">⚕️</view>\n\t\t\t\t\t<text class=\"category-title\">保健疗法</text>\n\t\t\t\t\t<view class=\"category-subtitle\">中医保健，养生调理</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"suggestion-list\">\n\t\t\t\t\t<view class=\"suggestion-item\" v-for=\"(item, index) in careSuggestions.treatment\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"item-header\">\n\t\t\t\t\t\t\t<view class=\"item-tag treatment-tag\">{{item.title}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"item-content\">{{item.advice}}</text>\n\t\t\t\t\t\t<view class=\"treatment-note\" v-if=\"item.title === '艾灸保健'\">\n\t\t\t\t\t\t\t<text class=\"note-warning\">⚠️ 注意事项：请在专业指导下进行，注意防烫伤</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 专家提醒 -->\n\t\t\t<view class=\"care-reminder\">\n\t\t\t\t<view class=\"reminder-header\">\n\t\t\t\t\t<view class=\"reminder-icon\">👨‍⚕️</view>\n\t\t\t\t\t<text class=\"reminder-title\">专家提醒</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"reminder-content\">以上建议仅供参考，具体调理方案请咨询专业医师。坚持执行，循序渐进，必有成效。</text>\n\t\t\t\t<image class=\"doctor-avatar-reminder\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 推荐商品 - 新增模块 -->\n\t\t<view class=\"recommend-products-section\" v-if=\"!!displaySettings.show_product_recommend && recommendProducts && recommendProducts.length > 0\">\n\t\t\t<view class=\"recommend-header\">\n\t\t\t\t<view class=\"recommend-icon-wrapper\">\n\t\t\t\t\t<text class=\"recommend-icon\">商品</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"recommend-title-wrapper\">\n\t\t\t\t\t<text class=\"section-title\">{{recommendTitle || '根据您的舌诊结果，为您推荐以下产品'}}</text>\n\t\t\t\t\t<text class=\"recommend-subtitle\">专业推荐，精准调理</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"products-list\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"product-item\" \n\t\t\t\t\tv-for=\"(product, index) in recommendProducts\" \n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"navigateToProduct(product)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"product-image-wrapper\">\n\t\t\t\t\t\t<image \n\t\t\t\t\t\t\tclass=\"product-image\" \n\t\t\t\t\t\t\t:src=\"product.pic || '/static/images/default-product.png'\"\n\t\t\t\t\t\t\tmode=\"aspectFill\"\n\t\t\t\t\t\t></image>\n\t\t\t\t\t\t<view class=\"product-type-badge\" :class=\"product.type === 'course' ? 'course-badge' : 'goods-badge'\">\n\t\t\t\t\t\t\t<text class=\"type-text\">{{product.type === 'course' ? '课程' : '商品'}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"product-info-wrapper\">\n\t\t\t\t\t\t<view class=\"product-title\">{{product.name}}</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"product-price-wrapper\" v-if=\"product.price || product.sell_price || product.market_price\">\n\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\n\t\t\t\t\t\t\t<text class=\"price-value\">{{product.price || product.sell_price || product.market_price || '0.00'}}</text>\n\t\t\t\t\t\t\t<text class=\"price-unit\" v-if=\"product.type === 'course'\">/课程</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<!-- 免费课程显示 -->\n\t\t\t\t\t\t<view class=\"product-price-wrapper\" v-else-if=\"product.type === 'course'\">\n\t\t\t\t\t\t\t<text class=\"free-text\">免费</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"product-reason\" v-if=\"product.recommend_reason\">\n\t\t\t\t\t\t\t<text class=\"reason-label\">推荐：</text>\n\t\t\t\t\t\t\t<text class=\"reason-text\">{{product.recommend_reason}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"product-sales\" v-if=\"product.sales\">\n\t\t\t\t\t\t\t<text class=\"sales-text\">已售{{product.sales}}件</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 购物车按钮独立放在右侧 -->\n\t\t\t\t\t<view class=\"product-cart-btn\" @click.stop=\"addToCart(product)\">\n\t\t\t\t\t\t<text class=\"iconfont icon_gouwuche cart-icon\"></text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 推荐说明 -->\n\t\t\t<view class=\"recommend-notice\">\n\t\t\t\t<view class=\"notice-header\">\n\t\t\t\t\t<view class=\"notice-icon\">i</view>\n\t\t\t\t\t<text class=\"notice-title\">推荐说明</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"notice-content\">以上推荐商品基于您的体质分析结果和健康得分，由专业团队精心挑选。建议根据实际情况选择使用，如有疑问请咨询专业医师。</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 视频推荐区域 -->\n\t\t<view class=\"video-recommend-section\" v-if=\"recommendVideos.length > 0 || true\">\n\t\t\t<view class=\"section-header\">\n\t\t\t\t<view class=\"recommend-icon-wrapper\">\n\t\t\t\t\t<text class=\"recommend-icon\">🎬</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"recommend-title-wrapper\">\n\t\t\t\t\t<text class=\"section-title\">体质调理视频推荐</text>\n\t\t\t\t\t<text class=\"recommend-subtitle\">根据您的体质特点，为您推荐专业调理视频</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 调试信息 -->\n\t\t\t<view v-if=\"recommendVideos.length === 0\" style=\"padding: 20rpx; background: #f0f0f0; margin: 20rpx 0; border-radius: 8rpx;\">\n\t\t\t\t<text style=\"color: #666; font-size: 24rpx;\">调试信息：当前推荐视频数量 = {{ recommendVideos.length }}</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"video-list\" v-if=\"recommendVideos.length > 0\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"video-item\"\n\t\t\t\t\tv-for=\"(video, index) in recommendVideos\"\n\t\t\t\t\t:key=\"video.id\"\n\t\t\t\t>\n\t\t\t\t\t<!-- 直接嵌入视频播放器 -->\n\t\t\t\t\t<view class=\"video-player-container\">\n\t\t\t\t\t\t<video\n\t\t\t\t\t\t\t:src=\"video.url\"\n\t\t\t\t\t\t\t:poster=\"video.pic\"\n\t\t\t\t\t\t\tcontrols\n\t\t\t\t\t\t\tautoplay=\"false\"\n\t\t\t\t\t\t\t:show-center-play-btn=\"true\"\n\t\t\t\t\t\t\t:show-play-btn=\"true\"\n\t\t\t\t\t\t\t:show-fullscreen-btn=\"true\"\n\t\t\t\t\t\t\t:show-progress=\"true\"\n\t\t\t\t\t\t\t:show-loading=\"true\"\n\t\t\t\t\t\t\t:enable-progress-gesture=\"true\"\n\t\t\t\t\t\t\t:object-fit=\"'contain'\"\n\t\t\t\t\t\t\t:playsinline=\"true\"\n\t\t\t\t\t\t\t:webkit-playsinline=\"true\"\n\t\t\t\t\t\t\tclass=\"video-player\"\n\t\t\t\t\t\t\t@error=\"onVideoError\"\n\t\t\t\t\t\t\t@play=\"onVideoPlay(video)\"\n\t\t\t\t\t\t\t@pause=\"onVideoPause(video)\"\n\t\t\t\t\t\t></video>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"video-info\">\n\t\t\t\t\t\t<text class=\"video-title\">{{ video.name }}</text>\n\t\t\t\t\t\t<text class=\"video-reason\" v-if=\"video.recommend_reason\">{{ video.recommend_reason }}</text>\n\t\t\t\t\t\t<view class=\"video-stats\">\n\t\t\t\t\t\t\t<text class=\"stat-item\">👁 {{ video.view_num || 0 }}次观看</text>\n\t\t\t\t\t\t\t<text class=\"stat-item\">👍 {{ video.zan_num || 0 }}点赞</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 视频推荐说明 -->\n\t\t\t<view class=\"recommend-notice\">\n\t\t\t\t<view class=\"notice-header\">\n\t\t\t\t\t<view class=\"notice-icon\">i</view>\n\t\t\t\t\t<text class=\"notice-title\">视频推荐说明</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"notice-content\">以上推荐视频基于您的体质分析结果，由专业团队精心挑选。建议根据实际情况观看学习，如有疑问请咨询专业医师。</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 视频播放器遮罩层已移除，现在视频直接在列表中播放 -->\n\n\n\t\t<!-- VIP会员专享 -->\n\t\t<!-- <view class=\"vip-section\">\n\t\t\t<view class=\"vip-header\">\n\t\t\t\t<text class=\"vip-icon\">👑</text>\n\t\t\t\t<text class=\"vip-text\">VIP会员专享</text>\n\t\t\t</view>\n\t\t\t<view class=\"vip-card\">\n\t\t\t\t<text class=\"vip-title\">广东省中医院为您专属定制</text>\n\t\t\t\t<text class=\"vip-subtitle\">健康调理方案</text>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 报告解读 -->\n\t\t<!-- <view class=\"report-section\">\n\t\t\t<view class=\"report-header\">\n\t\t\t\t<text class=\"report-title\">报告解读</text>\n\t\t\t\t<view class=\"member-badge\">会员专享</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"symptoms-summary\">\n\t\t\t\t<text class=\"summary-text\">您的主症为</text>\n\t\t\t\t<text class=\"symptom-highlight water\">水湿</text>\n\t\t\t\t<text class=\"summary-text\">，副症为</text>\n\t\t\t\t<text class=\"symptom-highlight spleen\">脾胃虚</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"main-symptom-detail\">\n\t\t\t\t<view class=\"symptom-label\">主症【水湿】：</view>\n\t\t\t\t<text class=\"symptom-description\">您体内水液代谢出现了异常，停滞在体内，也可以理解为人体新陈代谢速率变慢，导致体内的水分不能被正常的排出。</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"view-detail-btn\" @click=\"viewDetailContent\">\n\t\t\t\t<text class=\"lock-icon\">🔒</text>\n\t\t\t\t<text class=\"btn-text\">查看体征解读内容</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"doctor-section\">\n\t\t\t\t<text class=\"doctor-title\">名医为您解读体征</text>\n\t\t\t\t<image class=\"doctor-avatar\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 调理原则 -->\n\t\t<!-- <view class=\"principle-section\">\n\t\t\t<view class=\"principle-header\">\n\t\t\t\t<text class=\"principle-title\">调理原则</text>\n\t\t\t\t<view class=\"member-badge\">会员专享</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"principle-list\">\n\t\t\t\t<view class=\"principle-item\">\n\t\t\t\t\t<text class=\"principle-bullet\">•</text>\n\t\t\t\t\t<text class=\"principle-text\">日常饮食不要吃得过于油腻，油腻的食物会</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"principle-item\">\n\t\t\t\t\t<text class=\"principle-bullet\">•</text>\n\t\t\t\t\t<text class=\"principle-text\">每天积累运动要达到半小时以上，可以有效改善水湿体质，增强自己的体质。</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"view-principle-btn\" @click=\"viewPrincipleContent\">\n\t\t\t\t<text class=\"lock-icon\">🔒</text>\n\t\t\t\t<text class=\"btn-text\">查看病症调理原则</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"custom-plan-text\">\n\t\t\t\t<text>名医为您定制专属调理方案</text>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 今日营养目标 -->\n\t\t<!-- <view class=\"nutrition-section\">\n\t\t\t<view class=\"nutrition-header\">\n\t\t\t\t<text class=\"nutrition-title\">今日营养目标</text>\n\t\t\t\t<view class=\"member-badge\">会员专享</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"chart-container\">\n\t\t\t\t<view class=\"chart-left\">\n\t\t\t\t\t<view class=\"pie-chart\" :style=\"pieChartStyle\">\n\t\t\t\t\t\t<view class=\"pie-center\">\n\t\t\t\t\t\t\t<text class=\"calories-number\">{{nutritionData.totalCalories}}</text>\n\t\t\t\t\t\t\t<text class=\"calories-unit\">千卡</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"chart-right\">\n\t\t\t\t\t<view class=\"nutrition-item\" v-for=\"(item, index) in nutritionData.nutritionItems\" :key=\"index\">\n\t\t\t\t\t\t<view class=\"nutrition-color\" :style=\"{'background-color': item.color}\"></view>\n\t\t\t\t\t\t<text class=\"nutrition-name\">{{item.name}}</text>\n\t\t\t\t\t\t<text class=\"nutrition-percent\">{{item.percent}}%</text>\n\t\t\t\t\t\t<text class=\"nutrition-amount\">{{item.amount}}克</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 今日专属膳方 -->\n\t\t<!-- <view class=\"recipe-section\">\n\t\t\t<view class=\"recipe-header\">\n\t\t\t\t<text class=\"recipe-title\">今日专属膳方</text>\n\t\t\t\t<text class=\"adjust-plan\" @click=\"adjustRecipePlan\">调整膳方计划 ></text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"recipe-card\">\n\t\t\t\t<view class=\"recipe-content\">\n\t\t\t\t\t<view class=\"recipe-info\">\n\t\t\t\t\t\t<text class=\"recipe-name\">黄酒煮鸡</text>\n\t\t\t\t\t\t<text class=\"recipe-effect\">温中养血，散寒通络</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"recipe-image-container\">\n\t\t\t\t\t\t<image class=\"recipe-image\" src=\"/static/images/huangjiu-chicken.png\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"doctor-recommendation\">\n\t\t\t\t<image class=\"doctor-avatar-small\" src=\"/static/images/doctor-avatar.png\"></image>\n\t\t\t</view>\n\t\t</view> -->\n\t\t\n\t\t<!-- 底部按钮 - 浮动样式 -->\n\t\t<view class=\"bottom-buttons\">\n\t\t\t<view class=\"btn-secondary\" @click=\"goBack\">返回</view>\n\t\t\t<view class=\"btn-secondary\" @click=\"retakePhoto\">重新拍照</view>\n\t\t\t<view class=\"btn-primary\" @click=\"shareReport\">分享报告</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][data_001] 初始化页面数据\n\t\t\t\trecordId: 0,\n\t\t\t\tanalysisData: null,\n\t\t\t\tisLoading: true,\n\t\t\t\tloadingText: '分析中...',\n\t\t\t\t\n\t\t\t\t// 健康得分和体质信息\n\t\t\t\thealthScore: 89,\n\t\t\t\tconstitutionType: '湿热体质、血瘀体质',\n\t\t\t\tmainSymptom: '水湿',\n\t\t\t\tsubSymptom: '脾胃虚',\n\t\t\t\t\n\t\t\t\t// 2025-01-27 新增前端显示设置 - 控制各模块显示状态\n\t\t\t\tdisplaySettings: {\n\t\t\t\t\tshow_score: 1, // 是否显示评分，默认显示\n\t\t\t\t\tshow_score_value: 1, // 2025-01-27 是否显示评分分值，默认显示\n\t\t\t\t\tshow_symptoms: 1, // 是否显示体征，默认显示\n\t\t\t\t\tshow_tongue_analysis: 1, // 是否显示舌象分析，默认显示\n\t\t\t\t\tshow_care_advice: 1, // 是否显示调理建议，默认显示\n\t\t\t\t\tshow_product_recommend: 1 // 是否显示商品推荐，默认显示\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 舌象特征数据\n\t\t\t\ttongueFeatures: [],\n\t\t\t\tabnormalFeatures: [],\n\t\t\t\t\n\t\t\t\t// 体征症状\n\t\t\t\ttypicalSymptoms: [],\n\t\t\t\t\n\t\t\t\t// 风险评估数据\n\t\t\t\triskData: [],\n\t\t\t\t\n\t\t\t\t// 警告列表\n\t\t\t\twarningList: [],\n\t\t\t\t\n\t\t\t\t// 饮食建议\n\t\t\t\tdietSuggestions: [],\n\t\t\t\t\n\t\t\t\t// 运动建议\n\t\t\t\texerciseSuggestions: [],\n\t\t\t\t\n\t\t\t\t// 健康建议\n\t\t\t\thealthSuggestions: '',\n\t\t\t\t\n\t\t\t\t// 保养建议数据\n\t\t\t\tcareSuggestions: {},\n\t\t\t\t\n\t\t\t\t// 推荐商品数据 - 新增\n\t\t\t\trecommendProducts: [],\n\t\t\t\trecommendTitle: '',\n\n\t\t\t\t// 推荐视频数据 - 新增\n\t\t\t\trecommendVideos: [],\n\t\t\t\tshowVideoPlayer: false, // 是否显示视频播放器\n\t\t\t\tcurrentVideo: {}, // 当前播放的视频\n\t\t\t\t\n\t\t\t\t// 营养目标数据\n\t\t\t\tnutritionData: {\n\t\t\t\t\tcarbohydrate: { percent: 62, amount: 385 },\n\t\t\t\t\tprotein: { percent: 10, amount: 62 },\n\t\t\t\t\tfat: { percent: 28, amount: 77 },\n\t\t\t\t\ttotalCalories: 2484,\n\t\t\t\t\tnutritionItems: [\n\t\t\t\t\t\t{ name: '碳水化物', percent: 62, amount: 385, color: '#8884d8' },\n\t\t\t\t\t\t{ name: '蛋白质', percent: 10, amount: 62, color: '#82ca9d' },\n\t\t\t\t\t\t{ name: '脂肪', percent: 28, amount: 77, color: '#ffc658' }\n\t\t\t\t\t]\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 今日膳方数据\n\t\t\t\trecipeData: {\n\t\t\t\t\tname: '黄酒煮鸡',\n\t\t\t\t\teffect: '温中养血，散寒通络',\n\t\t\t\t\timage: '/static/images/huangjiu-chicken.png'\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 体质分析数据\n\t\t\t\tconstitutionData: {\n\t\t\t\t\ttype: '平和',\n\t\t\t\t\tposition: { x: -20, y: 15 },\n\t\t\t\t\tstatusColor: '#52c41a',\n\t\t\t\t\tstatusText: '体质改善',\n\t\t\t\t\tdescription: '通过健康天平分析得出，您的身体偏寒且正气偏虚，处于痰湿状态。',\n\t\t\t\t\texplanation: '健康天平圆点越靠近中心身体越健康，越偏离中心健康状态越严重。',\n\t\t\t\t\tnote: '注：「健康天平」由省中医院副院长杨志敏教授带领的健康辨识团队运用中医健康辨识体系，结合经络脏腑阴阳五行、气血津液等理论得出。'\n\t\t\t\t},\n\t\t\t\t\n\t\t\t\t// 圆形刻度标记数据\n\t\t\t\tscaleMarks: (() => {\n\t\t\t\t\tconst marks = [];\n\t\t\t\t\tfor (let i = 0; i < 24; i++) {\n\t\t\t\t\t\tconst angle = i * 15;\n\t\t\t\t\t\tconst isMainMark = i % 6 === 0;\n\t\t\t\t\t\tmarks.push({\n\t\t\t\t\t\t\tangle: angle,\n\t\t\t\t\t\t\topacity: isMainMark ? 0.3 : 0.1\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\treturn marks;\n\t\t\t\t})(),\n\t\t\t\t\n\t\t\t\t// 分析接口相关\n\t\t\t\tanalysisApiCompleted: false,\n\t\t\t\torderNo: '',\n\t\t\t\tapiType: '',\n\t\t\t\t\n\t\t\t\t// 2025-01-27 新增支付订单ID\n\t\t\t\torderId: '',\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][computed_001] 动态计算饼状图样式\n\t\t\tpieChartStyle() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][computed_002] 生成饼状图样式');\n\t\t\t\treturn {\n\t\t\t\t\tbackground: this.calculatePieChartGradient(this.nutritionData.nutritionItems)\n\t\t\t\t};\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][computed_003] 动态计算风险进度条样式\n\t\t\triskProgressStyles() {\n\t\t\t\treturn this.riskData.map(item => ({\n\t\t\t\t\tbackground: `conic-gradient(#ff6b6b 0% ${item.percentage}%, #f0f0f0 ${item.percentage}% 100%)`\n\t\t\t\t}));\n\t\t\t},\n\t\t\t\n\t\t\t// 体质数据\n\t\t\tconstitutionData() {\n\t\t\t\t\treturn {\n\t\t\t\t\ttype: this.constitutionType || '平和体质',\n\t\t\t\t\tposition: { x: 30, y: -20 }, // 根据体质类型调整位置\n\t\t\t\t\tstatusColor: '#ff9a9e',\n\t\t\t\t\tstatusText: '偏热',\n\t\t\t\t\tdescription: this.healthSuggestions || '您的体质整体较为平衡，建议保持良好的生活习惯。',\n\t\t\t\t\texplanation: '体质分析基于舌象特征、症状表现等多维度评估得出。',\n\t\t\t\t\tnote: '以上分析仅供参考，具体调理方案请咨询专业医师。'\n\t\t\t\t};\n\t\t\t},\n\t\t\t\n\t\t\t// 刻度标记\n\t\t\tscaleMarks() {\n\t\t\t\tconst marks = [];\n\t\t\t\tfor (let i = 0; i < 12; i++) {\n\t\t\t\t\tmarks.push({\n\t\t\t\t\t\tangle: i * 30,\n\t\t\t\t\t\topacity: i % 3 === 0 ? 1 : 0.5\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\treturn marks;\n\t\t\t},\n\t\t\t\n\t\t\t// 显示的舌象特征（优先显示异常的，最多6个）\n\t\t\tdisplayedTongueFeatures() {\n\t\t\t\tif (!this.tongueFeatures || this.tongueFeatures.length === 0) {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 优先选择异常特征\n\t\t\t\tconst abnormalFeatures = this.tongueFeatures.filter(f => f.feature_situation === '异常');\n\t\t\t\tconst normalFeatures = this.tongueFeatures.filter(f => f.feature_situation === '正常');\n\t\t\t\t\n\t\t\t\t// 最多显示6个，优先异常特征\n\t\t\t\tlet displayed = [...abnormalFeatures];\n\t\t\t\tif (displayed.length < 6) {\n\t\t\t\t\tdisplayed = displayed.concat(normalFeatures.slice(0, 6 - displayed.length));\n\t\t\t\t} else {\n\t\t\t\t\tdisplayed = displayed.slice(0, 6);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 预先计算每个特征的样式，避免在模板中调用函数\n\t\t\t\treturn displayed.map((feature, index) => {\n\t\t\t\t\t// 计算偏移样式\n\t\t\t\t\tconst offset = this.calculateOffsetForSamePosition(feature.feature_group, index, displayed.length);\n\t\t\t\t\t\n\t\t\t\t\t// 返回带有预计算样式的特征对象\n\t\t\t\t\treturn {\n\t\t\t\t\t\t...feature,\n\t\t\t\t\t\tmarkerStyle: {\n\t\t\t\t\t\t\tzIndex: (10 + index).toString(), // 确保层级正确\n\t\t\t\t\t\t\t...offset\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 返回上一页功能\n\t\t\tgoBack() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 用户点击返回');\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 查看体征解读详情功能\n\t\t\tviewDetailContent() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 用户点击查看体征解读');\n\t\t\t\t// 需要VIP权限才能查看详细内容\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '需要VIP权限',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 查看调理原则详情功能\n\t\t\tviewPrincipleContent() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 用户点击查看调理原则');\n\t\t\t\t// 需要VIP权限才能查看详细内容\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '需要VIP权限',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_001] 调整膳方计划\n\t\t\tadjustRecipePlan() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_002] 用户点击调整膳方计划');\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_003] 当前膳方:', this.recipeData);\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/recipe/adjust'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_001] 显示详细健康报告\n\t\t\tonShowHealthReport() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_002] 导航到健康详细报告');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/tongue/detail'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_003] 分享功能\n\t\t\tonShare() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_004] 用户点击分享');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '分享功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_005] 查看营养详情\n\t\t\tonViewNutritionDetail() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_006] 导航到营养详情页面');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/nutrition/detail'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_007] 查看完整调理方案\n\t\t\tonViewCompleteRegulation() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_008] 导航到调理方案页面');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/tongue/plan'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_009] 开始舌诊拍摄\n\t\t\tonStartTongueDetection() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_010] 启动舌诊拍摄功能');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/tongue/camera'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_011] 计算饼状图角度\n\t\t\tcalculatePieChartGradient(nutritionItems) {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 计算饼状图渐变');\n\t\t\t\tlet cumulativePercent = 0;\n\t\t\t\tconst segments = nutritionItems.map(item => {\n\t\t\t\t\tconst startPercent = cumulativePercent;\n\t\t\t\t\tconst endPercent = cumulativePercent + item.percent;\n\t\t\t\t\tcumulativePercent = endPercent;\n\t\t\t\t\treturn `${item.color} ${startPercent}% ${endPercent}%`;\n\t\t\t\t});\n\t\t\t\treturn `conic-gradient(${segments.join(', ')})`;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 获取舌诊分析记录\n\t\t\t * 调用后端接口获取完整的舌诊分析数据\n\t\t\t */\n\t\t\tgetAnalysisRecord() {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,002-INFO-[complete][getAnalysisRecord_001] 开始获取舌诊分析记录:', this.recordId);\n\t\t\t\t\n\t\t\t\tif (!this.recordId) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,003-ERROR-[complete][getAnalysisRecord_002] 记录ID为空');\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '参数错误，请重新扫描',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 2000);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用正确的getApp().post方法调用后端接口获取分析记录\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.post('ApiSheZhen/getRecord', {\n\t\t\t\t\tid: this.recordId\n\t\t\t\t}, (response) => {\n\t\t\t\t\tconsole.log('2025-01-06 11:45:00,004-INFO-[complete][getAnalysisRecord_003] API响应:', response);\n\t\t\t\t\t\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\tif (response && (response.status === 1 || response.code === 1)) {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,005-INFO-[complete][getAnalysisRecord_004] 获取分析记录成功');\n\t\t\t\t\t\t// 解析并设置分析数据\n\t\t\t\t\t\tthis.parseAnalysisData(response.data);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('2025-01-06 11:45:00,006-ERROR-[complete][getAnalysisRecord_005] 获取分析记录失败:', response?.msg || response?.message);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: response?.msg || response?.message || '获取分析记录失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}, 2000);\n\t\t\t\t\t}\n\t\t\t\t}, (err) => {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,007-ERROR-[complete][getAnalysisRecord_006] 获取分析记录异常:', err);\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '网络异常，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\t// 使用模拟数据进行测试\n\t\t\t\t\tthis.loadSimulateData();\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析舌诊分析数据\n\t\t\t * @param {Object} data 后端返回的分析数据\n\t\t\t */\n\t\t\tparseAnalysisData(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,008-INFO-[complete][parseAnalysisData_001] 开始解析分析数据');\n\t\t\t\tconsole.log('2025-01-06 11:45:00,008-DEBUG-[complete][parseAnalysisData_debug] 完整API数据:', JSON.stringify(data, null, 2));\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 保存原始分析数据\n\t\t\t\t\tthis.analysisData = data;\n\t\t\t\t\t\n\t\t\t\t\t// 2025-01-27 解析显示设置配置\n\t\t\t\t\tif (data.display_settings) {\n\t\t\t\t\t\tconsole.log('2025-01-27 11:45:00,008-INFO-[complete][parseAnalysisData_001] 原始显示设置数据:', JSON.stringify(data.display_settings));\n\t\t\t\t\t\tthis.displaySettings = {\n\t\t\t\t\t\t\tshow_score: data.display_settings.show_score !== undefined ? data.display_settings.show_score : 1,\n\t\t\t\t\t\t\tshow_score_value: data.display_settings.show_score_value !== undefined ? data.display_settings.show_score_value : 1, // 2025-01-27 解析评分分值显示设置\n\t\t\t\t\t\t\tshow_symptoms: data.display_settings.show_symptoms !== undefined ? data.display_settings.show_symptoms : 1,\n\t\t\t\t\t\t\tshow_tongue_analysis: data.display_settings.show_tongue_analysis !== undefined ? data.display_settings.show_tongue_analysis : 1,\n\t\t\t\t\t\t\tshow_care_advice: data.display_settings.show_care_advice !== undefined ? data.display_settings.show_care_advice : 1,\n\t\t\t\t\t\t\tshow_product_recommend: data.display_settings.show_product_recommend !== undefined ? data.display_settings.show_product_recommend : 1\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconsole.log('2025-01-27 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析后显示设置:', JSON.stringify(this.displaySettings));\n\t\t\t\t\t\tconsole.log('2025-01-27 11:45:00,010-INFO-[complete][parseAnalysisData_003] show_score_value类型和值:', typeof data.display_settings.show_score_value, data.display_settings.show_score_value);\n\t\t\t\t\t\tconsole.log('2025-01-27 11:45:00,011-INFO-[complete][parseAnalysisData_004] 最终show_score_value:', this.displaySettings.show_score_value, typeof this.displaySettings.show_score_value);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析基本信息\n\t\t\t\t\t// 优先从report的raw_report_json中获取分数和体质类型，因为constitution_score可能为\"0.00\"\n\t\t\t\t\tlet reportScore = 89; // 默认值\n\t\t\t\t\tlet reportConstitutionType = ''; // 从报告中获取的体质类型\n\t\t\t\t\tif (data.report && data.report.raw_report_json) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst reportData = JSON.parse(data.report.raw_report_json);\n\t\t\t\t\t\t\tif (reportData.score) {\n\t\t\t\t\t\t\t\treportScore = parseFloat(reportData.score);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (reportData.physique_name) {\n\t\t\t\t\t\t\t\treportConstitutionType = reportData.physique_name;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.warn('2025-01-31 WARN-[complete][parseAnalysisData_005] 解析报告JSON失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.healthScore = reportScore || parseFloat(data.constitution_score) || 89;\n\t\t\t\t\t// 优先使用报告中的体质类型，然后是API返回的体质类型，最后是默认值\n\t\t\t\t\tthis.constitutionType = reportConstitutionType || data.constitution_type || '湿热体质、血瘀体质';\n\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][parseAnalysisData_006] 解析后的基本信息:', {\n\t\t\t\t\t\thealthScore: this.healthScore,\n\t\t\t\t\t\tconstitutionType: this.constitutionType,\n\t\t\t\t\t\toriginalScore: data.constitution_score,\n\t\t\t\t\t\toriginalType: data.constitution_type,\n\t\t\t\t\t\treportScore: reportScore,\n\t\t\t\t\t\treportConstitutionType: reportConstitutionType\n\t\t\t\t\t});\n\n\t\t\t\t\t// 额外调试：检查体质类型是否包含\"未知\"\n\t\t\t\t\tif (this.constitutionType && this.constitutionType.includes('未知')) {\n\t\t\t\t\t\tconsole.warn('2025-01-31 WARN-[complete][parseAnalysisData_007] 检测到体质类型包含\"未知\":', this.constitutionType);\n\t\t\t\t\t}\n\n\t\t\t\t\t// 调试：输出原始报告数据\n\t\t\t\t\tif (data.report && data.report.raw_report_json) {\n\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][parseAnalysisData_008] 原始报告数据片段:', data.report.raw_report_json.substring(0, 500));\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析典型症状\n\t\t\t\t\tif (data.typical_symptoms) {\n\t\t\t\t\t\tthis.typicalSymptoms = data.typical_symptoms.split('；').filter(symptom => symptom.trim());\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析典型症状:', this.typicalSymptoms);\n\t\t\t\t\t} else if (data.analysis_result && data.analysis_result.typical_symptom) {\n\t\t\t\t\t\t// 处理嵌套在analysis_result中的typical_symptom字段\n\t\t\t\t\t\tthis.typicalSymptoms = data.analysis_result.typical_symptom.split('；').filter(symptom => symptom.trim());\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 从analysis_result解析典型症状:', this.typicalSymptoms);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,009-WARN-[complete][parseAnalysisData_002] 未找到典型症状数据');\n\t\t\t\t\t\tthis.typicalSymptoms = [];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析舌象特征\n\t\t\t\t\tthis.parseTongueFeatures(data);\n\t\t\t\t\t\n\t\t\t\t\t// 解析饮食建议\n\t\t\t\t\tthis.parseDietSuggestions(data);\n\t\t\t\t\t\n\t\t\t\t\t// 解析运动建议\n\t\t\t\t\tthis.parseExerciseSuggestions(data);\n\t\t\t\t\t\n\t\t\t\t\t// 解析健康建议\n\t\t\t\t\tthis.healthSuggestions = data.health_suggestions || data.constitution_analysis || '';\n\t\t\t\t\t\n\t\t\t\t\t// 解析风险评估\n\t\t\t\t\tthis.parseRiskAssessment(data);\n\t\t\t\t\t\n\t\t\t\t\t// 解析保养建议数据（新增）\n\t\t\t\t\tthis.parseCareSuggestions(data);\n\t\t\t\t\t\n\t\t\t\t\t// 解析推荐商品数据（新增）\n\t\t\t\t\tthis.parseRecommendProducts(data);\n\n\t\t\t\t\t// 获取推荐视频（新增）\n\t\t\t\t\tthis.getRecommendVideos();\n\n\t\t\t\t\t// 解析报告数据\n\t\t\t\t\tif (data.report && data.report.raw_report_json) {\n\t\t\t\t\t\tthis.parseReportData(data.report.raw_report_json);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('2025-01-06 11:45:00,010-INFO-[complete][parseAnalysisData_003] 分析数据解析完成');\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,011-ERROR-[complete][parseAnalysisData_004] 解析分析数据异常:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '数据解析失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析舌象特征数据\n\t\t\t */\n\t\t\tparseTongueFeatures(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,012-INFO-[complete][parseTongueFeatures_001] 解析舌象特征');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tif (data.tongue_features) {\n\t\t\t\t\t\tconst features = typeof data.tongue_features === 'string' \n\t\t\t\t\t\t\t? JSON.parse(data.tongue_features) \n\t\t\t\t\t\t\t: data.tongue_features;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (Array.isArray(features)) {\n\t\t\t\t\t\t\tthis.tongueFeatures = features;\n\t\t\t\t\t\t\t// 提取异常特征\n\t\t\t\t\t\t\tthis.abnormalFeatures = features.filter(feature => \n\t\t\t\t\t\t\t\tfeature.situation === '异常' && feature.interpret\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,013-INFO-[complete][parseTongueFeatures_002] 异常特征:', this.abnormalFeatures.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,014-ERROR-[complete][parseTongueFeatures_003] 解析舌象特征失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析饮食建议\n\t\t\t */\n\t\t\tparseDietSuggestions(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,015-INFO-[complete][parseDietSuggestions_001] 解析饮食建议');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tif (data.diet_suggestions) {\n\t\t\t\t\t\tconst suggestions = typeof data.diet_suggestions === 'string' \n\t\t\t\t\t\t\t? JSON.parse(data.diet_suggestions) \n\t\t\t\t\t\t\t: data.diet_suggestions;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (Array.isArray(suggestions)) {\n\t\t\t\t\t\t\tthis.dietSuggestions = suggestions;\n\t\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,016-INFO-[complete][parseDietSuggestions_002] 饮食建议数量:', suggestions.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,017-ERROR-[complete][parseDietSuggestions_003] 解析饮食建议失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析运动建议\n\t\t\t */\n\t\t\tparseExerciseSuggestions(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,018-INFO-[complete][parseExerciseSuggestions_001] 解析运动建议');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tif (data.exercise_suggestions) {\n\t\t\t\t\t\tconst suggestions = typeof data.exercise_suggestions === 'string' \n\t\t\t\t\t\t\t? JSON.parse(data.exercise_suggestions) \n\t\t\t\t\t\t\t: data.exercise_suggestions;\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (Array.isArray(suggestions)) {\n\t\t\t\t\t\t\tthis.exerciseSuggestions = suggestions;\n\t\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,019-INFO-[complete][parseExerciseSuggestions_002] 运动建议数量:', suggestions.length);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,020-ERROR-[complete][parseExerciseSuggestions_003] 解析运动建议失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析风险评估数据\n\t\t\t */\n\t\t\tparseRiskAssessment(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,021-INFO-[complete][parseRiskAssessment_001] 解析风险评估');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 从风险评估文本中提取风险项目\n\t\t\t\t\tif (data.risk_assessment) {\n\t\t\t\t\t\tconst riskText = data.risk_assessment;\n\t\t\t\t\t\tconst riskLines = riskText.split('\\n').filter(line => line.includes('：'));\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.riskData = riskLines.map((line, index) => {\n\t\t\t\t\t\t\tconst name = line.split('：')[0].replace(/^\\d+\\./, '').trim();\n\t\t\t\t\t\t\t// 根据内容严重程度估算风险百分比\n\t\t\t\t\t\t\tconst severity = this.calculateRiskPercentage(line);\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tname: name,\n\t\t\t\t\t\t\t\tpercentage: severity\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}).slice(0, 6); // 最多显示6个风险项\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,022-INFO-[complete][parseRiskAssessment_002] 风险数据:', this.riskData.length);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,023-ERROR-[complete][parseRiskAssessment_003] 解析风险评估失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 计算风险百分比\n\t\t\t */\n\t\t\tcalculateRiskPercentage(riskText) {\n\t\t\t\t// 根据关键词估算风险程度\n\t\t\t\tconst highRiskKeywords = ['严重', '高风险', '心血管', '糖尿病'];\n\t\t\t\tconst mediumRiskKeywords = ['可能', '容易', '影响'];\n\t\t\t\tconst lowRiskKeywords = ['轻微', '注意'];\n\t\t\t\t\n\t\t\t\tlet percentage = 15; // 基础风险值\n\t\t\t\t\n\t\t\t\tif (highRiskKeywords.some(keyword => riskText.includes(keyword))) {\n\t\t\t\t\tpercentage += 25;\n\t\t\t\t} else if (mediumRiskKeywords.some(keyword => riskText.includes(keyword))) {\n\t\t\t\t\tpercentage += 15;\n\t\t\t\t} else if (lowRiskKeywords.some(keyword => riskText.includes(keyword))) {\n\t\t\t\t\tpercentage += 5;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn Math.min(percentage, 60); // 最高不超过60%\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析报告JSON数据\n\t\t\t */\n\t\t\tparseReportData(rawReportJson) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 解析报告JSON数据');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst reportData = typeof rawReportJson === 'string' \n\t\t\t\t\t\t? JSON.parse(rawReportJson) \n\t\t\t\t\t\t: rawReportJson;\n\t\t\t\t\t\n\t\t\t\t\t// 解析典型症状（优先从reportData中获取）\n\t\t\t\t\tif (reportData.typical_symptom && this.typicalSymptoms.length === 0) {\n\t\t\t\t\t\tthis.typicalSymptoms = reportData.typical_symptom.split('；').filter(symptom => symptom.trim());\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 从报告数据解析典型症状:', this.typicalSymptoms);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析更详细的特征数据\n\t\t\t\t\tif (reportData.features && Array.isArray(reportData.features)) {\n\t\t\t\t\t\tthis.tongueFeatures = reportData.features;\n\t\t\t\t\t\tthis.abnormalFeatures = reportData.features.filter(feature => \n\t\t\t\t\t\t\tfeature.feature_situation === '异常'\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 解析建议数据\n\t\t\t\t\tif (reportData.advices) {\n\t\t\t\t\t\t// 保存完整的保养建议数据\n\t\t\t\t\t\tthis.careSuggestions = reportData.advices;\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 解析保养建议:', this.careSuggestions);\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (reportData.advices.food) {\n\t\t\t\t\t\t\tthis.dietSuggestions = reportData.advices.food;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (reportData.advices.sport) {\n\t\t\t\t\t\t\tthis.exerciseSuggestions = reportData.advices.sport;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 报告数据解析完成');\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,026-ERROR-[complete][parseReportData_003] 解析报告数据失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 加载模拟数据用于测试\n\t\t\t */\n\t\t\tloadSimulateData() {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,027-INFO-[complete][loadSimulateData_001] 加载模拟保养建议数据');\n\t\t\t\t\n\t\t\t\t// 模拟保养建议数据\n\t\t\t\tthis.careSuggestions = {\n\t\t\t\t\tfood: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"避免高盐、高糖、高脂、火锅、烧烤等饮食。\",\n\t\t\t\t\t\t\ttitle: \"禁忌饮食\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"宜食性平之物，少食寒凉之物，如螃蟹、西瓜、苦瓜、冬瓜、梨、绿豆、冷饮等。\",\n\t\t\t\t\t\t\ttitle: \"建议饮食\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tmusic: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"音乐疗法：道教音乐崇尚'中和'的审美特征，具体来说体现为'阴、阳调和'、'动、静结合'和'散、正相间'等方面，常听这类曲目能让体内脏腑、气血平衡，使人心情愉快，精神饱满；改善睡眠；增强抗压能力等。其代表性曲目有：《啸咏朱陵府》《卫灵咒》《华夏颂》等。\",\n\t\t\t\t\t\t\ttitle: \"音乐建议\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tsleep: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"注意休息，劳逸结合，不可过劳，顺应四时，遵循春生、夏长、秋收、冬藏的规律，春夏应夜卧早起，秋季应早卧早起，冬季应早卧晚起。\",\n\t\t\t\t\t\t\ttitle: \"生活建议\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"保持情志舒畅，减少思虑，多与人沟通交流。\",\n\t\t\t\t\t\t\ttitle: \"情志建议\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"病室宜安静、舒适，有良好的通风环境。\",\n\t\t\t\t\t\t\ttitle: \"居住环境建议\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\tsport: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"避免久坐、久站、久卧。成人可适当于上午时刻（避寒冷、避炎热）参与如舞蹈、气功、太极拳、八段锦、五禽戏等运动，强度宜低强度，一周2-3次为宜，每次30-40分钟，以自我感觉不过度劳累为主。\",\n\t\t\t\t\t\t\ttitle: \"运动建议\"\n\t\t\t\t\t\t}\n\t\t\t\t\t],\n\t\t\t\t\ttreatment: [\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"可做保健性艾灸。先将艾条点燃，放在灸盒中的铁纱上，并将温灸盒置于关元穴上方，盖好封盖以调节温度。每次灸20~30分钟。每日1次，7~10次为1个疗程。注意预防烫伤。\",\n\t\t\t\t\t\t\ttitle: \"艾灸保健\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"可做保健性耳穴疗法。取神门、心、脾、颈椎、肩、颈、等耳穴。将耳穴消毒，在耳穴上贴王不留行籽或耳穴压丸，用拇、食指进行垂直按压，施压至患出现沉、重、胀、痛感。每穴按压1分钟左右。每穴重复操作2~3遍，每天3~5次。双侧耳穴轮流使用，2日1次替换。\",\n\t\t\t\t\t\t\ttitle: \"耳穴疗法\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"可用六字诀进行呼吸训练以达到保健效果。六字诀是一种吐纳法。它是通过呬、呵、呼、嘘、吹、嘻六个字的不同发音口型，唇齿喉舌的用力不同，以牵动不同的脏腑经络气血的运行。\\n方法：首先预备姿势，两足开立，与肩同宽，头正颈直，含胸拔背，松腰松胯，双膝微屈，全身放松，呼吸自然。\\n其次联系呼吸，顺腹式呼吸，先呼后吸，呼所时读字，同时提肛缩肾，体重移至足跟。\\n最后调息， 每个字读六遍后，调息一次，以稍事休息，恢复自然。\",\n\t\t\t\t\t\t\ttitle: \"功法导引\"\n\t\t\t\t\t\t},\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tadvice: \"可行叩齿保健法以达到健脾益胃，纳气补肾的效果，古人认为齿健则身健，身健则长寿。方法：口唇轻闭，首先，上下门牙齿叩击9次，然后左侧上下牙齿叩击9次，右侧上下齿叩击9次，最后上下门齿再叩击9次。每日早晚各一次，每次3分钟左右。叩齿时可用双手指有节律地搓双侧耳孔，提拉双耳廓直到发热为止。\",\n\t\t\t\t\t\t\ttitle: \"叩齿保健法\"\n\t\t\t\t\t\t}\n\t\t\t\t\t]\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconsole.log('2025-01-06 11:45:00,028-INFO-[complete][loadSimulateData_002] 模拟数据加载完成');\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_012] 重新拍照功能\n\t\t\tretakePhoto() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 用户点击重新拍照');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pagesB/shezhen/guide'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_013] 分享报告功能\n\t\t\tshareReport() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_013] 用户点击分享报告');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '分享功能开发中',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t// 2025-01-03 22:55:53,565-INF0-[complete][method_014] 查看舌象图片\n\t\t\tviewTongueImage() {\n\t\t\t\tconsole.log('2025-01-03 22:55:53,565-INF0-[complete][method_014] 用户点击查看舌象图片');\n\t\t\t\t// 实现查看舌象图片的功能\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/tongue/image'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 根据舌象特征组获取标记样式\n\t\t\t * @param {String} featureGroup 特征组名称\n\t\t\t * @returns {Object} 包含定位样式的对象\n\t\t\t */\n\t\t\tgetMarkerStyle(featureGroup) {\n\t\t\t\tconst positions = {\n\t\t\t\t\t// 舌根区域（现在在上方）\n\t\t\t\t\t'舌根部': { top: '15%', left: '50%' },\n\t\t\t\t\t\n\t\t\t\t\t// 舌质相关 - 分布在舌体中部\n\t\t\t\t\t'舌质': { top: '35%', left: '50%' },\n\t\t\t\t\t'舌形胖瘦': { top: '45%', left: '50%' },\n\t\t\t\t\t'舌中央': { top: '50%', left: '50%' },\n\t\t\t\t\t\n\t\t\t\t\t// 舌两侧 - 更靠边缘\n\t\t\t\t\t'舌齿痕': { top: '45%', left: '8%' },\n\t\t\t\t\t'舌两侧': { top: '40%', left: '5%' },\n\t\t\t\t\t'舌瘀斑瘀点': { top: '50%', left: '92%' },\n\t\t\t\t\t\n\t\t\t\t\t// 舌面特征 - 分散在不同位置\n\t\t\t\t\t'舌裂纹': { top: '42%', left: '78%' },\n\t\t\t\t\t'舌点刺': { top: '38%', left: '22%' },\n\t\t\t\t\t\n\t\t\t\t\t// 舌苔相关 - 主要分布在中上部\n\t\t\t\t\t'苔色': { top: '40%', left: '50%' },\n\t\t\t\t\t'舌苔腻': { top: '35%', left: '30%' },\n\t\t\t\t\t'舌苔腐': { top: '35%', left: '70%' },\n\t\t\t\t\t'舌苔厚薄': { top: '38%', left: '35%' },\n\t\t\t\t\t'舌苔润燥': { top: '38%', left: '65%' },\n\t\t\t\t\t'舌苔剥脱': { top: '32%', left: '50%' },\n\t\t\t\t\t\n\t\t\t\t\t// 舌尖区域（现在在下方）\n\t\t\t\t\t'舌色': { top: '75%', left: '50%' },\n\t\t\t\t\t'舌尖色': { top: '85%', left: '50%' },\n\t\t\t\t\t'舌尖': { top: '90%', left: '50%' }\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 获取基础位置\n\t\t\t\tconst basePosition = positions[featureGroup] || { top: '50%', left: '50%' };\n\t\t\t\t\n\t\t\t\t// 小程序环境下使用不同的定位策略\n\t\t\t\tif (this.isMiniProgram) {\n\t\t\t\t\t// 转换百分比为rpx值，基于600rpx宽度和480rpx高度的容器\n\t\t\t\t\tconst topRpx = Math.round((parseFloat(basePosition.top) / 100) * 480);\n\t\t\t\t\tconst leftRpx = Math.round((parseFloat(basePosition.left) / 100) * 600);\n\t\t\t\t\t\n\t\t\t\t\treturn {\n\t\t\t\t\t\tposition: 'absolute',\n\t\t\t\t\t\ttop: topRpx + 'rpx',\n\t\t\t\t\t\tleft: leftRpx + 'rpx',\n\t\t\t\t\t\tmarginTop: '-30rpx', // marker高度的一半\n\t\t\t\t\t\tmarginLeft: '-50rpx', // marker宽度的一半\n\t\t\t\t\t\tzIndex: 10\n\t\t\t\t\t};\n\t\t\t\t} else {\n\t\t\t\t\t// 浏览器环境保持原有样式\n\t\t\t\treturn {\n\t\t\t\t\tposition: 'absolute',\n\t\t\t\t\ttop: basePosition.top,\n\t\t\t\t\tleft: basePosition.left,\n\t\t\t\t\t\ttransform: 'translate(-50%, -50%)',\n\t\t\t\t\t\tzIndex: 10\n\t\t\t\t};\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析保养建议数据\n\t\t\t * @param {Object} data 后端返回的分析数据\n\t\t\t */\n\t\t\tparseCareSuggestions(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,029-INFO-[complete][parseCareSuggestions_001] 解析保养建议数据');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 直接从数据中获取advices字段\n\t\t\t\t\tif (data.advices) {\n\t\t\t\t\t\tthis.careSuggestions = data.advices;\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,030-INFO-[complete][parseCareSuggestions_002] 保养建议数据解析成功:', this.careSuggestions);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 解析各类建议到原有字段（兼容性）\n\t\t\t\t\t\tif (data.advices.food) {\n\t\t\t\t\t\t\tthis.dietSuggestions = data.advices.food;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (data.advices.sport) {\n\t\t\t\t\t\t\tthis.exerciseSuggestions = data.advices.sport;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有advices字段，尝试从其他字段构建\n\t\t\t\t\tlet suggestions = {};\n\t\t\t\t\t\n\t\t\t\t\t// 检查是否有字符串形式的建议数据\n\t\t\t\t\tif (data.diet_suggestions) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst dietData = typeof data.diet_suggestions === 'string' \n\t\t\t\t\t\t\t\t? JSON.parse(data.diet_suggestions) \n\t\t\t\t\t\t\t\t: data.diet_suggestions;\n\t\t\t\t\t\t\tif (Array.isArray(dietData)) {\n\t\t\t\t\t\t\t\tsuggestions.food = dietData;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('2025-01-06 11:45:00,031-ERROR-[complete][parseCareSuggestions_003] 解析饮食建议失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tif (data.exercise_suggestions) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst exerciseData = typeof data.exercise_suggestions === 'string' \n\t\t\t\t\t\t\t\t? JSON.parse(data.exercise_suggestions) \n\t\t\t\t\t\t\t\t: data.exercise_suggestions;\n\t\t\t\t\t\t\tif (Array.isArray(exerciseData)) {\n\t\t\t\t\t\t\t\tsuggestions.sport = exerciseData;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\tconsole.error('2025-01-06 11:45:00,032-ERROR-[complete][parseCareSuggestions_004] 解析运动建议失败:', e);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果构建了建议数据，设置到careSuggestions\n\t\t\t\t\tif (Object.keys(suggestions).length > 0) {\n\t\t\t\t\t\tthis.careSuggestions = suggestions;\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,033-INFO-[complete][parseCareSuggestions_005] 从其他字段构建保养建议:', this.careSuggestions);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,034-WARN-[complete][parseCareSuggestions_006] 未找到保养建议数据');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,035-ERROR-[complete][parseCareSuggestions_007] 解析保养建议异常:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 获取优化的标记样式，避免重叠\n\t\t\t * @param {String} featureGroup 特征组名称\n\t\t\t * @param {Number} index 当前索引\n\t\t\t * @param {Number} total 总数量\n\t\t\t * @returns {Object} 包含定位样式的对象\n\t\t\t */\n\t\t\tgetOptimizedMarkerStyle(featureGroup, index, total) {\n\t\t\t\t// 先获取基础样式\n\t\t\t\tconst baseStyle = this.getMarkerStyle(featureGroup);\n\t\t\t\t\n\t\t\t\t// 如果是小程序环境且有多个标签，需要避免重叠\n\t\t\t\tif (this.isMiniProgram && total > 1) {\n\t\t\t\t\t// 如果多个特征位置相同，进行偏移处理\n\t\t\t\t\tconst samePositionOffset = this.calculateOffsetForSamePosition(featureGroup, index, total);\n\t\t\t\t\tif (samePositionOffset) {\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t...baseStyle,\n\t\t\t\t\t\t\ttop: `calc(${baseStyle.top} + ${samePositionOffset.top}rpx)`,\n\t\t\t\t\t\t\tleft: `calc(${baseStyle.left} + ${samePositionOffset.left}rpx)`\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn baseStyle;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 计算相同位置标签的偏移量\n\t\t\t * @param {String} featureGroup 特征组名称\n\t\t\t * @param {Number} index 当前索引\n\t\t\t * @param {Number} total 总数量\n\t\t\t * @returns {Object|null} 偏移量对象\n\t\t\t */\n\t\t\tcalculateOffsetForSamePosition(featureGroup, index, total) {\n\t\t\t\t// 定义容易重叠的特征组\n\t\t\t\tconst centerGroups = ['舌质', '舌形胖瘦', '舌中央', '苔色'];\n\t\t\t\tconst leftGroups = ['舌齿痕', '舌两侧'];\n\t\t\t\tconst rightGroups = ['舌瘀斑瘀点', '舌裂纹'];\n\t\t\t\t\n\t\t\t\tif (centerGroups.includes(featureGroup)) {\n\t\t\t\t\t// 中心区域的标签呈环形分布，但半径更小，更紧凑\n\t\t\t\t\tconst angle = (index * 360 / total) * Math.PI / 180;\n\t\t\t\t\tconst radius = this.isMiniProgram ? 25 : 20; // 减小偏移半径\n\t\t\t\t\treturn {\n\t\t\t\t\t\tmarginTop: (Math.sin(angle) * radius) + (this.isMiniProgram ? 'rpx' : 'px'),\n\t\t\t\t\t\tmarginLeft: (Math.cos(angle) * radius) + (this.isMiniProgram ? 'rpx' : 'px')\n\t\t\t\t\t};\n\t\t\t\t} else if (leftGroups.includes(featureGroup)) {\n\t\t\t\t\t// 左侧标签垂直分布，间距更小\n\t\t\t\t\tconst offset = (index - total / 2) * 30; // 减小间隔\n\t\t\t\t\treturn {\n\t\t\t\t\t\tmarginTop: offset + (this.isMiniProgram ? 'rpx' : 'px')\n\t\t\t\t\t};\n\t\t\t\t} else if (rightGroups.includes(featureGroup)) {\n\t\t\t\t\t// 右侧标签垂直分布，间距更小\n\t\t\t\t\tconst offset = (index - total / 2) * 30; // 减小间隔\n\t\t\t\t\treturn {\n\t\t\t\t\t\tmarginTop: offset + (this.isMiniProgram ? 'rpx' : 'px')\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn {};\n\t\t\t},\n\t\t\tgetFeaturePositionClass(featureGroup) {\n\t\t\t\tconst positionClasses = {\n\t\t\t\t\t'舌根部': 'root',\n\t\t\t\t\t'舌质': 'tongue',\n\t\t\t\t\t'舌形胖瘦': 'thickness',\n\t\t\t\t\t'舌中央': 'center',\n\t\t\t\t\t'舌齿痕': 'indent',\n\t\t\t\t\t'舌两侧': 'sides',\n\t\t\t\t\t'舌瘀斑瘀点': 'spots',\n\t\t\t\t\t'舌裂纹': 'cracks',\n\t\t\t\t\t'舌点刺': 'punctures',\n\t\t\t\t\t'苔色': 'mucus',\n\t\t\t\t\t'舌苔腻': 'thick',\n\t\t\t\t\t'舌苔腐': 'decay',\n\t\t\t\t\t'舌苔厚薄': 'thickness',\n\t\t\t\t\t'舌苔润燥': 'moisture',\n\t\t\t\t\t'舌苔剥脱': 'peeling',\n\t\t\t\t\t'舌色': 'color',\n\t\t\t\t\t'舌尖色': 'tip',\n\t\t\t\t\t'舌尖': 'tip'\n\t\t\t\t};\n\t\t\t\tconst className = positionClasses[featureGroup] || '';\n\t\t\t\tconsole.log(`2025-01-06 12:30:00,001-INFO-[complete][getFeaturePositionClass] 特征组: ${featureGroup} -> CSS类: marker-${className}`);\n\t\t\t\treturn className;\n\t\t\t},\n\t\t\tgetSimpleMarkerStyle(featureGroup, index) {\n\t\t\t\t// 现在主要通过CSS类定位，只需要简单的样式补充\n\t\t\t\tconst offset = this.calculateOffsetForSamePosition(featureGroup, index, this.displayedTongueFeatures.length);\n\t\t\t\t\n\t\t\t\tconst style = {\n\t\t\t\t\tzIndex: 10 + index, // 确保层级正确\n\t\t\t\t\t...offset\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconsole.log(`2025-01-06 12:30:00,002-INFO-[complete][getSimpleMarkerStyle] 特征组: ${featureGroup}, 索引: ${index}, 样式:`, style);\n\t\t\t\t\n\t\t\t\t// 只返回偏移样式，基础定位由CSS类处理\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 解析推荐商品数据 - 新增方法\n\t\t\t * @param {Object} data 后端返回的分析数据\n\t\t\t */\n\t\t\tparseRecommendProducts(data) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,036-INFO-[complete][parseRecommendProducts_001] 解析推荐商品数据');\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\t// 从 recommend_products 字段解析推荐商品\n\t\t\t\t\tif (data.recommend_products && Array.isArray(data.recommend_products)) {\n\t\t\t\t\t\tthis.recommendProducts = data.recommend_products;\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,037-INFO-[complete][parseRecommendProducts_002] 推荐商品数据解析成功:', this.recommendProducts.length, '个商品');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 获取推荐标题\n\t\t\t\t\tif (data.recommend_title) {\n\t\t\t\t\t\tthis.recommendTitle = data.recommend_title;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 如果没有推荐商品，尝试调用独立的推荐商品接口\n\t\t\t\t\tif ((!this.recommendProducts || this.recommendProducts.length === 0) && this.constitutionType && this.healthScore) {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,038-INFO-[complete][parseRecommendProducts_003] 数据中无推荐商品，调用独立接口获取');\n\t\t\t\t\t\tthis.getRecommendProductsFromApi();\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,039-ERROR-[complete][parseRecommendProducts_004] 解析推荐商品异常:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 从API获取推荐商品 - 新增方法\n\t\t\t */\n\t\t\tgetRecommendProductsFromApi() {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,040-INFO-[complete][getRecommendProductsFromApi_001] 开始从API获取推荐商品');\n\t\t\t\t\n\t\t\t\tconst app = getApp();\n\t\t\t\tapp.post('ApiSheZhen/getRecommendProducts', {\n\t\t\t\t\tconstitution_type: this.constitutionType,\n\t\t\t\t\tconstitution_score: this.healthScore\n\t\t\t\t}, (response) => {\n\t\t\t\t\tconsole.log('2025-01-06 11:45:00,041-INFO-[complete][getRecommendProductsFromApi_002] 推荐商品API响应:', response);\n\t\t\t\t\t\n\t\t\t\t\tif (response && (response.status === 1 || response.code === 1)) {\n\t\t\t\t\t\tconst recommendData = response.data;\n\t\t\t\t\t\tif (recommendData && recommendData.products && Array.isArray(recommendData.products)) {\n\t\t\t\t\t\t\tthis.recommendProducts = recommendData.products;\n\t\t\t\t\t\t\tif (recommendData.recommend_title) {\n\t\t\t\t\t\t\t\tthis.recommendTitle = recommendData.recommend_title;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,042-INFO-[complete][getRecommendProductsFromApi_003] 成功获取推荐商品:', this.recommendProducts.length, '个');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,043-WARN-[complete][getRecommendProductsFromApi_004] 获取推荐商品失败:', response?.msg);\n\t\t\t\t\t}\n\t\t\t\t}, (err) => {\n\t\t\t\t\tconsole.error('2025-01-06 11:45:00,044-ERROR-[complete][getRecommendProductsFromApi_005] 获取推荐商品异常:', err);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取推荐视频 - 新增方法\n\t\t\t */\n\t\t\tgetRecommendVideos() {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_001] 开始获取推荐视频');\n\n\t\t\t\t// 获取体质类型和得分\n\t\t\t\tlet constitutionType = this.constitutionType || '';\n\t\t\t\tlet constitutionScore = this.healthScore || 0;\n\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_001.5] 体质信息:', {\n\t\t\t\t\tconstitutionType: constitutionType,\n\t\t\t\t\tconstitutionScore: constitutionScore\n\t\t\t\t});\n\n\t\t\t\t// 如果没有体质信息，不获取推荐视频\n\t\t\t\tif (!constitutionType && !constitutionScore) {\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_002] 无体质信息，跳过视频推荐');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst app = getApp();\n\n\t\t\t\t// 调用推荐视频接口\n\t\t\t\tapp.post('ApiSheZhen/getRecommendProducts', {\n\t\t\t\t\tconstitution_type: constitutionType,\n\t\t\t\t\tconstitution_score: constitutionScore\n\t\t\t\t}, (response) => {\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_003] 获取推荐视频结果:', response);\n\n\t\t\t\t\t// 兼容多种响应格式\n\t\t\t\t\tif (response && (response.code === 1 || response.status === 1) && response.data && response.data.products) {\n\t\t\t\t\t\t// 筛选出视频类型的推荐\n\t\t\t\t\t\tconst videos = response.data.products.filter(item => item.type === 'video');\n\t\t\t\t\t\tthis.recommendVideos = videos;\n\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);\n\n\t\t\t\t\t\t// 如果有视频数据，输出详细信息\n\t\t\t\t\t\tif (videos.length > 0) {\n\t\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_004.5] 视频详情:', videos);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_005] 无推荐视频数据，响应详情:', {\n\t\t\t\t\t\t\thasResponse: !!response,\n\t\t\t\t\t\t\tcode: response?.code,\n\t\t\t\t\t\t\tstatus: response?.status,\n\t\t\t\t\t\t\thasData: !!response?.data,\n\t\t\t\t\t\t\thasProducts: !!response?.data?.products,\n\t\t\t\t\t\t\tproductsLength: response?.data?.products?.length\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 临时添加测试数据，方便调试\n\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_005.5] 添加测试视频数据');\n\t\t\t\t\t\tthis.recommendVideos = [{\n\t\t\t\t\t\t\tid: 999,\n\t\t\t\t\t\t\tname: '测试调理视频',\n\t\t\t\t\t\t\ttype: 'video',\n\t\t\t\t\t\t\turl: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',\n\t\t\t\t\t\t\tpic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',\n\t\t\t\t\t\t\trecommend_reason: '适合您的体质类型',\n\t\t\t\t\t\t\tview_num: 1000,\n\t\t\t\t\t\t\tzan_num: 50\n\t\t\t\t\t\t}];\n\t\t\t\t\t}\n\t\t\t\t}, (error) => {\n\t\t\t\t\tconsole.error('2025-01-31 ERROR-[complete][getRecommendVideos_006] 获取推荐视频失败:', error);\n\n\t\t\t\t\t// 出错时也添加测试数据\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][getRecommendVideos_006.5] 接口出错，添加测试视频数据');\n\t\t\t\t\tthis.recommendVideos = [{\n\t\t\t\t\t\tid: 998,\n\t\t\t\t\t\tname: '错误测试视频',\n\t\t\t\t\t\ttype: 'video',\n\t\t\t\t\t\turl: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',\n\t\t\t\t\t\tpic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',\n\t\t\t\t\t\trecommend_reason: '测试推荐理由',\n\t\t\t\t\t\tview_num: 500,\n\t\t\t\t\t\tzan_num: 25\n\t\t\t\t\t}];\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 播放视频 - 新增方法\n\t\t\t */\n\t\t\tplayVideo(video) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][playVideo_001] 播放视频:', video);\n\n\t\t\t\tif (!video.url) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '视频地址无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 处理视频URL，确保中文字符正确编码\n\t\t\t\tlet processedVideo = { ...video };\n\t\t\t\tif (video.url && video.url.includes('%')) {\n\t\t\t\t\t// URL已经编码，直接使用\n\t\t\t\t\tprocessedVideo.url = video.url;\n\t\t\t\t} else if (video.url) {\n\t\t\t\t\t// 对URL中的中文字符进行编码\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst urlParts = video.url.split('/');\n\t\t\t\t\t\tconst encodedParts = urlParts.map(part => {\n\t\t\t\t\t\t\t// 只对文件名部分进行编码，保留协议和域名\n\t\t\t\t\t\t\tif (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {\n\t\t\t\t\t\t\t\treturn encodeURIComponent(part);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn part;\n\t\t\t\t\t\t});\n\t\t\t\t\t\tprocessedVideo.url = encodedParts.join('/');\n\t\t\t\t\t\tconsole.log('2025-01-31 INFO-[complete][playVideo_001.5] URL编码处理:', {\n\t\t\t\t\t\t\toriginal: video.url,\n\t\t\t\t\t\t\tprocessed: processedVideo.url\n\t\t\t\t\t\t});\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tconsole.warn('2025-01-31 WARN-[complete][playVideo_001.6] URL编码失败，使用原始URL:', e);\n\t\t\t\t\t\tprocessedVideo.url = video.url;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 设置当前播放视频并显示播放器\n\t\t\t\tthis.currentVideo = processedVideo;\n\t\t\t\tthis.showVideoPlayer = true;\n\n\t\t\t\t// 记录视频播放事件\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][playVideo_002] 开始播放视频:', video.name);\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][playVideo_003] 视频URL:', processedVideo.url);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 关闭视频播放器 - 新增方法\n\t\t\t */\n\t\t\tcloseVideoPlayer() {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][closeVideoPlayer_001] 关闭视频播放器');\n\t\t\t\tthis.showVideoPlayer = false;\n\t\t\t\tthis.currentVideo = {};\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频播放错误处理 - 新增方法\n\t\t\t */\n\t\t\tonVideoError(e) {\n\t\t\t\tconsole.error('2025-01-31 ERROR-[complete][onVideoError_001] 视频播放错误:', e);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '视频播放失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频开始播放事件 - 新增方法\n\t\t\t */\n\t\t\tonVideoPlay(video) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', video.name);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频暂停事件 - 新增方法\n\t\t\t */\n\t\t\tonVideoPause(video) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停:', video.name);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频开始播放 - 新增方法\n\t\t\t */\n\t\t\tonVideoPlay(e) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', e);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频暂停播放 - 新增方法\n\t\t\t */\n\t\t\tonVideoPause(e) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停播放:', e);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频开始加载 - 新增方法\n\t\t\t */\n\t\t\tonVideoLoadStart(e) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoLoadStart_001] 视频开始加载:', e);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 视频可以播放 - 新增方法\n\t\t\t */\n\t\t\tonVideoCanPlay(e) {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onVideoCanPlay_001] 视频可以播放:', e);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取推荐徽章文本 - 新增方法\n\t\t\t */\n\t\t\tgetRecommendBadge(reason) {\n\t\t\t\tif (!reason) return '';\n\t\t\t\t\n\t\t\t\tif (reason.includes('体质匹配')) return '体质';\n\t\t\t\tif (reason.includes('得分推荐')) return '得分';\n\t\t\t\tif (reason.includes('热销')) return '热销';\n\t\t\t\tif (reason.includes('专业')) return '专业';\n\t\t\t\treturn '推荐';\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 查看商品详情 - 新增方法\n\t\t\t */\n\t\t\tviewProductDetail(product) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,045-INFO-[complete][viewProductDetail_001] 查看商品详情:', product);\n\t\t\t\t\n\t\t\t\t// 根据商品类型跳转到对应的详情页面\n\t\t\t\tif (product.product_type === 2) {\n\t\t\t\t\t// 课程详情\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/course/detail?id=${product.id}`\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 商品详情\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/product/detail?id=${product.id}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 购买商品 - 新增方法\n\t\t\t */\n\t\t\tbuyProduct(product) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,046-INFO-[complete][buyProduct_001] 购买商品:', product);\n\t\t\t\t\n\t\t\t\t// 确认购买对话框\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认购买',\n\t\t\t\t\tcontent: `确定要购买\"${product.name}\"吗？`,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 根据商品类型进行不同的购买流程\n\t\t\t\t\t\t\tif (product.product_type === 2) {\n\t\t\t\t\t\t\t\t// 课程购买\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: `/pages/course/buy?id=${product.id}`\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 商品购买 - 加入购物车或直接购买\n\t\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\t\turl: `/pages/product/buy?id=${product.id}&type=direct`\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 导航到商品详情页 - 新增方法\n\t\t\t */\n\t\t\tnavigateToProduct(product) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,047-INFO-[complete][navigateToProduct_001] 导航到商品详情:', product);\n\t\t\t\t\n\t\t\t\t// 根据产品类型使用不同的路径\n\t\t\t\tlet url = '';\n\t\t\t\tconst productId = product.id || 290; // 如果没有id则使用默认值290\n\t\t\t\t\n\t\t\t\tif (product.type === 'course') {\n\t\t\t\t\t// 课程路径\n\t\t\t\t\turl = `/activity/kecheng/product?id=${productId}`;\n\t\t\t\t} else {\n\t\t\t\t\t// 商品路径\n\t\t\t\t\turl = `/shopPackage/shop/product?id=${productId}`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('2025-01-06 11:45:00,048-INFO-[complete][navigateToProduct_002] 准备导航到:', url);\n\t\t\t\t\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: url,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('2025-01-06 11:45:00,049-INFO-[complete][navigateToProduct_003] 成功导航到详情页:', url);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('2025-01-06 11:45:00,050-ERROR-[complete][navigateToProduct_004] 导航失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\t/**\n\t\t\t * 添加到购物车 - 新增方法\n\t\t\t */\n\t\t\taddToCart(product) {\n\t\t\t\tconsole.log('2025-01-06 11:45:00,051-INFO-[complete][addToCart_001] 点击购物车按钮:', product);\n\t\t\t\t\n\t\t\t\tif (product.type === 'course') {\n\t\t\t\t\t// 课程类型：直接跳转到课程详情页\n\t\t\t\t\tthis.navigateToProduct(product);\n\t\t\t\t} else {\n\t\t\t\t\t// 商品类型：添加到购物车\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已添加到购物车',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 这里可以调用实际的购物车API\n\t\t\t\t\t// const app = getApp();\n\t\t\t\t\t// app.post('Cart/add', {\n\t\t\t\t\t//     product_id: product.id,\n\t\t\t\t\t//     quantity: 1\n\t\t\t\t\t// }, (response) => {\n\t\t\t\t\t//     if (response.status === 1) {\n\t\t\t\t\t//         uni.showToast({\n\t\t\t\t\t//             title: '添加成功',\n\t\t\t\t\t//             icon: 'success'\n\t\t\t\t\t//         });\n\t\t\t\t\t//     }\n\t\t\t\t\t// });\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tonLoad(options) {\n\t\t\tconsole.log('2025-01-06 11:45:00,100-INFO-[complete][onLoad_001] 舌诊完整页面加载，参数:', options);\n\t\t\t\n\t\t\t// 检测是否为小程序环境\n\t\t\t// #ifdef MP\n\t\t\tthis.isMiniProgram = true;\n\t\t\tconsole.log('2025-01-06 11:45:00,101-INFO-[complete][onLoad_002] 检测到小程序环境');\n\t\t\t// #endif\n\t\t\t\n\t\t\t// #ifndef MP\n\t\t\tconsole.log('2025-01-06 11:45:00,101-INFO-[complete][onLoad_002] 检测到非小程序环境(浏览器)');\n\t\t\t// #endif\n\t\t\t\n\t\t\t// 获取传递的参数\n\t\t\tif (options.record_id || options.recordId) {\n\t\t\t\tthis.recordId = options.record_id || options.recordId;\n\t\t\t\tconsole.log('2025-01-06 11:45:00,102-INFO-[complete][onLoad_003] 获取记录ID:', this.recordId);\n\t\t\t}\n\t\t\t\n\t\t\tif (options.order_no || options.orderNo) {\n\t\t\t\tthis.orderNo = options.order_no || options.orderNo;\n\t\t\t\tconsole.log('2025-01-06 11:45:00,103-INFO-[complete][onLoad_004] 获取订单号:', this.orderNo);\n\t\t\t}\n\t\t\t\n\t\t\tif (options.api_type || options.apiType) {\n\t\t\t\tthis.apiType = options.api_type || options.apiType;\n\t\t\t\tconsole.log('2025-01-06 11:45:00,104-INFO-[complete][onLoad_005] 获取API类型:', this.apiType);\n\t\t\t}\n\t\t\t\n\t\t\t// 2025-01-27 获取订单ID参数\n\t\t\tif (options.order_id) {\n\t\t\t\tthis.orderId = options.order_id;\n\t\t\t\tconsole.log('2025-01-27 11:45:00,106-INFO-[complete][onLoad_006] 获取支付订单ID:', this.orderId);\n\t\t\t}\n\t\t\t\n\t\t\t// 临时添加测试视频数据，方便调试\n\t\t\tsetTimeout(() => {\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onLoad_007] 设置测试视频数据');\n\t\t\t\tthis.recommendVideos = [{\n\t\t\t\t\tid: 1001,\n\t\t\t\t\tname: '测试调理视频 - onLoad',\n\t\t\t\t\ttype: 'video',\n\t\t\t\t\turl: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',\n\t\t\t\t\tpic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',\n\t\t\t\t\trecommend_reason: '适合您的体质类型 - 测试数据',\n\t\t\t\t\tview_num: 1500,\n\t\t\t\t\tzan_num: 75\n\t\t\t\t}];\n\t\t\t\tconsole.log('2025-01-31 INFO-[complete][onLoad_008] 测试视频数据设置完成，数量:', this.recommendVideos.length);\n\t\t\t}, 1000);\n\n\t\t\t// 检查必要参数\n\t\t\tif (this.recordId) {\n\t\t\t\t// 获取舌诊分析数据\n\t\t\t\tthis.getAnalysisRecord();\n\t\t\t} else {\n\t\t\t\tconsole.error('2025-01-06 11:45:00,105-ERROR-[complete][onLoad_006] 缺少必要参数recordId');\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '参数错误，请重新进入',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 2000);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n/* 2025-01-03 22:55:53,565-INF0-[complete][style_001] 舌诊完整页面样式定义 */\n\n.tongue-complete {\n\tbackground-color: #ffffff;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx; /* 为底部浮动按钮留出空间 */\n}\n\n/* 顶部导航样式 */\n.top-nav {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 20rpx 30rpx;\n\tbackground-color: white;\n\tposition: relative;\n}\n\n.nav-left {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.back-icon {\n\tfont-size: 40rpx;\n\tcolor: #333;\n}\n\n.nav-title {\n\tfont-size: 36rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tposition: absolute;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n}\n\n.nav-right {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.menu-icon {\n\tfont-size: 30rpx;\n\tcolor: #666;\n}\n\n.record-icon {\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n/* 医院信息 */\n.hospital-info {\n\tbackground-color: #e8f4ff;\n\tpadding: 20rpx;\n\ttext-align: center;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 报告标签页 */\n.report-tabs {\n\tdisplay: flex;\n\tbackground-color: white;\n\tpadding: 0 30rpx;\n}\n\n.tab-item {\n\tflex: 1;\n\ttext-align: center;\n\tpadding: 30rpx 0;\n\tposition: relative;\n}\n\n.tab-item.active .tab-text {\n\tcolor: #1890ff;\n\tfont-weight: 500;\n}\n\n.tab-line {\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\twidth: 60rpx;\n\theight: 6rpx;\n\tbackground-color: #1890ff;\n\tborder-radius: 3rpx;\n}\n\n.tab-text {\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n/* 健康得分卡片 */\n.health-card {\n\tbackground: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n\tborder-radius: 20rpx;\n\tmargin: 30rpx 20rpx;\n\tpadding: 40rpx 30rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba(79, 172, 254, 0.3);\n}\n\n.health-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.health-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.health-avatar {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tpadding: 10rpx;\n\tobject-fit: cover;\n\tobject-position: center;\n\tflex-shrink: 0;\n\tborder: 2rpx solid rgba(255, 255, 255, 0.3);\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n}\n\n.health-symptoms {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n\tflex: 1;\n\tmin-width: 0;\n}\n\n.main-symptom, .sub-symptom {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tflex-wrap: wrap;\n}\n\n.symptom-label {\n\tfont-size: 28rpx;\n\tcolor: rgba(255, 255, 255, 0.9);\n\tfont-weight: 400;\n\twhite-space: nowrap;\n}\n\n.symptom-value {\n\tfont-size: 32rpx;\n\tcolor: white;\n\tfont-weight: 600;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 15rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n\tword-break: break-all;\n\tline-height: 1.4;\n}\n\n.health-right {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.score-circle {\n\twidth: 160rpx;\n\theight: 160rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.4);\n\tborder: 3rpx solid rgba(255, 255, 255, 0.5);\n}\n\n.score-inner {\n\ttext-align: center;\n}\n\n.score-number {\n\tfont-size: 48rpx;\n\tfont-weight: 700;\n\tcolor: #4facfe;\n\tdisplay: block;\n\tline-height: 1;\n}\n\n.score-number.no-score {\n\tfont-size: 48rpx;\n\tfont-weight: 700;\n\tcolor: #cccccc;\n\tdisplay: block;\n\tline-height: 1;\n}\n\n.score-text {\n\tfont-size: 24rpx;\n\tcolor: #4facfe;\n\tfont-weight: 500;\n\tmargin-top: 8rpx;\n\tdisplay: block;\n}\n\n/* 智能问诊 */\n.ai-diagnosis {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin: 30rpx;\n\tpadding: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n}\n\n.ai-left {\n\tflex: 1;\n}\n\n.ai-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.ai-subtitle {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 5rpx;\n}\n\n.ai-description {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.login-btn {\n\tbackground-color: #1890ff;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n}\n\n.analysis-complete-btn {\n\tbackground-color: #52c41a;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n}\n\n.loading-btn {\n\tbackground-color: #faad14;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n\tanimation: pulse 1.5s infinite;\n}\n\n@keyframes pulse {\n\t0% { opacity: 1; }\n\t50% { opacity: 0.6; }\n\t100% { opacity: 1; }\n}\n\n/* 可能体征 */\n.symptoms-section {\n\tmargin: 30rpx 20rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 30rpx;\n}\n\n.symptoms-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 15rpx;\n\tmargin-top: 20rpx;\n}\n\n.symptom-tag {\n\tbackground: rgba(79, 172, 254, 0.08);\n\tcolor: #4facfe;\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\tborder: 1rpx solid rgba(79, 172, 254, 0.2);\n\tbackdrop-filter: blur(10rpx);\n\tbox-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);\n}\n\n/* 舌象分析 */\n.tongue-analysis {\n\tmargin: 30rpx;\n}\n\n.tongue-diagram {\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 40rpx;\n\tposition: relative;\n\ttext-align: center;\n}\n\n.tongue-svg {\n\twidth: 600rpx;\n\theight: 480rpx;\n\tposition: relative;\n\tbackground: #ffffff;\n\tborder-radius: 15rpx;\n\toverflow: hidden;\n\tmargin: 0 auto;\n}\n\n/* CSS绘制的舌头形状 */\n.tongue-shape {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\twidth: 350rpx;\n\theight: 400rpx;\n\tbackground: linear-gradient(135deg, #ffb3ba, #ff8a9b);\n\tborder-radius: 85rpx 85rpx 175rpx 175rpx;\n\ttransform: translate(-50%, -50%);\n\tbox-shadow: inset 0 15rpx 30rpx rgba(255, 255, 255, 0.3),\n\t            inset 0 -15rpx 30rpx rgba(0, 0, 0, 0.1),\n\t            0 8rpx 20rpx rgba(0, 0, 0, 0.2);\n}\n\n/* 舌根区域高光效果 */\n.tongue-shape::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 20rpx;\n\tleft: 50%;\n\twidth: 200rpx;\n\theight: 60rpx;\n\tbackground: rgba(255, 255, 255, 0.3);\n\tborder-radius: 50%;\n\ttransform: translateX(-50%);\n}\n\n/* 舌苔覆盖区域 */\n.tongue-shape::after {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 15%;\n\tleft: 50%;\n\twidth: 280rpx;\n\theight: 280rpx;\n\tbackground: rgba(255, 255, 255, 0.15);\n\tborder-radius: 60rpx 60rpx 140rpx 140rpx;\n\ttransform: translateX(-50%);\n\tbox-shadow: inset 0 5rpx 15rpx rgba(255, 255, 255, 0.2);\n}\n\n.tongue-markers {\n\tposition: relative;\n\twidth: 100%;\n\theight: 100%;\n\t/* 确保小程序端的定位基准 */\n\toverflow: visible;\n\tz-index: 1;\n}\n\n.marker {\n\tposition: absolute;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tz-index: 10;\n\t/* 确保小程序端的样式继承 */\n\tbox-sizing: border-box;\n\t/* 默认居中对齐 */\n\ttransform: translate(-50%, -50%);\n\t/* 移除动态样式依赖，使用CSS类定位 */\n\t/* 适当缩小标记整体尺寸 */\n\tzoom: 0.9;\n}\n\n.marker.abnormal .marker-icon {\n\tbackground: #ff4d4f;\n\tcolor: white;\n}\n\n.marker.normal .marker-icon {\n\tbackground: #52c41a;\n\tcolor: white;\n}\n\n.marker-icon {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 20rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 6rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);\n}\n\n.marker-text {\n\tfont-size: 20rpx;\n\tcolor: #333;\n\tbackground: rgba(255, 255, 255, 0.95);\n\tpadding: 3rpx 8rpx;\n\tborder-radius: 10rpx;\n\ttext-align: center;\n\tbox-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);\n\twhite-space: nowrap;\n\tfont-weight: 500;\n\tmax-width: 120rpx; /* 限制文本宽度 */\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n}\n\n.tongue-photo-btn {\n\tposition: absolute;\n\tbottom: 15rpx;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n\tpadding: 10rpx 25rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 24rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n}\n\n.photo-btn-text {\n\tcolor: white;\n\tfont-weight: 500;\n}\n\n/* 体征异常 */\n.abnormal-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.abnormal-title {\n\tmargin-bottom: 30rpx;\n}\n\n.abnormal-count {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.abnormal-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 30rpx;\n}\n\n.abnormal-item {\n\tborder-bottom: 1rpx solid #f0f0f0;\n\tpadding-bottom: 30rpx;\n}\n\n.abnormal-item:last-child {\n\tborder-bottom: none;\n\tpadding-bottom: 0;\n}\n\n.abnormal-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 15rpx;\n}\n\n.abnormal-icon {\n\twidth: 32rpx;\n\theight: 32rpx;\n\tbackground-color: #ff4d4f;\n\tcolor: white;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 20rpx;\n}\n\n.abnormal-name {\n\tfont-size: 30rpx;\n\tfont-weight: 500;\n\tcolor: #ff4d4f;\n}\n\n.abnormal-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.abnormal-desc-container {\n\tdisplay: flex;\n\talign-items: flex-end;\n\tgap: 20rpx;\n}\n\n.doctor-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tflex-shrink: 0;\n}\n\n/* 舌象体征论述 - 简约医疗风格 */\n.theory-section {\n\tmargin: 0rpx;\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\t/* box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2); */\n}\n\n.theory-header-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\tmargin-bottom: 30rpx;\n\tpadding-bottom: 20rpx;\n\tborder-bottom: 2rpx solid rgba(24, 144, 255, 0.1);\n}\n\n.theory-icon {\n\tfont-size: 40rpx;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.3);\n}\n\n.theory-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #8a92b2;\n\tfont-weight: 500;\n\tmargin-left: auto;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n/* 理论卡片 */\n.theory-card {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 24rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.4);\n\tbox-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.theory-card:hover {\n\ttransform: translateY(-4rpx);\n\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);\n}\n\n.theory-card:last-child {\n\tmargin-bottom: 0;\n}\n\n/* 体质分析卡片 */\n.constitution-card {\n\tborder-left: 6rpx solid #52c41a;\n\tbackground: linear-gradient(135deg, rgba(246, 255, 237, 0.9), rgba(240, 249, 235, 0.7));\n}\n\n.constitution-card::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, transparent 70%);\n\tborder-radius: 50%;\n}\n\n.constitution-tag {\n\tbackground: linear-gradient(135deg, #52c41a, #73d13d);\n\tcolor: white;\n}\n\n/* 证候介绍卡片 */\n.syndrome-card {\n\tborder-left: 6rpx solid #1890ff;\n\tbackground: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(230, 247, 255, 0.7));\n}\n\n.syndrome-card::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tright: 0;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);\n\tborder-radius: 50%;\n}\n\n.syndrome-tag {\n\tbackground: linear-gradient(135deg, #1890ff, #40a9ff);\n\tcolor: white;\n}\n\n/* 卡片头部 */\n.card-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 20rpx;\n}\n\n.header-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\tflex: 1;\n}\n\n.theory-tag {\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tletter-spacing: 0.5rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.theory-title {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: #1a202c;\n\tletter-spacing: 0.5rpx;\n}\n\n.header-right {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n/* 状态指示器 */\n.status-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 20rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.status-indicator.active .status-dot {\n\tcolor: #52c41a;\n\tanimation: pulse 2s infinite;\n}\n\n.status-text {\n\tfont-size: 22rpx;\n\tfont-weight: 500;\n\tcolor: #52c41a;\n}\n\n@keyframes pulse {\n\t0%, 100% { opacity: 1; }\n\t50% { opacity: 0.7; }\n}\n\n/* 卡片内容 */\n.card-content {\n\tposition: relative;\n}\n\n.content-wrapper {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.theory-description {\n\tfont-size: 28rpx;\n\tcolor: #4a5568;\n\tline-height: 1.8;\n\tfont-weight: 400;\n\tletter-spacing: 0.5rpx;\n\ttext-align: justify;\n\ttext-justify: inter-ideograph;\n}\n\n/* 医学印章 */\n.medical-stamp {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 12rpx;\n\tmargin-top: 16rpx;\n\tpadding: 12rpx 16rpx;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tborder-radius: 30rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.4);\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n\talign-self: flex-end;\n}\n\n.doctor-avatar-small {\n\twidth: 36rpx;\n\theight: 36rpx;\n\tborder-radius: 50%;\n\tborder: 2rpx solid rgba(255, 255, 255, 0.8);\n\tflex-shrink: 0;\n}\n\n.stamp-text {\n\tfont-size: 22rpx;\n\tcolor: #718096;\n\tfont-weight: 500;\n\tletter-spacing: 0.5rpx;\n}\n\n/* 空状态 */\n.empty-theory-state {\n\ttext-align: center;\n\tpadding: 80rpx 40rpx;\n\tbackground: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));\n\tborder-radius: 20rpx;\n\tborder: 2rpx dashed rgba(203, 213, 225, 0.6);\n\tposition: relative;\n}\n\n.empty-icon {\n\tfont-size: 80rpx;\n\tmargin-bottom: 20rpx;\n\topacity: 0.6;\n}\n\n.empty-title {\n\tfont-size: 32rpx;\n\tcolor: #64748b;\n\tfont-weight: 600;\n\tmargin-bottom: 12rpx;\n\tletter-spacing: 0.5rpx;\n}\n\n.empty-description {\n\tfont-size: 26rpx;\n\tcolor: #94a3b8;\n\tfont-weight: 400;\n\tline-height: 1.6;\n\tmargin-bottom: 30rpx;\n}\n\n/* 加载动画 */\n.loading-dots {\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 8rpx;\n}\n\n.dot {\n\twidth: 8rpx;\n\theight: 8rpx;\n\tborder-radius: 50%;\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\n\tanimation: loading 1.4s infinite ease-in-out both;\n}\n\n.dot:nth-child(1) { animation-delay: -0.32s; }\n.dot:nth-child(2) { animation-delay: -0.16s; }\n.dot:nth-child(3) { animation-delay: 0s; }\n\n@keyframes loading {\n\t0%, 80%, 100% {\n\t\ttransform: scale(0);\n\t\topacity: 0.5;\n\t}\n\t40% {\n\t\ttransform: scale(1);\n\t\topacity: 1;\n\t}\n}\n\n/* 患病风险 */\n.risk-section {\n\tmargin: 30rpx 20rpx;\n}\n\n.risk-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 30rpx;\n\tmargin-top: 30rpx;\n\tjustify-items: center;\n}\n\n.risk-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 15rpx;\n}\n\n.risk-progress-circle {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tposition: relative;\n\tpadding: 6rpx;\n\tbackground: #ffffff;\n\tbox-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.15);\n}\n\n.progress-ring {\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 50%;\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tpadding: 2rpx;\n\tbox-sizing: border-box;\n}\n\n.progress-inner {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\ttext-align: center;\n\twidth: 100rpx;\n\theight: 100rpx;\n\tbackground: #ffffff;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);\n}\n\n.progress-percentage {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tcolor: #4facfe;\n}\n\n.risk-name {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\ttext-align: center;\n\tfont-weight: 500;\n\tline-height: 1.4;\n\tmax-width: 120rpx;\n}\n\n/* 需要警惕 */\n.warning-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.warning-list {\n\tmargin-top: 30rpx;\n}\n\n.warning-item {\n\tmargin-bottom: 30rpx;\n\tposition: relative;\n}\n\n.warning-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.warning-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.warning-bullet {\n\tcolor: #ff4d4f;\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n}\n\n.warning-title {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.warning-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tpadding-right: 100rpx;\n}\n\n.doctor-avatar {\n\tposition: absolute;\n\tbottom: 0;\n\tright: 0;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n}\n\n/* 结果解读 */\n.result-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.constitution-chart {\n\tmargin: 40rpx 0;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tgap: 30rpx;\n}\n\n.constitution-circle {\n\twidth: 400rpx;\n\theight: 400rpx;\n\tborder: 4rpx solid #e6f4ff;\n\tborder-radius: 50%;\n\tposition: relative;\n\tbackground: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);\n\tbox-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);\n}\n\n.constitution-inner {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 100%;\n\theight: 100%;\n}\n\n.constitution-center {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\ttext-align: center;\n\tbackground: linear-gradient(135deg, #ffffff 0%, #f8fdff 100%);\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.15);\n\tborder: 2rpx solid #e6f4ff;\n}\n\n.constitution-type {\n\tfont-size: 36rpx;\n\tfont-weight: 600;\n\tcolor: #1890ff;\n\tmargin-bottom: 5rpx;\n}\n\n.constitution-indicator {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.indicator-dot {\n\tposition: absolute;\n\twidth: 20rpx;\n\theight: 20rpx;\n\tbackground: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n\tborder-radius: 50%;\n\tbox-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);\n\tanimation: pulse 2s ease-in-out infinite;\n}\n\n@keyframes pulse {\n\t0% {\n\t\ttransform: scale(1);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);\n\t}\n\t50% {\n\t\ttransform: scale(1.2);\n\t\tbox-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.6);\n\t}\n\t100% {\n\t\ttransform: scale(1);\n\t\tbox-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);\n\t}\n}\n\n.direction-label {\n\tposition: absolute;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #1890ff;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tborder: 1rpx solid #e6f4ff;\n\ttransform: translate(-50%, -50%);\n}\n\n.direction-label.top {\n\ttop: 20rpx;\n\tleft: 50%;\n}\n\n.direction-label.right {\n\tright: 20rpx;\n\ttop: 50%;\n\ttransform: translate(50%, -50%);\n}\n\n.direction-label.bottom {\n\tbottom: 20rpx;\n\tleft: 50%;\n\ttransform: translate(-50%, 50%);\n}\n\n.direction-label.left {\n\tleft: 20rpx;\n\ttop: 50%;\n\ttransform: translate(-50%, -50%);\n}\n\n.scale-marks {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\twidth: 100%;\n\theight: 100%;\n}\n\n.scale-mark {\n\tposition: absolute;\n\twidth: 2rpx;\n\theight: 20rpx;\n\tbackground: linear-gradient(to bottom, #1890ff, rgba(24, 144, 255, 0.3));\n\ttop: 10rpx;\n\tleft: 50%;\n\ttransform-origin: 50% 190rpx;\n\tborder-radius: 1rpx;\n}\n\n.constitution-status {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.status-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tpadding: 12rpx 20rpx;\n\tborder-radius: 25rpx;\n\tborder: 1rpx solid #e6f4ff;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);\n}\n\n.status-dot {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tborder-radius: 50%;\n\tbox-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);\n}\n\n.status-text {\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.result-description {\n\tmargin: 30rpx 0;\n}\n\n.result-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tdisplay: block;\n\tmargin-bottom: 15rpx;\n}\n\n.result-text.secondary {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.result-note {\n\tbackground-color: #f8f9fa;\n\tpadding: 30rpx;\n\tborder-radius: 15rpx;\n\tposition: relative;\n}\n\n.note-content {\n\tpadding-right: 100rpx;\n}\n\n.note-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tline-height: 1.6;\n}\n\n.doctor-avatar-note {\n\tposition: absolute;\n\tbottom: 20rpx;\n\tright: 20rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n}\n\n/* VIP会员专享 */\n.vip-section {\n\tmargin: 30rpx;\n\tbackground: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.vip-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.vip-icon {\n\tfont-size: 32rpx;\n}\n\n.vip-text {\n\tfont-size: 28rpx;\n\tcolor: #8b4513;\n\tfont-weight: 500;\n}\n\n.vip-card {\n\ttext-align: center;\n}\n\n.vip-title {\n\tfont-size: 32rpx;\n\tcolor: #8b4513;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.vip-subtitle {\n\tfont-size: 48rpx;\n\tcolor: #8b4513;\n\tfont-weight: bold;\n}\n\n/* 报告解读 */\n.report-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.report-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.report-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.member-badge {\n\tbackground-color: #1890ff;\n\tcolor: white;\n\tpadding: 5rpx 10rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 24rpx;\n}\n\n.symptoms-summary {\n\tmargin-bottom: 30rpx;\n}\n\n.summary-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tmargin-right: 10rpx;\n\tmargin-left: 10rpx;\n}\n\n.symptom-highlight {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #1890ff;\n}\n\n.main-symptom-detail {\n\tmargin-bottom: 30rpx;\n}\n\n.symptom-label {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.symptom-description {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.view-detail-btn {\n\tbackground-color: #1890ff;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n\tmargin-top: 20rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.doctor-section {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n}\n\n.doctor-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.doctor-avatar {\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n}\n\n/* 调理原则 */\n.principle-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.principle-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.principle-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.principle-list {\n\tmargin-bottom: 30rpx;\n}\n\n.principle-item {\n\tmargin-bottom: 15rpx;\n}\n\n.principle-bullet {\n\tcolor: #ff4d4f;\n\tfont-size: 24rpx;\n\tfont-weight: bold;\n\tmargin-right: 10rpx;\n}\n\n.principle-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n}\n\n.view-principle-btn {\n\tbackground-color: #1890ff;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n\tmargin-top: 20rpx;\n\tmargin-bottom: 30rpx;\n}\n\n.custom-plan-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\ttext-align: center;\n}\n\n/* 今日营养目标 */\n.nutrition-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.nutrition-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.nutrition-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.chart-container {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.chart-left {\n\tflex: 1;\n}\n\n.pie-chart {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tborder-radius: 50%;\n\tposition: relative;\n\tmargin-bottom: 20rpx;\n}\n\n.pie-center {\n\tposition: absolute;\n\ttop: 50%;\n\tleft: 50%;\n\ttransform: translate(-50%, -50%);\n\ttext-align: center;\n\tbackground-color: white;\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.calories-number {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tdisplay: block;\n\tmargin-bottom: 5rpx;\n\tcolor: #333;\n}\n\n.calories-unit {\n\tfont-size: 20rpx;\n\tcolor: #666;\n}\n\n.chart-right {\n\tflex: 1;\n}\n\n.nutrition-item {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.nutrition-color {\n\twidth: 20rpx;\n\theight: 20rpx;\n\tborder-radius: 4rpx;\n\tflex-shrink: 0;\n}\n\n.nutrition-name {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tflex: 1;\n}\n\n.nutrition-percent {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\twidth: 60rpx;\n\ttext-align: right;\n}\n\n.nutrition-amount {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\twidth: 80rpx;\n\ttext-align: right;\n}\n\n/* 今日专属膳方 */\n.recipe-section {\n\tmargin: 30rpx;\n\tbackground-color: white;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.recipe-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.recipe-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.adjust-plan {\n\tbackground-color: #1890ff;\n\tcolor: white;\n\tpadding: 20rpx 40rpx;\n\tborder-radius: 25rpx;\n\tfont-size: 28rpx;\n\tmargin-left: auto;\n}\n\n.recipe-card {\n\tbackground-color: #f0f0f0;\n\tborder-radius: 15rpx;\n\tpadding: 30rpx;\n}\n\n.recipe-content {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.recipe-info {\n\tflex: 1;\n}\n\n.recipe-name {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n}\n\n.recipe-effect {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.recipe-image-container {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 15rpx;\n\toverflow: hidden;\n}\n\n.recipe-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.doctor-recommendation {\n\tmargin-top: 20rpx;\n}\n\n/* 底部按钮 - 浮动样式 */\n.bottom-buttons {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tdisplay: flex;\n\tgap: 20rpx;\n\tpadding: 20rpx 30rpx;\n\tpadding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配刘海屏 */\n\tbackground: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 1) 100%);\n\tbackdrop-filter: blur(20rpx);\n\tborder-top: 1rpx solid rgba(0, 0, 0, 0.08);\n\tbox-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);\n\tz-index: 999;\n}\n\n.btn-secondary {\n\tflex: 1;\n\tpadding: 28rpx 25rpx;\n\ttext-align: center;\n\tbackground: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n\tborder: 2rpx solid #e2e8f0;\n\tborder-radius: 28rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #64748b;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.btn-secondary:active {\n\ttransform: translateY(2rpx);\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.btn-primary {\n\tflex: 1;\n\tpadding: 28rpx 25rpx;\n\ttext-align: center;\n\tbackground: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);\n\tcolor: white;\n\tborder-radius: 28rpx;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tborder: none;\n\tbox-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);\n\ttransition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.btn-primary::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: -100%;\n\twidth: 100%;\n\theight: 100%;\n\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n\ttransition: left 0.5s;\n}\n\n.btn-primary:active {\n\ttransform: translateY(2rpx);\n\tbox-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);\n}\n\n.btn-primary:active::before {\n\tleft: 100%;\n}\n\n/* 详细舌象特征列表 */\n.tongue-features-detail {\n\tmargin-top: 30rpx;\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\n}\n\n.features-header {\n\ttext-align: center;\n\tmargin-bottom: 30rpx;\n}\n\n.features-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t-webkit-background-clip: text;\n\t-webkit-text-fill-color: transparent;\n\tmargin-bottom: 8rpx;\n\tletter-spacing: 1rpx;\n}\n\n.features-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #8a92b2;\n\ttext-align: center;\n\tmargin-bottom: 10rpx;\n\tfont-weight: 500;\n}\n\n.features-grid {\n\tdisplay: grid;\n\tgrid-template-columns: 1fr 1fr;\n\tgap: 20rpx;\n\tmargin-top: 20rpx;\n}\n\n.feature-card {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n\ttransition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n\tposition: relative;\n\toverflow: hidden;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n}\n\n.feature-card::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\theight: 3rpx;\n\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);\n\ttransition: all 0.3s ease;\n}\n\n.feature-card:hover {\n\ttransform: translateY(-4rpx);\n\tbox-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);\n}\n\n.normal-card {\n\tborder-left: 4rpx solid transparent;\n\tbackground: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(183, 235, 143, 0.05));\n}\n\n.normal-card::before {\n\tbackground: linear-gradient(90deg, #52c41a, #b7eb8f, #52c41a);\n}\n\n.abnormal-card {\n\tborder-left: 4rpx solid transparent;\n\tbackground: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 158, 158, 0.05));\n}\n\n.abnormal-card::before {\n\tbackground: linear-gradient(90deg, #ff4d4f, #ff9e9e, #ff4d4f);\n}\n\n.card-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 16rpx;\n}\n\n.feature-badge {\n\tfont-size: 20rpx;\n\tfont-weight: 600;\n\tpadding: 6rpx 14rpx;\n\tborder-radius: 20rpx;\n\tletter-spacing: 0.5rpx;\n\ttext-transform: uppercase;\n}\n\n.normal-badge {\n\tbackground: linear-gradient(135deg, #52c41a, #73d13d);\n\tcolor: white;\n\tbox-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);\n}\n\n.abnormal-badge {\n\tbackground: linear-gradient(135deg, #ff4d4f, #ff7875);\n\tcolor: white;\n\tbox-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);\n}\n\n.feature-category {\n\tfont-size: 22rpx;\n\tcolor: #8a92b2;\n\tbackground: rgba(255, 255, 255, 0.6);\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 12rpx;\n\tfont-weight: 500;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n\tbackdrop-filter: blur(5rpx);\n}\n\n.feature-name {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #2c3e50;\n\tmargin-bottom: 12rpx;\n\tline-height: 1.4;\n}\n\n.feature-description {\n\tfont-size: 24rpx;\n\tcolor: #64748b;\n\tline-height: 1.6;\n\tmargin-top: 8rpx;\n\tfont-weight: 400;\n}\n\n.no-features {\n\ttext-align: center;\n\tpadding: 60rpx 40rpx;\n\tbackground: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));\n\tborder-radius: 16rpx;\n\tborder: 2rpx dashed rgba(203, 213, 225, 0.6);\n}\n\n.empty-icon {\n\tfont-size: 48rpx;\n\tmargin-bottom: 16rpx;\n\topacity: 0.7;\n}\n\n.empty-text {\n\tfont-size: 28rpx;\n\tcolor: #64748b;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.empty-hint {\n\tfont-size: 22rpx;\n\tcolor: #94a3b8;\n\tfont-weight: 400;\n}\n\n/* 动态显示检测到的舌象特征 */\n.displayedTongueFeatures {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n\tpointer-events: none;\n}\n\n/* 获取舌象特征的动态位置 */\n.getMarkerPosition {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\twidth: 100%;\n\theight: 100%;\n}\n\n/* 保养建议样式 */\n.care-suggestions-section {\n\t/* margin: 30rpx;\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 40rpx;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n\tbackdrop-filter: blur(10rpx);\n\tborder: 1rpx solid rgba(255, 255, 255, 0.2); */\n}\n\n.care-category {\n\tmargin-bottom: 40rpx;\n\tbackground: rgba(255, 255, 255, 0.6);\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);\n}\n\n.care-category:last-child {\n\tmargin-bottom: 0;\n}\n\n.category-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 25rpx;\n\tpadding-bottom: 20rpx;\n\tborder-bottom: 2rpx solid rgba(79, 172, 254, 0.1);\n}\n\n.category-icon {\n\tfont-size: 48rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tmargin-right: 20rpx;\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n}\n\n.food-icon {\n\tbackground: linear-gradient(135deg, #ff9a56, #ffad56);\n}\n\n.sport-icon {\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\n}\n\n.life-icon {\n\tbackground: linear-gradient(135deg, #a8edea, #fed6e3);\n}\n\n.music-icon {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n}\n\n.treatment-icon {\n\tbackground: linear-gradient(135deg, #f093fb, #f5576c);\n}\n\n.category-title {\n\tfont-size: 36rpx;\n\tfont-weight: 700;\n\tcolor: #333;\n\tmargin-bottom: 8rpx;\n\tletter-spacing: 1rpx;\n}\n\n.category-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #8a92b2;\n\tfont-weight: 500;\n\tmargin-left: auto;\n\tbackground: rgba(255, 255, 255, 0.8);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.suggestion-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.suggestion-item {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));\n\tborder-radius: 12rpx;\n\tpadding: 24rpx;\n\tborder: 1rpx solid rgba(255, 255, 255, 0.4);\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n\ttransition: all 0.3s ease;\n}\n\n.suggestion-item:hover {\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.item-header {\n\tmargin-bottom: 16rpx;\n}\n\n.item-tag {\n\tdisplay: inline-block;\n\tfont-size: 22rpx;\n\tfont-weight: 600;\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 20rpx;\n\tletter-spacing: 0.5rpx;\n\tcolor: white;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);\n}\n\n.forbidden-tag {\n\tbackground: linear-gradient(135deg, #ff4d4f, #ff7875);\n}\n\n.recommend-tag {\n\tbackground: linear-gradient(135deg, #52c41a, #73d13d);\n}\n\n.life-tag {\n\tbackground: linear-gradient(135deg, #722ed1, #9254de);\n}\n\n.music-tag {\n\tbackground: linear-gradient(135deg, #1890ff, #40a9ff);\n}\n\n.treatment-tag {\n\tbackground: linear-gradient(135deg, #fa8c16, #ffa940);\n}\n\n.item-content {\n\tfont-size: 28rpx;\n\tcolor: #4a5568;\n\tline-height: 1.8;\n\tfont-weight: 400;\n\tletter-spacing: 0.5rpx;\n}\n\n.treatment-note {\n\tmargin-top: 16rpx;\n\tbackground: rgba(255, 193, 7, 0.1);\n\tborder: 1rpx solid rgba(255, 193, 7, 0.3);\n\tborder-radius: 8rpx;\n\tpadding: 12rpx 16rpx;\n}\n\n.note-warning {\n\tfont-size: 24rpx;\n\tcolor: #fa8c16;\n\tfont-weight: 500;\n}\n\n.care-reminder {\n\tbackground: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.03));\n\tborder-radius: 16rpx;\n\tpadding: 30rpx;\n\tborder: 2rpx solid rgba(24, 144, 255, 0.1);\n\tposition: relative;\n\tmargin-top: 30rpx;\n}\n\n.reminder-header {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 16rpx;\n}\n\n.reminder-icon {\n\tfont-size: 40rpx;\n\tmargin-right: 16rpx;\n}\n\n.reminder-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1890ff;\n}\n\n.reminder-content {\n\tfont-size: 28rpx;\n\tcolor: #4a5568;\n\tline-height: 1.8;\n\tpadding-right: 100rpx;\n}\n\n.doctor-avatar-reminder {\n\tposition: absolute;\n\tbottom: 20rpx;\n\tright: 20rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 40rpx;\n\tborder: 3rpx solid rgba(255, 255, 255, 0.8);\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\n}\n\n/* 无保养建议数据提示 */\n.no-care-suggestions {\n\ttext-align: center;\n\tpadding: 60rpx 40rpx;\n\tbackground: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));\n\tborder-radius: 16rpx;\n\tborder: 2rpx dashed rgba(203, 213, 225, 0.6);\n}\n\n.no-care-text {\n\tfont-size: 28rpx;\n\tcolor: #64748b;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.no-care-hint {\n\tfont-size: 22rpx;\n\tcolor: #94a3b8;\n\tfont-weight: 400;\n}\n\n\n\n.theory-item {\n\tmargin-bottom: 40rpx;\n}\n\n.theory-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.theory-header {\n\tdisplay: flex;\n\talign-items: baseline;\n\tgap: 10rpx;\n\tmargin-bottom: 15rpx;\n}\n\n.theory-name {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n}\n\n.theory-name.water-wet {\n\tcolor: #1890ff;\n}\n\n.theory-name.spleen-weak {\n\tcolor: #52c41a;\n}\n\n.theory-summary {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.theory-subtitle {\n\tmargin-bottom: 20rpx;\n}\n\n.theory-subtitle text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.theory-content {\n\tposition: relative;\n}\n\n.theory-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.8;\n\tpadding-right: 100rpx;\n}\n\n/* 体质分析卡片 */\n.theory-card {\n\tbackground: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);\n\tborder-radius: 16rpx;\n\tpadding: 24rpx;\n\tborder: 1rpx solid rgba(24, 144, 255, 0.1);\n\tbox-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);\n}\n\n.card-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 16rpx;\n}\n\n.header-left {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.theory-tag {\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tpadding: 6rpx 12rpx;\n\tborder-radius: 16rpx;\n\tletter-spacing: 0.5rpx;\n\ttext-transform: uppercase;\n}\n\n.theory-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #1890ff;\n}\n\n.header-right {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.status-indicator {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.status-dot {\n\twidth: 16rpx;\n\theight: 16rpx;\n\tborder-radius: 50%;\n\tbox-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);\n}\n\n.status-text {\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n}\n\n.content-wrapper {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 15rpx;\n}\n\n.medical-stamp {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 10rpx;\n}\n\n.doctor-avatar-small {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tborder-radius: 50%;\n\tflex-shrink: 0;\n}\n\n.stamp-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tfont-weight: 500;\n}\n\n/* 空状态 */\n.empty-theory-state {\n\ttext-align: center;\n\tpadding: 60rpx 40rpx;\n\tbackground: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));\n\tborder-radius: 16rpx;\n\tborder: 2rpx dashed rgba(203, 213, 225, 0.6);\n}\n\n.empty-title {\n\tfont-size: 28rpx;\n\tcolor: #64748b;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.empty-description {\n\tfont-size: 22rpx;\n\tcolor: #94a3b8;\n\tfont-weight: 400;\n}\n\n.loading-dots {\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 10rpx;\n\tmargin-top: 20rpx;\n}\n\n.dot {\n\twidth: 12rpx;\n\theight: 12rpx;\n\tborder-radius: 50%;\n\tbackground: #999;\n\tanimation: blink 1s infinite;\n}\n\n@keyframes blink {\n\t0%, 100% { opacity: 1; }\n\t50% { opacity: 0.5; }\n}\n\n/* 舌象特征标记位置定义 - 优化后的紧凑布局 */\n.marker-root {\n\ttop: 20% !important;\n\tleft: 50% !important;\n}\n\n.marker-tongue {\n\ttop: 38% !important;\n\tleft: 50% !important;\n}\n\n.marker-thickness {\n\ttop: 45% !important;\n\tleft: 50% !important;\n}\n\n.marker-center {\n\ttop: 50% !important;\n\tleft: 50% !important;\n}\n\n.marker-indent {\n\ttop: 45% !important;\n\tleft: 18% !important;\n}\n\n.marker-sides {\n\ttop: 40% !important;\n\tleft: 15% !important;\n}\n\n.marker-spots {\n\ttop: 50% !important;\n\tleft: 85% !important;\n}\n\n.marker-cracks {\n\ttop: 42% !important;\n\tleft: 82% !important;\n}\n\n.marker-punctures {\n\ttop: 38% !important;\n\tleft: 28% !important;\n}\n\n.marker-mucus {\n\ttop: 38% !important;\n\tleft: 50% !important;\n}\n\n.marker-thick {\n\ttop: 35% !important;\n\tleft: 35% !important;\n}\n\n.marker-decay {\n\ttop: 35% !important;\n\tleft: 65% !important;\n}\n\n.marker-moisture {\n\ttop: 38% !important;\n\tleft: 65% !important;\n}\n\n.marker-peeling {\n\ttop: 32% !important;\n\tleft: 50% !important;\n}\n\n.marker-color {\n\ttop: 65% !important;\n\tleft: 50% !important;\n}\n\n.marker-tip {\n\ttop: 75% !important;\n\tleft: 50% !important;\n}\n\n/* 小程序特殊定位 - 优化后的紧凑布局 */\n/* #ifdef MP */\n.marker-root {\n\ttop: 95rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-tongue {\n\ttop: 180rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-thickness {\n\ttop: 216rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-center {\n\ttop: 240rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-indent {\n\ttop: 216rpx !important;\n\tleft: 90rpx !important;\n}\n\n.marker-sides {\n\ttop: 192rpx !important;\n\tleft: 75rpx !important;\n}\n\n.marker-spots {\n\ttop: 240rpx !important;\n\tleft: 510rpx !important;\n}\n\n.marker-cracks {\n\ttop: 202rpx !important;\n\tleft: 450rpx !important;\n}\n\n.marker-punctures {\n\ttop: 182rpx !important;\n\tleft: 160rpx !important;\n}\n\n.marker-mucus {\n\ttop: 182rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-thick {\n\ttop: 168rpx !important;\n\tleft: 210rpx !important;\n}\n\n.marker-decay {\n\ttop: 168rpx !important;\n\tleft: 390rpx !important;\n}\n\n.marker-moisture {\n\ttop: 182rpx !important;\n\tleft: 390rpx !important;\n}\n\n.marker-peeling {\n\ttop: 154rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-color {\n\ttop: 330rpx !important;\n\tleft: 300rpx !important;\n}\n\n.marker-tip {\n\ttop: 380rpx !important;\n\tleft: 300rpx !important;\n}\n/* #endif */\n\n/* 推荐商品 - 新增模块 */\n.recommend-products-section {\n\tmargin: 30rpx 20rpx;\n\tbackground: #ffffff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx 20rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n}\n\n.recommend-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 16rpx;\n\tmargin-bottom: 24rpx;\n\tpadding-bottom: 16rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.recommend-icon-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.recommend-icon {\n\tfont-size: 24rpx;\n\tcolor: white;\n\tfont-weight: 600;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tborder-radius: 12rpx;\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.2);\n}\n\n.recommend-title-wrapper {\n\tflex: 1;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n\tmargin-bottom: 4rpx;\n\tline-height: 1.4;\n}\n\n.recommend-subtitle {\n\tfont-size: 24rpx;\n\tcolor: #999999;\n\tfont-weight: 400;\n}\n\n.products-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 16rpx;\n}\n\n.product-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx;\n\tbackground: #ffffff;\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid #f0f0f0;\n\ttransition: all 0.2s ease;\n\tposition: relative;\n\tgap: 20rpx;\n}\n\n.product-item:active {\n\tbackground: #f8f9fa;\n\ttransform: scale(0.98);\n}\n\n.product-image-wrapper {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 8rpx;\n\toverflow: hidden;\n\tbackground: #f5f5f5;\n\tposition: relative;\n\tflex-shrink: 0;\n}\n\n.product-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.product-type-badge {\n\tposition: absolute;\n\ttop: 4rpx;\n\tleft: 4rpx;\n\tpadding: 2rpx 8rpx;\n\tborder-radius: 8rpx;\n\tfont-size: 18rpx;\n\tfont-weight: 500;\n}\n\n.course-badge {\n\tbackground: rgba(255, 154, 86, 0.9);\n\tcolor: white;\n}\n\n.goods-badge {\n\tbackground: rgba(79, 172, 254, 0.9);\n\tcolor: white;\n}\n\n.type-text {\n\tfont-size: 18rpx;\n\tfont-weight: 500;\n\tcolor: white;\n}\n\n.product-info-wrapper {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: center;\n\tmin-width: 0;\n}\n\n.product-title {\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n\tline-height: 1.4;\n\tmargin-bottom: 8rpx;\n\tdisplay: -webkit-box;\n\t-webkit-box-orient: vertical;\n\t-webkit-line-clamp: 2;\n\toverflow: hidden;\n}\n\n.product-price-wrapper {\n\tdisplay: flex;\n\talign-items: baseline;\n\tgap: 2rpx;\n\tmargin-bottom: 8rpx;\n}\n\n.price-symbol {\n\tfont-size: 24rpx;\n\tcolor: #ff6b35;\n\tfont-weight: 600;\n}\n\n.price-value {\n\tfont-size: 32rpx;\n\tcolor: #ff6b35;\n\tfont-weight: 700;\n}\n\n.price-unit {\n\tfont-size: 20rpx;\n\tcolor: #ff6b35;\n\tfont-weight: 500;\n}\n\n.product-reason {\n\tmargin-bottom: 8rpx;\n}\n\n.reason-label {\n\tfont-size: 22rpx;\n\tcolor: #666666;\n\tmargin-right: 8rpx;\n}\n\n.reason-text {\n\tfont-size: 22rpx;\n\tcolor: #4facfe;\n\tfont-weight: 500;\n}\n\n.product-sales {\n\tmargin-bottom: 0;\n}\n\n.sales-text {\n\tfont-size: 20rpx;\n\tcolor: #999999;\n}\n\n.product-cart-btn {\n\twidth: 56rpx;\n\theight: 56rpx;\n\tborder-radius: 28rpx;\n\tbackground: linear-gradient(135deg, #4facfe, #00f2fe);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.3);\n\ttransition: all 0.2s ease;\n\tflex-shrink: 0;\n}\n\n.product-cart-btn:active {\n\ttransform: scale(0.9);\n\tbox-shadow: 0 1rpx 4rpx rgba(79, 172, 254, 0.4);\n}\n\n.cart-icon {\n\tfont-size: 28rpx;\n\tcolor: white;\n}\n\n/* 推荐说明 */\n.recommend-notice {\n\tmargin-top: 24rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n\tpadding: 20rpx;\n\tborder: 1rpx solid #e9ecef;\n}\n\n.notice-header {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tmargin-bottom: 12rpx;\n}\n\n.notice-icon {\n\tfont-size: 16rpx;\n\tcolor: white;\n\tfont-weight: bold;\n\twidth: 24rpx;\n\theight: 24rpx;\n\tborder-radius: 50%;\n\tbackground: #4facfe;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.notice-title {\n\tfont-size: 26rpx;\n\tfont-weight: 600;\n\tcolor: #333333;\n}\n\n.notice-content {\n\tfont-size: 22rpx;\n\tcolor: #666666;\n\tline-height: 1.6;\n}\n\n.free-text {\n\tfont-size: 28rpx;\n\tcolor: #52c41a;\n\tfont-weight: 600;\n}\n\n/* 视频推荐区域样式 */\n.video-recommend-section {\n\tmargin: 30rpx 20rpx;\n\tbackground: #ffffff;\n\tborder-radius: 16rpx;\n\tpadding: 30rpx 20rpx;\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n}\n\n.video-list {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.video-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\tbackground: #f8f9fa;\n\tborder-radius: 12rpx;\n\toverflow: hidden;\n\tmargin-bottom: 20rpx;\n\ttransition: all 0.3s ease;\n}\n\n.video-item:active {\n\ttransform: scale(0.98);\n\tbackground: #e9ecef;\n}\n\n.video-player-container {\n\twidth: 100%;\n\theight: 400rpx;\n\tposition: relative;\n\tbackground: #000;\n\tborder-radius: 12rpx 12rpx 0 0;\n\toverflow: hidden;\n}\n\n.video-player {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: contain;\n}\n\n.video-cover {\n\tposition: relative;\n\twidth: 200rpx;\n\theight: 120rpx;\n\tflex-shrink: 0;\n\tdisplay: none; /* 隐藏，因为现在使用video组件 */\n}\n\n.cover-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: cover;\n}\n\n.play-overlay {\n\tposition: absolute;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.3);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.play-button {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tbackground: rgba(255, 255, 255, 0.9);\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.play-icon {\n\tfont-size: 24rpx;\n\tcolor: #333;\n\tmargin-left: 4rpx;\n}\n\n.video-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 20rpx;\n\tbackground: #fff;\n\tborder-radius: 0 0 12rpx 12rpx;\n}\n\n.video-title {\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tline-height: 1.4;\n\tmargin-bottom: 10rpx;\n}\n\n.video-reason {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tpadding: 8rpx 12rpx;\n\tbackground: #e8f5e8;\n\tcolor: #4caf50;\n\tborder-radius: 8rpx;\n\talign-self: flex-start;\n}\n\n.video-stats {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.stat-item {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n/* 视频播放器遮罩层样式 */\n.video-player-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.8);\n\tz-index: 9999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tpadding: 40rpx;\n}\n\n.video-player-container {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\twidth: 100%;\n\tmax-width: 640rpx;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.video-player-header {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tpadding: 20rpx 30rpx;\n\tbackground: #f8f9fa;\n\tborder-bottom: 1px solid #eee;\n}\n\n.video-player-title {\n\tfont-size: 32rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\tflex: 1;\n\tmargin-right: 20rpx;\n}\n\n.close-btn {\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: #f0f0f0;\n\tborder-radius: 50%;\n\tcursor: pointer;\n}\n\n.close-text {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tfont-weight: bold;\n}\n\n.video-player {\n\twidth: 100%;\n\theight: 400rpx;\n\tbackground: #000;\n}\n\n.video-player-info {\n\tpadding: 20rpx 30rpx;\n\tbackground: #fff;\n}\n\n.video-description {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n}\n\n.video-stats-row {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.video-stats-row .stat-item {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754044343284\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}