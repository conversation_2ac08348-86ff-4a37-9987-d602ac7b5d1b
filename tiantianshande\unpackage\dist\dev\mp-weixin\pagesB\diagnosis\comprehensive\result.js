require('../../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesB/diagnosis/comprehensive/result"],{

/***/ 6993:
/*!************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"pagesB%2Fdiagnosis%2Fcomprehensive%2Fresult"} ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _result = _interopRequireDefault(__webpack_require__(/*! ./pagesB/diagnosis/comprehensive/result.vue */ 6994));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_result.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 6994:
/*!***************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue ***!
  \***************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./result.vue?vue&type=template&id=c11410ae& */ 6995);
/* harmony import */ var _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./result.vue?vue&type=script&lang=js& */ 6997);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./result.vue?vue&type=style&index=0&lang=css& */ 6999);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["render"],
  _result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesB/diagnosis/comprehensive/result.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 6995:
/*!**********************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=template&id=c11410ae& ***!
  \**********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=template&id=c11410ae& */ 6996);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_template_id_c11410ae___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 6996:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=template&id=c11410ae& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.recommendVideos.length
  var m0 = _vm.t("color1")
  var m1 = _vm.t("color1rgb")
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        m0: m0,
        m1: m1,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 6997:
/*!****************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=script&lang=js& */ 6998);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 6998:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      recordId: '',
      resultData: {},
      activeTab: 'comprehensive',
      tongueResult: {},
      faceResult: {},
      sublingualResult: {},
      comprehensiveAnalysis: {},
      tongueAnalysis: {},
      faceAnalysis: {},
      sublingualAnalysis: {},
      careAdvice: {},
      recommendVideos: [],
      // 推荐视频列表
      showVideoPlayer: false,
      // 是否显示视频播放器
      currentVideo: {} // 当前播放的视频
    };
  },

  computed: {
    currentDate: function currentDate() {
      var now = new Date();
      return now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0') + '-' + String(now.getDate()).padStart(2, '0');
    },
    scoreLevelClass: function scoreLevelClass() {
      if (!this.resultData.comprehensive_score) return 'normal';
      var score = parseInt(this.resultData.comprehensive_score);
      if (score >= 80) return 'excellent';
      if (score >= 60) return 'good';
      if (score >= 40) return 'normal';
      return 'poor';
    },
    scoreLevelText: function scoreLevelText() {
      if (!this.resultData.comprehensive_score) return '正常';
      var score = parseInt(this.resultData.comprehensive_score);
      if (score >= 80) return '优秀';
      if (score >= 60) return '良好';
      if (score >= 40) return '正常';
      return '需要关注';
    }
  },
  onLoad: function onLoad(options) {
    if (options.recordId) {
      this.recordId = options.recordId;
      this.getAnalysisRecord();
    }
  },
  methods: {
    // 获取分析记录 - 2025-07-17 修改为使用新的综合诊疗接口
    getAnalysisRecord: function getAnalysisRecord() {
      var _this = this;
      console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_001] 开始获取综合诊疗分析记录');
      var app = getApp();
      uni.showLoading({
        title: '加载中...'
      });

      // 2025-07-17 使用新的综合诊疗接口获取记录
      app.post('ApiComprehensiveAnalysis/getRecord', {
        id: this.recordId
      }, function (response) {
        uni.hideLoading();
        console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_002] 获取综合诊疗记录结果:', response);
        if (response && response.code === 1) {
          console.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_003] 获取记录成功，开始解析数据');
          _this.resultData = response.data;
          _this.parseNewAnalysisResult();
        } else {
          console.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_004] 获取记录失败:', response === null || response === void 0 ? void 0 : response.msg);
          uni.showToast({
            title: (response === null || response === void 0 ? void 0 : response.msg) || '获取记录失败',
            icon: 'none'
          });

          // 降级到模拟数据，确保页面能正常显示
          _this.loadMockData();
        }
      }, function (error) {
        uni.hideLoading();
        console.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_005] 获取记录接口调用失败:', error);
        uni.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });

        // 降级到模拟数据，确保页面能正常显示
        _this.loadMockData();
      });
    },
    // 2025-07-17 新增：解析新综合诊疗接口的分析结果
    parseNewAnalysisResult: function parseNewAnalysisResult() {
      try {
        console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_001] 开始解析新综合诊疗分析结果');
        console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_002] 原始数据:', this.resultData);

        // 从新接口数据中提取信息
        var analysisResult = this.resultData.analysis_result || {};
        var analysisData = {};

        // 处理 analysis_result 数据
        if (typeof analysisResult === 'string') {
          analysisData = JSON.parse(analysisResult);
        } else if ((0, _typeof2.default)(analysisResult) === 'object') {
          analysisData = analysisResult;
        }
        console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_003] 解析后的分析数据:', analysisData);

        // 检查是否有新接口的数据结构
        if (analysisData.raw_report_data) {
          console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_004] 发现新接口数据结构');
          this.parseNewApiData(analysisData.raw_report_data);
        } else if (analysisData.physique_name || analysisData.score) {
          console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_005] 发现API数据结构');
          this.parseNewApiData(analysisData);
        } else {
          console.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_006] 使用旧版解析方法');
          this.parseAnalysisResult();
          return;
        }
      } catch (error) {
        console.error('2025-07-17 ERROR-[comprehensive-result][parseNewAnalysisResult_007] 解析新综合诊疗结果失败:', error);
        // 回退到旧版解析方法
        this.parseAnalysisResult();
      }
    },
    // 2025-07-17 新增：解析新API数据
    parseNewApiData: function parseNewApiData(apiData) {
      console.log('2025-07-17 INFO-[comprehensive-result][parseNewApiData_001] 开始解析新API数据:', apiData);

      // 更新基础信息
      if (apiData.score) {
        this.resultData.comprehensive_score = apiData.score;
      }

      // 解析特征数据 - 分类处理舌部、面部、舌下特征
      if (apiData.features && Array.isArray(apiData.features)) {
        this.parseNewComprehensiveFeatures(apiData.features);
      } else {
        this.parseDefaultFeatures();
      }

      // 解析综合健康分析
      this.parseNewComprehensiveAnalysis(apiData);

      // 解析调理建议 - 从 advices 对象中提取
      if (apiData.advices) {
        this.parseNewComprehensiveCareAdvice(apiData.advices);
      } else {
        this.parseDefaultCareAdvice();
      }
    },
    // 2025-07-17 新增：解析新API的综合特征
    parseNewComprehensiveFeatures: function parseNewComprehensiveFeatures(features) {
      console.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveFeatures_001] 解析综合特征:', features);

      // 分类处理不同部位的特征
      var tongueFeatures = features.filter(function (f) {
        return f.feature_category === '舌部';
      });
      var faceFeatures = features.filter(function (f) {
        return f.feature_category === '面部';
      });
      var sublingualFeatures = features.filter(function (f) {
        return f.feature_category === '舌下';
      });

      // 解析舌诊结果
      if (this.resultData.tongue_image && tongueFeatures.length > 0) {
        var tongueScore = this.calculateFeatureScore(tongueFeatures);
        this.tongueResult = {
          constitution_type: this.getConstitutionFromFeatures(tongueFeatures),
          constitution_score: tongueScore
        };
        this.tongueAnalysis = {
          detailed_analysis: this.generateFeatureAnalysis(tongueFeatures, '舌诊')
        };
      }

      // 解析面诊结果
      if (this.resultData.face_image && faceFeatures.length > 0) {
        var faceScore = this.calculateFeatureScore(faceFeatures);
        this.faceResult = {
          face_status: this.getFaceStatusFromFeatures(faceFeatures),
          face_score: faceScore
        };
        this.faceAnalysis = {
          detailed_analysis: this.generateFeatureAnalysis(faceFeatures, '面诊')
        };
      }

      // 解析舌下脉络结果
      if (this.resultData.sublingual_image && sublingualFeatures.length > 0) {
        var sublingualScore = this.calculateFeatureScore(sublingualFeatures);
        this.sublingualResult = {
          vein_status: this.getVeinStatusFromFeatures(sublingualFeatures),
          vein_score: sublingualScore
        };
        this.sublingualAnalysis = {
          detailed_analysis: this.generateFeatureAnalysis(sublingualFeatures, '舌下脉络')
        };
      }

      // 获取推荐视频
      this.getRecommendVideos();
    },
    // 2025-07-17 新增：解析新API的综合健康分析
    parseNewComprehensiveAnalysis: function parseNewComprehensiveAnalysis(apiData) {
      var score = apiData.score || 85;
      var physiqueName = apiData.physique_name || '未知体质';
      var physiqueAnalysis = apiData.physique_analysis || '体质分析中...';
      var riskWarning = apiData.risk_warning || '请注意保持健康的生活方式';
      this.comprehensiveAnalysis = {
        overall_health: physiqueAnalysis,
        constitution_type: physiqueName,
        constitution_desc: this.getConstitutionDescription(physiqueName),
        health_advice: riskWarning
      };
    },
    // 2025-07-17 新增：解析新API的综合调理建议
    parseNewComprehensiveCareAdvice: function parseNewComprehensiveCareAdvice(advices) {
      console.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveCareAdvice_001] 解析综合调理建议:', advices);

      // 饮食建议
      var dietAdvice = '保持均衡饮食，多吃新鲜蔬果';
      if (advices.food && Array.isArray(advices.food)) {
        dietAdvice = advices.food.map(function (item) {
          return item.advice || item.title;
        }).join('；');
      }

      // 运动建议
      var exerciseAdvice = '适当进行有氧运动，增强体质';
      if (advices.sport && Array.isArray(advices.sport)) {
        exerciseAdvice = advices.sport.map(function (item) {
          return item.advice || item.title;
        }).join('；');
      }

      // 生活建议
      var sleepAdvice = '保持规律作息，早睡早起';
      if (advices.sleep && Array.isArray(advices.sleep)) {
        sleepAdvice = advices.sleep.map(function (item) {
          return item.advice || item.title;
        }).join('；');
      }
      this.careAdvice = {
        diet: dietAdvice,
        exercise: exerciseAdvice,
        sleep: sleepAdvice
      };
    },
    // 2025-07-17 新增：计算特征评分
    calculateFeatureScore: function calculateFeatureScore(features) {
      if (!features || features.length === 0) return 85;
      var normalCount = 0;
      features.forEach(function (feature) {
        if (feature.feature_situation === '正常') {
          normalCount++;
        }
      });
      var normalRatio = normalCount / features.length;
      return Math.round(60 + normalRatio * 40); // 60-100分范围
    },
    // 2025-07-17 新增：从特征获取体质类型
    getConstitutionFromFeatures: function getConstitutionFromFeatures(features) {
      // 简单的体质判断逻辑，可以根据实际需要完善
      var abnormalFeatures = features.filter(function (f) {
        return f.feature_situation === '异常';
      });
      if (abnormalFeatures.length === 0) return '平和质';
      if (abnormalFeatures.length <= 2) return '偏颇体质';
      return '需要调理';
    },
    // 2025-07-17 新增：从面部特征获取面部状态
    getFaceStatusFromFeatures: function getFaceStatusFromFeatures(features) {
      var abnormalFeatures = features.filter(function (f) {
        return f.feature_situation === '异常';
      });
      if (abnormalFeatures.length === 0) return '气血充足';
      if (abnormalFeatures.length <= 1) return '气血较好';
      return '需要调理';
    },
    // 2025-07-17 新增：从舌下特征获取脉络状态
    getVeinStatusFromFeatures: function getVeinStatusFromFeatures(features) {
      var abnormalFeatures = features.filter(function (f) {
        return f.feature_situation === '异常';
      });
      if (abnormalFeatures.length === 0) return '血液循环良好';
      if (abnormalFeatures.length <= 1) return '血液循环较好';
      return '血液循环需要改善';
    },
    // 2025-07-17 新增：生成特征分析文本
    generateFeatureAnalysis: function generateFeatureAnalysis(features, type) {
      var normalFeatures = features.filter(function (f) {
        return f.feature_situation === '正常';
      });
      var abnormalFeatures = features.filter(function (f) {
        return f.feature_situation === '异常';
      });
      var analysis = "".concat(type, "\u663E\u793A\uFF1A");
      if (normalFeatures.length > 0) {
        var normalNames = normalFeatures.map(function (f) {
          return f.feature_name;
        }).join('、');
        analysis += "".concat(normalNames, "\u6B63\u5E38\uFF1B");
      }
      if (abnormalFeatures.length > 0) {
        var abnormalNames = abnormalFeatures.map(function (f) {
          return f.feature_name;
        }).join('、');
        analysis += "".concat(abnormalNames, "\u9700\u8981\u5173\u6CE8\u3002");
      } else {
        analysis += '整体状况良好。';
      }
      return analysis;
    },
    // 2025-07-17 新增：获取体质描述
    getConstitutionDescription: function getConstitutionDescription(constitutionType) {
      var descriptions = {
        '平和质': '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
        '气虚质': '元气不足，以疲乏、气短、自汗等气虚表现为主要特征。',
        '阳虚质': '阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。',
        '阴虚质': '阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。',
        '痰湿质': '痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。',
        '湿热质': '湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。',
        '血瘀质': '血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。',
        '气郁质': '气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。',
        '特禀质': '先天失常，以生理缺陷、过敏反应等为主要特征。'
      };
      return descriptions[constitutionType] || '体质特征分析中，请咨询专业医师。';
    },
    // 2025-07-17 新增：加载模拟数据
    loadMockData: function loadMockData() {
      var _this$resultData, _this$resultData2, _this$resultData3;
      this.resultData = {
        comprehensive_score: 85,
        tongue_image: ((_this$resultData = this.resultData) === null || _this$resultData === void 0 ? void 0 : _this$resultData.tongue_image) || '',
        face_image: ((_this$resultData2 = this.resultData) === null || _this$resultData2 === void 0 ? void 0 : _this$resultData2.face_image) || '',
        sublingual_image: ((_this$resultData3 = this.resultData) === null || _this$resultData3 === void 0 ? void 0 : _this$resultData3.sublingual_image) || ''
      };
      this.parseDefaultFeatures();
      this.parseDefaultCareAdvice();
    },
    // 2025-07-17 新增：解析默认特征
    parseDefaultFeatures: function parseDefaultFeatures() {
      if (this.resultData.tongue_image) {
        this.tongueResult = {
          constitution_type: '平和质',
          constitution_score: 85
        };
        this.tongueAnalysis = {
          detailed_analysis: '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'
        };
      }
      if (this.resultData.face_image) {
        this.faceResult = {
          face_status: '气血充足',
          face_score: 82
        };
        this.faceAnalysis = {
          detailed_analysis: '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'
        };
      }
      if (this.resultData.sublingual_image) {
        this.sublingualResult = {
          vein_status: '血液循环良好',
          vein_score: 88
        };
        this.sublingualAnalysis = {
          detailed_analysis: '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'
        };
      }
      this.comprehensiveAnalysis = {
        overall_health: '根据综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',
        constitution_type: '平和质',
        constitution_desc: '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
        health_advice: '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'
      };
    },
    // 2025-07-17 新增：解析默认调理建议
    parseDefaultCareAdvice: function parseDefaultCareAdvice() {
      this.careAdvice = {
        diet: '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',
        sleep: '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',
        exercise: '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'
      };
    },
    // 解析分析结果（保留旧版本兼容）
    parseAnalysisResult: function parseAnalysisResult() {
      try {
        // 解析主要分析结果
        var analysisData = JSON.parse(this.resultData.analysis_result || '{}');

        // 解析舌诊结果
        if (this.resultData.tongue_image) {
          this.tongueResult = {
            constitution_type: analysisData.constitution_type || '平和质',
            constitution_score: analysisData.constitution_score || 85
          };
          this.tongueAnalysis = {
            detailed_analysis: analysisData.tongue_detailed || '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'
          };
        }

        // 解析面诊结果
        if (this.resultData.face_image) {
          this.faceResult = {
            face_status: analysisData.face_status || '气血充足',
            face_score: analysisData.face_score || 82
          };
          this.faceAnalysis = {
            detailed_analysis: analysisData.face_detailed || '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'
          };
        }

        // 解析舌下脉络结果
        if (this.resultData.sublingual_image) {
          this.sublingualResult = {
            vein_status: analysisData.vein_status || '血液循环良好',
            vein_score: analysisData.vein_score || 88
          };
          this.sublingualAnalysis = {
            detailed_analysis: analysisData.sublingual_detailed || '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'
          };
        }

        // 解析综合分析
        this.comprehensiveAnalysis = {
          overall_health: analysisData.overall_health || '根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',
          constitution_type: analysisData.comprehensive_constitution || '平和质',
          constitution_desc: analysisData.comprehensive_desc || '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',
          health_advice: analysisData.comprehensive_advice || '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'
        };

        // 解析调理建议
        this.careAdvice = {
          diet: analysisData.diet_advice || '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',
          sleep: analysisData.sleep_advice || '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',
          exercise: analysisData.exercise_advice || '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'
        };
      } catch (error) {
        console.error('解析分析结果失败:', error);
      }
    },
    // 切换标签
    switchTab: function switchTab(tab) {
      this.activeTab = tab;
    },
    // 返回
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 分享结果
    shareResult: function shareResult() {
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    // 获取推荐视频
    getRecommendVideos: function getRecommendVideos() {
      var _this2 = this;
      console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_001] 开始获取推荐视频');

      // 获取体质类型和得分
      var constitutionType = '';
      var constitutionScore = 0;
      if (this.tongueResult && this.tongueResult.constitution_type) {
        constitutionType = this.tongueResult.constitution_type;
      }
      if (this.tongueResult && this.tongueResult.constitution_score) {
        constitutionScore = parseInt(this.tongueResult.constitution_score);
      }

      // 如果没有体质信息，不获取推荐视频
      if (!constitutionType && !constitutionScore) {
        console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_002] 无体质信息，跳过视频推荐');
        return;
      }
      var app = getApp();

      // 调用推荐视频接口
      app.post('ApiSheZhen/getRecommendProducts', {
        constitution_type: constitutionType,
        constitution_score: constitutionScore
      }, function (response) {
        console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_003] 获取推荐视频结果:', response);
        if (response && response.code === 1 && response.data && response.data.products) {
          // 筛选出视频类型的推荐
          var videos = response.data.products.filter(function (item) {
            return item.type === 'video';
          });
          _this2.recommendVideos = videos;
          console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);
        } else {
          console.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_005] 无推荐视频数据');
        }
      }, function (error) {
        console.error('2025-01-31 ERROR-[comprehensive-result][getRecommendVideos_006] 获取推荐视频失败:', error);
      });
    },
    // 播放视频
    playVideo: function playVideo(video) {
      console.log('2025-01-31 INFO-[comprehensive-result][playVideo_001] 播放视频:', video);
      if (!video.url) {
        uni.showToast({
          title: '视频地址无效',
          icon: 'none'
        });
        return;
      }

      // 处理视频URL，确保中文字符正确编码
      var processedVideo = _objectSpread({}, video);
      if (video.url && video.url.includes('%')) {
        // URL已经编码，直接使用
        processedVideo.url = video.url;
      } else if (video.url) {
        // 对URL中的中文字符进行编码
        try {
          var urlParts = video.url.split('/');
          var encodedParts = urlParts.map(function (part) {
            // 只对文件名部分进行编码，保留协议和域名
            if (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {
              return encodeURIComponent(part);
            }
            return part;
          });
          processedVideo.url = encodedParts.join('/');
          console.log('2025-01-31 INFO-[comprehensive-result][playVideo_001.5] URL编码处理:', {
            original: video.url,
            processed: processedVideo.url
          });
        } catch (e) {
          console.warn('2025-01-31 WARN-[comprehensive-result][playVideo_001.6] URL编码失败，使用原始URL:', e);
          processedVideo.url = video.url;
        }
      }

      // 设置当前播放视频并显示播放器
      this.currentVideo = processedVideo;
      this.showVideoPlayer = true;

      // 记录视频播放事件
      console.log('2025-01-31 INFO-[comprehensive-result][playVideo_002] 开始播放视频:', video.name);
      console.log('2025-01-31 INFO-[comprehensive-result][playVideo_003] 视频URL:', processedVideo.url);
    },
    // 关闭视频播放器
    closeVideoPlayer: function closeVideoPlayer() {
      console.log('2025-01-31 INFO-[comprehensive-result][closeVideoPlayer_001] 关闭视频播放器');
      this.showVideoPlayer = false;
      this.currentVideo = {};
    },
    // 视频播放错误处理
    onVideoError: function onVideoError(e) {
      console.error('2025-01-31 ERROR-[comprehensive-result][onVideoError_001] 视频播放错误:', e);
      uni.showToast({
        title: '视频播放失败',
        icon: 'none'
      });
    },
    // 视频开始播放
    onVideoPlay: function onVideoPlay(e) {
      console.log('2025-01-31 INFO-[comprehensive-result][onVideoPlay_001] 视频开始播放:', e);
    },
    // 视频暂停播放
    onVideoPause: function onVideoPause(e) {
      console.log('2025-01-31 INFO-[comprehensive-result][onVideoPause_001] 视频暂停播放:', e);
    },
    // 视频开始加载
    onVideoLoadStart: function onVideoLoadStart(e) {
      console.log('2025-01-31 INFO-[comprehensive-result][onVideoLoadStart_001] 视频开始加载:', e);
    },
    // 视频可以播放
    onVideoCanPlay: function onVideoCanPlay(e) {
      console.log('2025-01-31 INFO-[comprehensive-result][onVideoCanPlay_001] 视频可以播放:', e);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 6999:
/*!************************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=style&index=0&lang=css& ***!
  \************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./result.vue?vue&type=style&index=0&lang=css& */ 7000);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_result_vue_vue_type_style_index_0_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 7000:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?vue&type=style&index=0&lang=css& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[6993,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesB/diagnosis/comprehensive/result.js.map