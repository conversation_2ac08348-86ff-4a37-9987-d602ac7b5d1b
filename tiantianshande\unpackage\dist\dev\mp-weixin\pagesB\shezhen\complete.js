require('../common/vendor.js');(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesB/shezhen/complete"],{

/***/ 6937:
/*!********************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/main.js?{"page":"pagesB%2Fshezhen%2Fcomplete"} ***!
  \********************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _complete = _interopRequireDefault(__webpack_require__(/*! ./pagesB/shezhen/complete.vue */ 6938));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_complete.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 6938:
/*!*************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue ***!
  \*************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./complete.vue?vue&type=template&id=39446614&scoped=true& */ 6939);
/* harmony import */ var _complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./complete.vue?vue&type=script&lang=js& */ 6941);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css& */ 6943);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 34);

var renderjs





/* normalize component */

var component = Object(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "39446614",
  null,
  false,
  _complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesB/shezhen/complete.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 6939:
/*!********************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=template&id=39446614&scoped=true& ***!
  \********************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=template&id=39446614&scoped=true& */ 6940);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_template_id_39446614_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 6940:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=template&id=39446614&scoped=true& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = !!_vm.displaySettings.show_symptoms
    ? _vm.typicalSymptoms.length
    : null
  var l0 = !!_vm.displaySettings.show_tongue_analysis
    ? _vm.__map(_vm.displayedTongueFeatures, function (feature, index) {
        var $orig = _vm.__get_orig(feature)
        var m0 = _vm.getFeaturePositionClass(feature.feature_group)
        return {
          $orig: $orig,
          m0: m0,
        }
      })
    : null
  var g1 = !!_vm.displaySettings.show_tongue_analysis
    ? _vm.tongueFeatures.length
    : null
  var g2 = !!_vm.displaySettings.show_symptoms
    ? _vm.abnormalFeatures.length
    : null
  var g3 = !!_vm.displaySettings.show_symptoms
    ? _vm.abnormalFeatures.length
    : null
  var g4 =
    !!_vm.displaySettings.show_care_advice &&
    _vm.careSuggestions &&
    Object.keys(_vm.careSuggestions).length > 0
  var g5 = g4
    ? _vm.careSuggestions.food && _vm.careSuggestions.food.length > 0
    : null
  var g6 = g4
    ? _vm.careSuggestions.sport && _vm.careSuggestions.sport.length > 0
    : null
  var g7 = g4
    ? _vm.careSuggestions.sleep && _vm.careSuggestions.sleep.length > 0
    : null
  var g8 = g4
    ? _vm.careSuggestions.music && _vm.careSuggestions.music.length > 0
    : null
  var g9 = g4
    ? _vm.careSuggestions.treatment && _vm.careSuggestions.treatment.length > 0
    : null
  var g10 =
    !!_vm.displaySettings.show_product_recommend &&
    _vm.recommendProducts &&
    _vm.recommendProducts.length > 0
  var g11 = _vm.recommendVideos.length > 0 || true
  var g12 = g11 ? _vm.recommendVideos.length : null
  var g13 = g11 && g12 === 0 ? _vm.recommendVideos.length : null
  var g14 = g11 ? _vm.recommendVideos.length : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        g5: g5,
        g6: g6,
        g7: g7,
        g8: g8,
        g9: g9,
        g10: g10,
        g11: g11,
        g12: g12,
        g13: g13,
        g14: g14,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 6941:
/*!**************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=script&lang=js& ***!
  \**************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=script&lang=js& */ 6942);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 6942:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _typeof2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/typeof */ 13));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _toConsumableArray2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/toConsumableArray */ 18));
var _methods;
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      // 2025-01-03 22:55:53,565-INF0-[complete][data_001] 初始化页面数据
      recordId: 0,
      analysisData: null,
      isLoading: true,
      loadingText: '分析中...',
      // 健康得分和体质信息
      healthScore: 89,
      constitutionType: '湿热体质、血瘀体质',
      mainSymptom: '水湿',
      subSymptom: '脾胃虚',
      // 2025-01-27 新增前端显示设置 - 控制各模块显示状态
      displaySettings: {
        show_score: 1,
        // 是否显示评分，默认显示
        show_score_value: 1,
        // 2025-01-27 是否显示评分分值，默认显示
        show_symptoms: 1,
        // 是否显示体征，默认显示
        show_tongue_analysis: 1,
        // 是否显示舌象分析，默认显示
        show_care_advice: 1,
        // 是否显示调理建议，默认显示
        show_product_recommend: 1 // 是否显示商品推荐，默认显示
      },

      // 舌象特征数据
      tongueFeatures: [],
      abnormalFeatures: [],
      // 体征症状
      typicalSymptoms: [],
      // 风险评估数据
      riskData: [],
      // 警告列表
      warningList: [],
      // 饮食建议
      dietSuggestions: [],
      // 运动建议
      exerciseSuggestions: [],
      // 健康建议
      healthSuggestions: '',
      // 保养建议数据
      careSuggestions: {},
      // 推荐商品数据 - 新增
      recommendProducts: [],
      recommendTitle: '',
      // 推荐视频数据 - 新增
      recommendVideos: [],
      showVideoPlayer: false,
      // 是否显示视频播放器
      currentVideo: {},
      // 当前播放的视频

      // 营养目标数据
      nutritionData: {
        carbohydrate: {
          percent: 62,
          amount: 385
        },
        protein: {
          percent: 10,
          amount: 62
        },
        fat: {
          percent: 28,
          amount: 77
        },
        totalCalories: 2484,
        nutritionItems: [{
          name: '碳水化物',
          percent: 62,
          amount: 385,
          color: '#8884d8'
        }, {
          name: '蛋白质',
          percent: 10,
          amount: 62,
          color: '#82ca9d'
        }, {
          name: '脂肪',
          percent: 28,
          amount: 77,
          color: '#ffc658'
        }]
      },
      // 今日膳方数据
      recipeData: {
        name: '黄酒煮鸡',
        effect: '温中养血，散寒通络',
        image: '/static/images/huangjiu-chicken.png'
      },
      // 体质分析数据
      constitutionData: {
        type: '平和',
        position: {
          x: -20,
          y: 15
        },
        statusColor: '#52c41a',
        statusText: '体质改善',
        description: '通过健康天平分析得出，您的身体偏寒且正气偏虚，处于痰湿状态。',
        explanation: '健康天平圆点越靠近中心身体越健康，越偏离中心健康状态越严重。',
        note: '注：「健康天平」由省中医院副院长杨志敏教授带领的健康辨识团队运用中医健康辨识体系，结合经络脏腑阴阳五行、气血津液等理论得出。'
      },
      // 圆形刻度标记数据
      scaleMarks: function () {
        var marks = [];
        for (var i = 0; i < 24; i++) {
          var angle = i * 15;
          var isMainMark = i % 6 === 0;
          marks.push({
            angle: angle,
            opacity: isMainMark ? 0.3 : 0.1
          });
        }
        return marks;
      }(),
      // 分析接口相关
      analysisApiCompleted: false,
      orderNo: '',
      apiType: '',
      // 2025-01-27 新增支付订单ID
      orderId: ''
    };
  },
  computed: {
    // 2025-01-03 22:55:53,565-INF0-[complete][computed_001] 动态计算饼状图样式
    pieChartStyle: function pieChartStyle() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][computed_002] 生成饼状图样式');
      return {
        background: this.calculatePieChartGradient(this.nutritionData.nutritionItems)
      };
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][computed_003] 动态计算风险进度条样式
    riskProgressStyles: function riskProgressStyles() {
      return this.riskData.map(function (item) {
        return {
          background: "conic-gradient(#ff6b6b 0% ".concat(item.percentage, "%, #f0f0f0 ").concat(item.percentage, "% 100%)")
        };
      });
    },
    // 体质数据
    constitutionData: function constitutionData() {
      return {
        type: this.constitutionType || '平和体质',
        position: {
          x: 30,
          y: -20
        },
        // 根据体质类型调整位置
        statusColor: '#ff9a9e',
        statusText: '偏热',
        description: this.healthSuggestions || '您的体质整体较为平衡，建议保持良好的生活习惯。',
        explanation: '体质分析基于舌象特征、症状表现等多维度评估得出。',
        note: '以上分析仅供参考，具体调理方案请咨询专业医师。'
      };
    },
    // 刻度标记
    scaleMarks: function scaleMarks() {
      var marks = [];
      for (var i = 0; i < 12; i++) {
        marks.push({
          angle: i * 30,
          opacity: i % 3 === 0 ? 1 : 0.5
        });
      }
      return marks;
    },
    // 显示的舌象特征（优先显示异常的，最多6个）
    displayedTongueFeatures: function displayedTongueFeatures() {
      var _this = this;
      if (!this.tongueFeatures || this.tongueFeatures.length === 0) {
        return [];
      }

      // 优先选择异常特征
      var abnormalFeatures = this.tongueFeatures.filter(function (f) {
        return f.feature_situation === '异常';
      });
      var normalFeatures = this.tongueFeatures.filter(function (f) {
        return f.feature_situation === '正常';
      });

      // 最多显示6个，优先异常特征
      var displayed = (0, _toConsumableArray2.default)(abnormalFeatures);
      if (displayed.length < 6) {
        displayed = displayed.concat(normalFeatures.slice(0, 6 - displayed.length));
      } else {
        displayed = displayed.slice(0, 6);
      }

      // 预先计算每个特征的样式，避免在模板中调用函数
      return displayed.map(function (feature, index) {
        // 计算偏移样式
        var offset = _this.calculateOffsetForSamePosition(feature.feature_group, index, displayed.length);

        // 返回带有预计算样式的特征对象
        return _objectSpread(_objectSpread({}, feature), {}, {
          markerStyle: _objectSpread({
            zIndex: (10 + index).toString()
          }, offset)
        });
      });
    }
  },
  methods: (_methods = {
    // 2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 返回上一页功能
    goBack: function goBack() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 用户点击返回');
      uni.navigateBack();
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 查看体征解读详情功能
    viewDetailContent: function viewDetailContent() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 用户点击查看体征解读');
      // 需要VIP权限才能查看详细内容
      uni.showToast({
        title: '需要VIP权限',
        icon: 'none'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 查看调理原则详情功能
    viewPrincipleContent: function viewPrincipleContent() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 用户点击查看调理原则');
      // 需要VIP权限才能查看详细内容
      uni.showToast({
        title: '需要VIP权限',
        icon: 'none'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_001] 调整膳方计划
    adjustRecipePlan: function adjustRecipePlan() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_002] 用户点击调整膳方计划');
      console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_003] 当前膳方:', this.recipeData);
      uni.navigateTo({
        url: '/pages/recipe/adjust'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_001] 显示详细健康报告
    onShowHealthReport: function onShowHealthReport() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_002] 导航到健康详细报告');
      uni.navigateTo({
        url: '/pages/tongue/detail'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_003] 分享功能
    onShare: function onShare() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_004] 用户点击分享');
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_005] 查看营养详情
    onViewNutritionDetail: function onViewNutritionDetail() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_006] 导航到营养详情页面');
      uni.navigateTo({
        url: '/pages/nutrition/detail'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_007] 查看完整调理方案
    onViewCompleteRegulation: function onViewCompleteRegulation() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_008] 导航到调理方案页面');
      uni.navigateTo({
        url: '/pages/tongue/plan'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_009] 开始舌诊拍摄
    onStartTongueDetection: function onStartTongueDetection() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_010] 启动舌诊拍摄功能');
      uni.navigateTo({
        url: '/pages/tongue/camera'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_011] 计算饼状图角度
    calculatePieChartGradient: function calculatePieChartGradient(nutritionItems) {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 计算饼状图渐变');
      var cumulativePercent = 0;
      var segments = nutritionItems.map(function (item) {
        var startPercent = cumulativePercent;
        var endPercent = cumulativePercent + item.percent;
        cumulativePercent = endPercent;
        return "".concat(item.color, " ").concat(startPercent, "% ").concat(endPercent, "%");
      });
      return "conic-gradient(".concat(segments.join(', '), ")");
    },
    /**
     * 获取舌诊分析记录
     * 调用后端接口获取完整的舌诊分析数据
     */
    getAnalysisRecord: function getAnalysisRecord() {
      var _this2 = this;
      console.log('2025-01-06 11:45:00,002-INFO-[complete][getAnalysisRecord_001] 开始获取舌诊分析记录:', this.recordId);
      if (!this.recordId) {
        console.error('2025-01-06 11:45:00,003-ERROR-[complete][getAnalysisRecord_002] 记录ID为空');
        uni.showToast({
          title: '参数错误，请重新扫描',
          icon: 'none'
        });
        setTimeout(function () {
          uni.navigateBack();
        }, 2000);
        return;
      }
      uni.showLoading({
        title: '加载中...'
      });

      // 使用正确的getApp().post方法调用后端接口获取分析记录
      var app = getApp();
      app.post('ApiSheZhen/getRecord', {
        id: this.recordId
      }, function (response) {
        console.log('2025-01-06 11:45:00,004-INFO-[complete][getAnalysisRecord_003] API响应:', response);
        uni.hideLoading();
        if (response && (response.status === 1 || response.code === 1)) {
          console.log('2025-01-06 11:45:00,005-INFO-[complete][getAnalysisRecord_004] 获取分析记录成功');
          // 解析并设置分析数据
          _this2.parseAnalysisData(response.data);
        } else {
          console.error('2025-01-06 11:45:00,006-ERROR-[complete][getAnalysisRecord_005] 获取分析记录失败:', (response === null || response === void 0 ? void 0 : response.msg) || (response === null || response === void 0 ? void 0 : response.message));
          uni.showToast({
            title: (response === null || response === void 0 ? void 0 : response.msg) || (response === null || response === void 0 ? void 0 : response.message) || '获取分析记录失败',
            icon: 'none'
          });
          setTimeout(function () {
            uni.navigateBack();
          }, 2000);
        }
      }, function (err) {
        console.error('2025-01-06 11:45:00,007-ERROR-[complete][getAnalysisRecord_006] 获取分析记录异常:', err);
        uni.hideLoading();
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        });
        // 使用模拟数据进行测试
        _this2.loadSimulateData();
      });
    },
    /**
     * 解析舌诊分析数据
     * @param {Object} data 后端返回的分析数据
     */
    parseAnalysisData: function parseAnalysisData(data) {
      console.log('2025-01-06 11:45:00,008-INFO-[complete][parseAnalysisData_001] 开始解析分析数据');
      console.log('2025-01-06 11:45:00,008-DEBUG-[complete][parseAnalysisData_debug] 完整API数据:', JSON.stringify(data, null, 2));
      try {
        // 保存原始分析数据
        this.analysisData = data;

        // 2025-01-27 解析显示设置配置
        if (data.display_settings) {
          console.log('2025-01-27 11:45:00,008-INFO-[complete][parseAnalysisData_001] 原始显示设置数据:', JSON.stringify(data.display_settings));
          this.displaySettings = {
            show_score: data.display_settings.show_score !== undefined ? data.display_settings.show_score : 1,
            show_score_value: data.display_settings.show_score_value !== undefined ? data.display_settings.show_score_value : 1,
            // 2025-01-27 解析评分分值显示设置
            show_symptoms: data.display_settings.show_symptoms !== undefined ? data.display_settings.show_symptoms : 1,
            show_tongue_analysis: data.display_settings.show_tongue_analysis !== undefined ? data.display_settings.show_tongue_analysis : 1,
            show_care_advice: data.display_settings.show_care_advice !== undefined ? data.display_settings.show_care_advice : 1,
            show_product_recommend: data.display_settings.show_product_recommend !== undefined ? data.display_settings.show_product_recommend : 1
          };
          console.log('2025-01-27 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析后显示设置:', JSON.stringify(this.displaySettings));
          console.log('2025-01-27 11:45:00,010-INFO-[complete][parseAnalysisData_003] show_score_value类型和值:', (0, _typeof2.default)(data.display_settings.show_score_value), data.display_settings.show_score_value);
          console.log('2025-01-27 11:45:00,011-INFO-[complete][parseAnalysisData_004] 最终show_score_value:', this.displaySettings.show_score_value, (0, _typeof2.default)(this.displaySettings.show_score_value));
        }

        // 解析基本信息
        // 优先从report的raw_report_json中获取分数和体质类型，因为constitution_score可能为"0.00"
        var reportScore = 89; // 默认值
        var reportConstitutionType = ''; // 从报告中获取的体质类型
        if (data.report && data.report.raw_report_json) {
          try {
            var reportData = JSON.parse(data.report.raw_report_json);
            if (reportData.score) {
              reportScore = parseFloat(reportData.score);
            }
            if (reportData.physique_name) {
              reportConstitutionType = reportData.physique_name;
            }
          } catch (e) {
            console.warn('2025-01-31 WARN-[complete][parseAnalysisData_005] 解析报告JSON失败:', e);
          }
        }
        this.healthScore = reportScore || parseFloat(data.constitution_score) || 89;
        // 优先使用报告中的体质类型，然后是API返回的体质类型，最后是默认值
        this.constitutionType = reportConstitutionType || data.constitution_type || '湿热体质、血瘀体质';
        console.log('2025-01-31 INFO-[complete][parseAnalysisData_006] 解析后的基本信息:', {
          healthScore: this.healthScore,
          constitutionType: this.constitutionType,
          originalScore: data.constitution_score,
          originalType: data.constitution_type,
          reportScore: reportScore,
          reportConstitutionType: reportConstitutionType
        });

        // 额外调试：检查体质类型是否包含"未知"
        if (this.constitutionType && this.constitutionType.includes('未知')) {
          console.warn('2025-01-31 WARN-[complete][parseAnalysisData_007] 检测到体质类型包含"未知":', this.constitutionType);
        }

        // 调试：输出原始报告数据
        if (data.report && data.report.raw_report_json) {
          console.log('2025-01-31 INFO-[complete][parseAnalysisData_008] 原始报告数据片段:', data.report.raw_report_json.substring(0, 500));
        }

        // 解析典型症状
        if (data.typical_symptoms) {
          this.typicalSymptoms = data.typical_symptoms.split('；').filter(function (symptom) {
            return symptom.trim();
          });
          console.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析典型症状:', this.typicalSymptoms);
        } else if (data.analysis_result && data.analysis_result.typical_symptom) {
          // 处理嵌套在analysis_result中的typical_symptom字段
          this.typicalSymptoms = data.analysis_result.typical_symptom.split('；').filter(function (symptom) {
            return symptom.trim();
          });
          console.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 从analysis_result解析典型症状:', this.typicalSymptoms);
        } else {
          console.log('2025-01-06 11:45:00,009-WARN-[complete][parseAnalysisData_002] 未找到典型症状数据');
          this.typicalSymptoms = [];
        }

        // 解析舌象特征
        this.parseTongueFeatures(data);

        // 解析饮食建议
        this.parseDietSuggestions(data);

        // 解析运动建议
        this.parseExerciseSuggestions(data);

        // 解析健康建议
        this.healthSuggestions = data.health_suggestions || data.constitution_analysis || '';

        // 解析风险评估
        this.parseRiskAssessment(data);

        // 解析保养建议数据（新增）
        this.parseCareSuggestions(data);

        // 解析推荐商品数据（新增）
        this.parseRecommendProducts(data);

        // 获取推荐视频（新增）
        this.getRecommendVideos();

        // 解析报告数据
        if (data.report && data.report.raw_report_json) {
          this.parseReportData(data.report.raw_report_json);
        }
        console.log('2025-01-06 11:45:00,010-INFO-[complete][parseAnalysisData_003] 分析数据解析完成');
      } catch (error) {
        console.error('2025-01-06 11:45:00,011-ERROR-[complete][parseAnalysisData_004] 解析分析数据异常:', error);
        uni.showToast({
          title: '数据解析失败',
          icon: 'none'
        });
      }
    },
    /**
     * 解析舌象特征数据
     */
    parseTongueFeatures: function parseTongueFeatures(data) {
      console.log('2025-01-06 11:45:00,012-INFO-[complete][parseTongueFeatures_001] 解析舌象特征');
      try {
        if (data.tongue_features) {
          var features = typeof data.tongue_features === 'string' ? JSON.parse(data.tongue_features) : data.tongue_features;
          if (Array.isArray(features)) {
            this.tongueFeatures = features;
            // 提取异常特征
            this.abnormalFeatures = features.filter(function (feature) {
              return feature.situation === '异常' && feature.interpret;
            });
            console.log('2025-01-06 11:45:00,013-INFO-[complete][parseTongueFeatures_002] 异常特征:', this.abnormalFeatures.length);
          }
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,014-ERROR-[complete][parseTongueFeatures_003] 解析舌象特征失败:', error);
      }
    },
    /**
     * 解析饮食建议
     */
    parseDietSuggestions: function parseDietSuggestions(data) {
      console.log('2025-01-06 11:45:00,015-INFO-[complete][parseDietSuggestions_001] 解析饮食建议');
      try {
        if (data.diet_suggestions) {
          var suggestions = typeof data.diet_suggestions === 'string' ? JSON.parse(data.diet_suggestions) : data.diet_suggestions;
          if (Array.isArray(suggestions)) {
            this.dietSuggestions = suggestions;
            console.log('2025-01-06 11:45:00,016-INFO-[complete][parseDietSuggestions_002] 饮食建议数量:', suggestions.length);
          }
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,017-ERROR-[complete][parseDietSuggestions_003] 解析饮食建议失败:', error);
      }
    },
    /**
     * 解析运动建议
     */
    parseExerciseSuggestions: function parseExerciseSuggestions(data) {
      console.log('2025-01-06 11:45:00,018-INFO-[complete][parseExerciseSuggestions_001] 解析运动建议');
      try {
        if (data.exercise_suggestions) {
          var suggestions = typeof data.exercise_suggestions === 'string' ? JSON.parse(data.exercise_suggestions) : data.exercise_suggestions;
          if (Array.isArray(suggestions)) {
            this.exerciseSuggestions = suggestions;
            console.log('2025-01-06 11:45:00,019-INFO-[complete][parseExerciseSuggestions_002] 运动建议数量:', suggestions.length);
          }
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,020-ERROR-[complete][parseExerciseSuggestions_003] 解析运动建议失败:', error);
      }
    },
    /**
     * 解析风险评估数据
     */
    parseRiskAssessment: function parseRiskAssessment(data) {
      var _this3 = this;
      console.log('2025-01-06 11:45:00,021-INFO-[complete][parseRiskAssessment_001] 解析风险评估');
      try {
        // 从风险评估文本中提取风险项目
        if (data.risk_assessment) {
          var riskText = data.risk_assessment;
          var riskLines = riskText.split('\n').filter(function (line) {
            return line.includes('：');
          });
          this.riskData = riskLines.map(function (line, index) {
            var name = line.split('：')[0].replace(/^\d+\./, '').trim();
            // 根据内容严重程度估算风险百分比
            var severity = _this3.calculateRiskPercentage(line);
            return {
              name: name,
              percentage: severity
            };
          }).slice(0, 6); // 最多显示6个风险项

          console.log('2025-01-06 11:45:00,022-INFO-[complete][parseRiskAssessment_002] 风险数据:', this.riskData.length);
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,023-ERROR-[complete][parseRiskAssessment_003] 解析风险评估失败:', error);
      }
    },
    /**
     * 计算风险百分比
     */
    calculateRiskPercentage: function calculateRiskPercentage(riskText) {
      // 根据关键词估算风险程度
      var highRiskKeywords = ['严重', '高风险', '心血管', '糖尿病'];
      var mediumRiskKeywords = ['可能', '容易', '影响'];
      var lowRiskKeywords = ['轻微', '注意'];
      var percentage = 15; // 基础风险值

      if (highRiskKeywords.some(function (keyword) {
        return riskText.includes(keyword);
      })) {
        percentage += 25;
      } else if (mediumRiskKeywords.some(function (keyword) {
        return riskText.includes(keyword);
      })) {
        percentage += 15;
      } else if (lowRiskKeywords.some(function (keyword) {
        return riskText.includes(keyword);
      })) {
        percentage += 5;
      }
      return Math.min(percentage, 60); // 最高不超过60%
    },
    /**
     * 解析报告JSON数据
     */
    parseReportData: function parseReportData(rawReportJson) {
      console.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 解析报告JSON数据');
      try {
        var reportData = typeof rawReportJson === 'string' ? JSON.parse(rawReportJson) : rawReportJson;

        // 解析典型症状（优先从reportData中获取）
        if (reportData.typical_symptom && this.typicalSymptoms.length === 0) {
          this.typicalSymptoms = reportData.typical_symptom.split('；').filter(function (symptom) {
            return symptom.trim();
          });
          console.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 从报告数据解析典型症状:', this.typicalSymptoms);
        }

        // 解析更详细的特征数据
        if (reportData.features && Array.isArray(reportData.features)) {
          this.tongueFeatures = reportData.features;
          this.abnormalFeatures = reportData.features.filter(function (feature) {
            return feature.feature_situation === '异常';
          });
        }

        // 解析建议数据
        if (reportData.advices) {
          // 保存完整的保养建议数据
          this.careSuggestions = reportData.advices;
          console.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 解析保养建议:', this.careSuggestions);
          if (reportData.advices.food) {
            this.dietSuggestions = reportData.advices.food;
          }
          if (reportData.advices.sport) {
            this.exerciseSuggestions = reportData.advices.sport;
          }
        }
        console.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 报告数据解析完成');
      } catch (error) {
        console.error('2025-01-06 11:45:00,026-ERROR-[complete][parseReportData_003] 解析报告数据失败:', error);
      }
    },
    /**
     * 加载模拟数据用于测试
     */
    loadSimulateData: function loadSimulateData() {
      console.log('2025-01-06 11:45:00,027-INFO-[complete][loadSimulateData_001] 加载模拟保养建议数据');

      // 模拟保养建议数据
      this.careSuggestions = {
        food: [{
          advice: "避免高盐、高糖、高脂、火锅、烧烤等饮食。",
          title: "禁忌饮食"
        }, {
          advice: "宜食性平之物，少食寒凉之物，如螃蟹、西瓜、苦瓜、冬瓜、梨、绿豆、冷饮等。",
          title: "建议饮食"
        }],
        music: [{
          advice: "音乐疗法：道教音乐崇尚'中和'的审美特征，具体来说体现为'阴、阳调和'、'动、静结合'和'散、正相间'等方面，常听这类曲目能让体内脏腑、气血平衡，使人心情愉快，精神饱满；改善睡眠；增强抗压能力等。其代表性曲目有：《啸咏朱陵府》《卫灵咒》《华夏颂》等。",
          title: "音乐建议"
        }],
        sleep: [{
          advice: "注意休息，劳逸结合，不可过劳，顺应四时，遵循春生、夏长、秋收、冬藏的规律，春夏应夜卧早起，秋季应早卧早起，冬季应早卧晚起。",
          title: "生活建议"
        }, {
          advice: "保持情志舒畅，减少思虑，多与人沟通交流。",
          title: "情志建议"
        }, {
          advice: "病室宜安静、舒适，有良好的通风环境。",
          title: "居住环境建议"
        }],
        sport: [{
          advice: "避免久坐、久站、久卧。成人可适当于上午时刻（避寒冷、避炎热）参与如舞蹈、气功、太极拳、八段锦、五禽戏等运动，强度宜低强度，一周2-3次为宜，每次30-40分钟，以自我感觉不过度劳累为主。",
          title: "运动建议"
        }],
        treatment: [{
          advice: "可做保健性艾灸。先将艾条点燃，放在灸盒中的铁纱上，并将温灸盒置于关元穴上方，盖好封盖以调节温度。每次灸20~30分钟。每日1次，7~10次为1个疗程。注意预防烫伤。",
          title: "艾灸保健"
        }, {
          advice: "可做保健性耳穴疗法。取神门、心、脾、颈椎、肩、颈、等耳穴。将耳穴消毒，在耳穴上贴王不留行籽或耳穴压丸，用拇、食指进行垂直按压，施压至患出现沉、重、胀、痛感。每穴按压1分钟左右。每穴重复操作2~3遍，每天3~5次。双侧耳穴轮流使用，2日1次替换。",
          title: "耳穴疗法"
        }, {
          advice: "可用六字诀进行呼吸训练以达到保健效果。六字诀是一种吐纳法。它是通过呬、呵、呼、嘘、吹、嘻六个字的不同发音口型，唇齿喉舌的用力不同，以牵动不同的脏腑经络气血的运行。\n方法：首先预备姿势，两足开立，与肩同宽，头正颈直，含胸拔背，松腰松胯，双膝微屈，全身放松，呼吸自然。\n其次联系呼吸，顺腹式呼吸，先呼后吸，呼所时读字，同时提肛缩肾，体重移至足跟。\n最后调息， 每个字读六遍后，调息一次，以稍事休息，恢复自然。",
          title: "功法导引"
        }, {
          advice: "可行叩齿保健法以达到健脾益胃，纳气补肾的效果，古人认为齿健则身健，身健则长寿。方法：口唇轻闭，首先，上下门牙齿叩击9次，然后左侧上下牙齿叩击9次，右侧上下齿叩击9次，最后上下门齿再叩击9次。每日早晚各一次，每次3分钟左右。叩齿时可用双手指有节律地搓双侧耳孔，提拉双耳廓直到发热为止。",
          title: "叩齿保健法"
        }]
      };
      console.log('2025-01-06 11:45:00,028-INFO-[complete][loadSimulateData_002] 模拟数据加载完成');
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_012] 重新拍照功能
    retakePhoto: function retakePhoto() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 用户点击重新拍照');
      uni.navigateTo({
        url: '/pagesB/shezhen/guide'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_013] 分享报告功能
    shareReport: function shareReport() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_013] 用户点击分享报告');
      uni.showToast({
        title: '分享功能开发中',
        icon: 'none'
      });
    },
    // 2025-01-03 22:55:53,565-INF0-[complete][method_014] 查看舌象图片
    viewTongueImage: function viewTongueImage() {
      console.log('2025-01-03 22:55:53,565-INF0-[complete][method_014] 用户点击查看舌象图片');
      // 实现查看舌象图片的功能
      uni.navigateTo({
        url: '/pages/tongue/image'
      });
    },
    /**
     * 根据舌象特征组获取标记样式
     * @param {String} featureGroup 特征组名称
     * @returns {Object} 包含定位样式的对象
     */
    getMarkerStyle: function getMarkerStyle(featureGroup) {
      var positions = {
        // 舌根区域（现在在上方）
        '舌根部': {
          top: '15%',
          left: '50%'
        },
        // 舌质相关 - 分布在舌体中部
        '舌质': {
          top: '35%',
          left: '50%'
        },
        '舌形胖瘦': {
          top: '45%',
          left: '50%'
        },
        '舌中央': {
          top: '50%',
          left: '50%'
        },
        // 舌两侧 - 更靠边缘
        '舌齿痕': {
          top: '45%',
          left: '8%'
        },
        '舌两侧': {
          top: '40%',
          left: '5%'
        },
        '舌瘀斑瘀点': {
          top: '50%',
          left: '92%'
        },
        // 舌面特征 - 分散在不同位置
        '舌裂纹': {
          top: '42%',
          left: '78%'
        },
        '舌点刺': {
          top: '38%',
          left: '22%'
        },
        // 舌苔相关 - 主要分布在中上部
        '苔色': {
          top: '40%',
          left: '50%'
        },
        '舌苔腻': {
          top: '35%',
          left: '30%'
        },
        '舌苔腐': {
          top: '35%',
          left: '70%'
        },
        '舌苔厚薄': {
          top: '38%',
          left: '35%'
        },
        '舌苔润燥': {
          top: '38%',
          left: '65%'
        },
        '舌苔剥脱': {
          top: '32%',
          left: '50%'
        },
        // 舌尖区域（现在在下方）
        '舌色': {
          top: '75%',
          left: '50%'
        },
        '舌尖色': {
          top: '85%',
          left: '50%'
        },
        '舌尖': {
          top: '90%',
          left: '50%'
        }
      };

      // 获取基础位置
      var basePosition = positions[featureGroup] || {
        top: '50%',
        left: '50%'
      };

      // 小程序环境下使用不同的定位策略
      if (this.isMiniProgram) {
        // 转换百分比为rpx值，基于600rpx宽度和480rpx高度的容器
        var topRpx = Math.round(parseFloat(basePosition.top) / 100 * 480);
        var leftRpx = Math.round(parseFloat(basePosition.left) / 100 * 600);
        return {
          position: 'absolute',
          top: topRpx + 'rpx',
          left: leftRpx + 'rpx',
          marginTop: '-30rpx',
          // marker高度的一半
          marginLeft: '-50rpx',
          // marker宽度的一半
          zIndex: 10
        };
      } else {
        // 浏览器环境保持原有样式
        return {
          position: 'absolute',
          top: basePosition.top,
          left: basePosition.left,
          transform: 'translate(-50%, -50%)',
          zIndex: 10
        };
      }
    },
    /**
     * 解析保养建议数据
     * @param {Object} data 后端返回的分析数据
     */
    parseCareSuggestions: function parseCareSuggestions(data) {
      console.log('2025-01-06 11:45:00,029-INFO-[complete][parseCareSuggestions_001] 解析保养建议数据');
      try {
        // 直接从数据中获取advices字段
        if (data.advices) {
          this.careSuggestions = data.advices;
          console.log('2025-01-06 11:45:00,030-INFO-[complete][parseCareSuggestions_002] 保养建议数据解析成功:', this.careSuggestions);

          // 解析各类建议到原有字段（兼容性）
          if (data.advices.food) {
            this.dietSuggestions = data.advices.food;
          }
          if (data.advices.sport) {
            this.exerciseSuggestions = data.advices.sport;
          }
          return;
        }

        // 如果没有advices字段，尝试从其他字段构建
        var suggestions = {};

        // 检查是否有字符串形式的建议数据
        if (data.diet_suggestions) {
          try {
            var dietData = typeof data.diet_suggestions === 'string' ? JSON.parse(data.diet_suggestions) : data.diet_suggestions;
            if (Array.isArray(dietData)) {
              suggestions.food = dietData;
            }
          } catch (e) {
            console.error('2025-01-06 11:45:00,031-ERROR-[complete][parseCareSuggestions_003] 解析饮食建议失败:', e);
          }
        }
        if (data.exercise_suggestions) {
          try {
            var exerciseData = typeof data.exercise_suggestions === 'string' ? JSON.parse(data.exercise_suggestions) : data.exercise_suggestions;
            if (Array.isArray(exerciseData)) {
              suggestions.sport = exerciseData;
            }
          } catch (e) {
            console.error('2025-01-06 11:45:00,032-ERROR-[complete][parseCareSuggestions_004] 解析运动建议失败:', e);
          }
        }

        // 如果构建了建议数据，设置到careSuggestions
        if (Object.keys(suggestions).length > 0) {
          this.careSuggestions = suggestions;
          console.log('2025-01-06 11:45:00,033-INFO-[complete][parseCareSuggestions_005] 从其他字段构建保养建议:', this.careSuggestions);
        } else {
          console.log('2025-01-06 11:45:00,034-WARN-[complete][parseCareSuggestions_006] 未找到保养建议数据');
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,035-ERROR-[complete][parseCareSuggestions_007] 解析保养建议异常:', error);
      }
    },
    /**
     * 获取优化的标记样式，避免重叠
     * @param {String} featureGroup 特征组名称
     * @param {Number} index 当前索引
     * @param {Number} total 总数量
     * @returns {Object} 包含定位样式的对象
     */
    getOptimizedMarkerStyle: function getOptimizedMarkerStyle(featureGroup, index, total) {
      // 先获取基础样式
      var baseStyle = this.getMarkerStyle(featureGroup);

      // 如果是小程序环境且有多个标签，需要避免重叠
      if (this.isMiniProgram && total > 1) {
        // 如果多个特征位置相同，进行偏移处理
        var samePositionOffset = this.calculateOffsetForSamePosition(featureGroup, index, total);
        if (samePositionOffset) {
          return _objectSpread(_objectSpread({}, baseStyle), {}, {
            top: "calc(".concat(baseStyle.top, " + ").concat(samePositionOffset.top, "rpx)"),
            left: "calc(".concat(baseStyle.left, " + ").concat(samePositionOffset.left, "rpx)")
          });
        }
      }
      return baseStyle;
    },
    /**
     * 计算相同位置标签的偏移量
     * @param {String} featureGroup 特征组名称
     * @param {Number} index 当前索引
     * @param {Number} total 总数量
     * @returns {Object|null} 偏移量对象
     */
    calculateOffsetForSamePosition: function calculateOffsetForSamePosition(featureGroup, index, total) {
      // 定义容易重叠的特征组
      var centerGroups = ['舌质', '舌形胖瘦', '舌中央', '苔色'];
      var leftGroups = ['舌齿痕', '舌两侧'];
      var rightGroups = ['舌瘀斑瘀点', '舌裂纹'];
      if (centerGroups.includes(featureGroup)) {
        // 中心区域的标签呈环形分布，但半径更小，更紧凑
        var angle = index * 360 / total * Math.PI / 180;
        var radius = this.isMiniProgram ? 25 : 20; // 减小偏移半径
        return {
          marginTop: Math.sin(angle) * radius + (this.isMiniProgram ? 'rpx' : 'px'),
          marginLeft: Math.cos(angle) * radius + (this.isMiniProgram ? 'rpx' : 'px')
        };
      } else if (leftGroups.includes(featureGroup)) {
        // 左侧标签垂直分布，间距更小
        var offset = (index - total / 2) * 30; // 减小间隔
        return {
          marginTop: offset + (this.isMiniProgram ? 'rpx' : 'px')
        };
      } else if (rightGroups.includes(featureGroup)) {
        // 右侧标签垂直分布，间距更小
        var _offset = (index - total / 2) * 30; // 减小间隔
        return {
          marginTop: _offset + (this.isMiniProgram ? 'rpx' : 'px')
        };
      }
      return {};
    },
    getFeaturePositionClass: function getFeaturePositionClass(featureGroup) {
      var positionClasses = {
        '舌根部': 'root',
        '舌质': 'tongue',
        '舌形胖瘦': 'thickness',
        '舌中央': 'center',
        '舌齿痕': 'indent',
        '舌两侧': 'sides',
        '舌瘀斑瘀点': 'spots',
        '舌裂纹': 'cracks',
        '舌点刺': 'punctures',
        '苔色': 'mucus',
        '舌苔腻': 'thick',
        '舌苔腐': 'decay',
        '舌苔厚薄': 'thickness',
        '舌苔润燥': 'moisture',
        '舌苔剥脱': 'peeling',
        '舌色': 'color',
        '舌尖色': 'tip',
        '舌尖': 'tip'
      };
      var className = positionClasses[featureGroup] || '';
      console.log("2025-01-06 12:30:00,001-INFO-[complete][getFeaturePositionClass] \u7279\u5F81\u7EC4: ".concat(featureGroup, " -> CSS\u7C7B: marker-").concat(className));
      return className;
    },
    getSimpleMarkerStyle: function getSimpleMarkerStyle(featureGroup, index) {
      // 现在主要通过CSS类定位，只需要简单的样式补充
      var offset = this.calculateOffsetForSamePosition(featureGroup, index, this.displayedTongueFeatures.length);
      var style = _objectSpread({
        zIndex: 10 + index
      }, offset);
      console.log("2025-01-06 12:30:00,002-INFO-[complete][getSimpleMarkerStyle] \u7279\u5F81\u7EC4: ".concat(featureGroup, ", \u7D22\u5F15: ").concat(index, ", \u6837\u5F0F:"), style);

      // 只返回偏移样式，基础定位由CSS类处理
      return style;
    },
    /**
     * 解析推荐商品数据 - 新增方法
     * @param {Object} data 后端返回的分析数据
     */
    parseRecommendProducts: function parseRecommendProducts(data) {
      console.log('2025-01-06 11:45:00,036-INFO-[complete][parseRecommendProducts_001] 解析推荐商品数据');
      try {
        // 从 recommend_products 字段解析推荐商品
        if (data.recommend_products && Array.isArray(data.recommend_products)) {
          this.recommendProducts = data.recommend_products;
          console.log('2025-01-06 11:45:00,037-INFO-[complete][parseRecommendProducts_002] 推荐商品数据解析成功:', this.recommendProducts.length, '个商品');
        }

        // 获取推荐标题
        if (data.recommend_title) {
          this.recommendTitle = data.recommend_title;
        }

        // 如果没有推荐商品，尝试调用独立的推荐商品接口
        if ((!this.recommendProducts || this.recommendProducts.length === 0) && this.constitutionType && this.healthScore) {
          console.log('2025-01-06 11:45:00,038-INFO-[complete][parseRecommendProducts_003] 数据中无推荐商品，调用独立接口获取');
          this.getRecommendProductsFromApi();
        }
      } catch (error) {
        console.error('2025-01-06 11:45:00,039-ERROR-[complete][parseRecommendProducts_004] 解析推荐商品异常:', error);
      }
    },
    /**
     * 从API获取推荐商品 - 新增方法
     */
    getRecommendProductsFromApi: function getRecommendProductsFromApi() {
      var _this4 = this;
      console.log('2025-01-06 11:45:00,040-INFO-[complete][getRecommendProductsFromApi_001] 开始从API获取推荐商品');
      var app = getApp();
      app.post('ApiSheZhen/getRecommendProducts', {
        constitution_type: this.constitutionType,
        constitution_score: this.healthScore
      }, function (response) {
        console.log('2025-01-06 11:45:00,041-INFO-[complete][getRecommendProductsFromApi_002] 推荐商品API响应:', response);
        if (response && (response.status === 1 || response.code === 1)) {
          var recommendData = response.data;
          if (recommendData && recommendData.products && Array.isArray(recommendData.products)) {
            _this4.recommendProducts = recommendData.products;
            if (recommendData.recommend_title) {
              _this4.recommendTitle = recommendData.recommend_title;
            }
            console.log('2025-01-06 11:45:00,042-INFO-[complete][getRecommendProductsFromApi_003] 成功获取推荐商品:', _this4.recommendProducts.length, '个');
          }
        } else {
          console.log('2025-01-06 11:45:00,043-WARN-[complete][getRecommendProductsFromApi_004] 获取推荐商品失败:', response === null || response === void 0 ? void 0 : response.msg);
        }
      }, function (err) {
        console.error('2025-01-06 11:45:00,044-ERROR-[complete][getRecommendProductsFromApi_005] 获取推荐商品异常:', err);
      });
    },
    /**
     * 获取推荐视频 - 新增方法
     */
    getRecommendVideos: function getRecommendVideos() {
      var _this5 = this;
      console.log('2025-01-31 INFO-[complete][getRecommendVideos_001] 开始获取推荐视频');

      // 获取体质类型和得分
      var constitutionType = this.constitutionType || '';
      var constitutionScore = this.healthScore || 0;
      console.log('2025-01-31 INFO-[complete][getRecommendVideos_001.5] 体质信息:', {
        constitutionType: constitutionType,
        constitutionScore: constitutionScore
      });

      // 如果没有体质信息，不获取推荐视频
      if (!constitutionType && !constitutionScore) {
        console.log('2025-01-31 INFO-[complete][getRecommendVideos_002] 无体质信息，跳过视频推荐');
        return;
      }
      var app = getApp();

      // 调用推荐视频接口
      app.post('ApiSheZhen/getRecommendProducts', {
        constitution_type: constitutionType,
        constitution_score: constitutionScore
      }, function (response) {
        console.log('2025-01-31 INFO-[complete][getRecommendVideos_003] 获取推荐视频结果:', response);

        // 兼容多种响应格式
        if (response && (response.code === 1 || response.status === 1) && response.data && response.data.products) {
          // 筛选出视频类型的推荐
          var videos = response.data.products.filter(function (item) {
            return item.type === 'video';
          });
          _this5.recommendVideos = videos;
          console.log('2025-01-31 INFO-[complete][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);

          // 如果有视频数据，输出详细信息
          if (videos.length > 0) {
            console.log('2025-01-31 INFO-[complete][getRecommendVideos_004.5] 视频详情:', videos);
          }
        } else {
          var _response$data, _response$data2, _response$data2$produ;
          console.log('2025-01-31 INFO-[complete][getRecommendVideos_005] 无推荐视频数据，响应详情:', {
            hasResponse: !!response,
            code: response === null || response === void 0 ? void 0 : response.code,
            status: response === null || response === void 0 ? void 0 : response.status,
            hasData: !!(response !== null && response !== void 0 && response.data),
            hasProducts: !!(response !== null && response !== void 0 && (_response$data = response.data) !== null && _response$data !== void 0 && _response$data.products),
            productsLength: response === null || response === void 0 ? void 0 : (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$produ = _response$data2.products) === null || _response$data2$produ === void 0 ? void 0 : _response$data2$produ.length
          });

          // 临时添加测试数据，方便调试
          console.log('2025-01-31 INFO-[complete][getRecommendVideos_005.5] 添加测试视频数据');
          _this5.recommendVideos = [{
            id: 999,
            name: '测试调理视频',
            type: 'video',
            url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
            pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
            recommend_reason: '适合您的体质类型',
            view_num: 1000,
            zan_num: 50
          }];
        }
      }, function (error) {
        console.error('2025-01-31 ERROR-[complete][getRecommendVideos_006] 获取推荐视频失败:', error);

        // 出错时也添加测试数据
        console.log('2025-01-31 INFO-[complete][getRecommendVideos_006.5] 接口出错，添加测试视频数据');
        _this5.recommendVideos = [{
          id: 998,
          name: '错误测试视频',
          type: 'video',
          url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
          pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
          recommend_reason: '测试推荐理由',
          view_num: 500,
          zan_num: 25
        }];
      });
    },
    /**
     * 播放视频 - 新增方法
     */
    playVideo: function playVideo(video) {
      console.log('2025-01-31 INFO-[complete][playVideo_001] 播放视频:', video);
      if (!video.url) {
        uni.showToast({
          title: '视频地址无效',
          icon: 'none'
        });
        return;
      }

      // 处理视频URL，确保中文字符正确编码
      var processedVideo = _objectSpread({}, video);
      if (video.url && video.url.includes('%')) {
        // URL已经编码，直接使用
        processedVideo.url = video.url;
      } else if (video.url) {
        // 对URL中的中文字符进行编码
        try {
          var urlParts = video.url.split('/');
          var encodedParts = urlParts.map(function (part) {
            // 只对文件名部分进行编码，保留协议和域名
            if (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {
              return encodeURIComponent(part);
            }
            return part;
          });
          processedVideo.url = encodedParts.join('/');
          console.log('2025-01-31 INFO-[complete][playVideo_001.5] URL编码处理:', {
            original: video.url,
            processed: processedVideo.url
          });
        } catch (e) {
          console.warn('2025-01-31 WARN-[complete][playVideo_001.6] URL编码失败，使用原始URL:', e);
          processedVideo.url = video.url;
        }
      }

      // 设置当前播放视频并显示播放器
      this.currentVideo = processedVideo;
      this.showVideoPlayer = true;

      // 记录视频播放事件
      console.log('2025-01-31 INFO-[complete][playVideo_002] 开始播放视频:', video.name);
      console.log('2025-01-31 INFO-[complete][playVideo_003] 视频URL:', processedVideo.url);
    },
    /**
     * 关闭视频播放器 - 新增方法
     */
    closeVideoPlayer: function closeVideoPlayer() {
      console.log('2025-01-31 INFO-[complete][closeVideoPlayer_001] 关闭视频播放器');
      this.showVideoPlayer = false;
      this.currentVideo = {};
    },
    /**
     * 视频播放错误处理 - 新增方法
     */
    onVideoError: function onVideoError(e) {
      console.error('2025-01-31 ERROR-[complete][onVideoError_001] 视频播放错误:', e);
      uni.showToast({
        title: '视频播放失败',
        icon: 'none'
      });
    },
    /**
     * 视频开始播放事件 - 新增方法
     */
    onVideoPlay: function onVideoPlay(video) {
      console.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', video.name);
    },
    /**
     * 视频暂停事件 - 新增方法
     */
    onVideoPause: function onVideoPause(video) {
      console.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停:', video.name);
    }
  }, (0, _defineProperty2.default)(_methods, "onVideoPlay", function onVideoPlay(e) {
    console.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', e);
  }), (0, _defineProperty2.default)(_methods, "onVideoPause", function onVideoPause(e) {
    console.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停播放:', e);
  }), (0, _defineProperty2.default)(_methods, "onVideoLoadStart", function onVideoLoadStart(e) {
    console.log('2025-01-31 INFO-[complete][onVideoLoadStart_001] 视频开始加载:', e);
  }), (0, _defineProperty2.default)(_methods, "onVideoCanPlay", function onVideoCanPlay(e) {
    console.log('2025-01-31 INFO-[complete][onVideoCanPlay_001] 视频可以播放:', e);
  }), (0, _defineProperty2.default)(_methods, "getRecommendBadge", function getRecommendBadge(reason) {
    if (!reason) return '';
    if (reason.includes('体质匹配')) return '体质';
    if (reason.includes('得分推荐')) return '得分';
    if (reason.includes('热销')) return '热销';
    if (reason.includes('专业')) return '专业';
    return '推荐';
  }), (0, _defineProperty2.default)(_methods, "viewProductDetail", function viewProductDetail(product) {
    console.log('2025-01-06 11:45:00,045-INFO-[complete][viewProductDetail_001] 查看商品详情:', product);

    // 根据商品类型跳转到对应的详情页面
    if (product.product_type === 2) {
      // 课程详情
      uni.navigateTo({
        url: "/pages/course/detail?id=".concat(product.id)
      });
    } else {
      // 商品详情
      uni.navigateTo({
        url: "/pages/product/detail?id=".concat(product.id)
      });
    }
  }), (0, _defineProperty2.default)(_methods, "buyProduct", function buyProduct(product) {
    console.log('2025-01-06 11:45:00,046-INFO-[complete][buyProduct_001] 购买商品:', product);

    // 确认购买对话框
    uni.showModal({
      title: '确认购买',
      content: "\u786E\u5B9A\u8981\u8D2D\u4E70\"".concat(product.name, "\"\u5417\uFF1F"),
      success: function success(res) {
        if (res.confirm) {
          // 根据商品类型进行不同的购买流程
          if (product.product_type === 2) {
            // 课程购买
            uni.navigateTo({
              url: "/pages/course/buy?id=".concat(product.id)
            });
          } else {
            // 商品购买 - 加入购物车或直接购买
            uni.navigateTo({
              url: "/pages/product/buy?id=".concat(product.id, "&type=direct")
            });
          }
        }
      }
    });
  }), (0, _defineProperty2.default)(_methods, "navigateToProduct", function navigateToProduct(product) {
    console.log('2025-01-06 11:45:00,047-INFO-[complete][navigateToProduct_001] 导航到商品详情:', product);

    // 根据产品类型使用不同的路径
    var url = '';
    var productId = product.id || 290; // 如果没有id则使用默认值290

    if (product.type === 'course') {
      // 课程路径
      url = "/activity/kecheng/product?id=".concat(productId);
    } else {
      // 商品路径
      url = "/shopPackage/shop/product?id=".concat(productId);
    }
    console.log('2025-01-06 11:45:00,048-INFO-[complete][navigateToProduct_002] 准备导航到:', url);
    uni.navigateTo({
      url: url,
      success: function success() {
        console.log('2025-01-06 11:45:00,049-INFO-[complete][navigateToProduct_003] 成功导航到详情页:', url);
      },
      fail: function fail(err) {
        console.error('2025-01-06 11:45:00,050-ERROR-[complete][navigateToProduct_004] 导航失败:', err);
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  }), (0, _defineProperty2.default)(_methods, "addToCart", function addToCart(product) {
    console.log('2025-01-06 11:45:00,051-INFO-[complete][addToCart_001] 点击购物车按钮:', product);
    if (product.type === 'course') {
      // 课程类型：直接跳转到课程详情页
      this.navigateToProduct(product);
    } else {
      // 商品类型：添加到购物车
      uni.showToast({
        title: '已添加到购物车',
        icon: 'success',
        duration: 1500
      });

      // 这里可以调用实际的购物车API
      // const app = getApp();
      // app.post('Cart/add', {
      //     product_id: product.id,
      //     quantity: 1
      // }, (response) => {
      //     if (response.status === 1) {
      //         uni.showToast({
      //             title: '添加成功',
      //             icon: 'success'
      //         });
      //     }
      // });
    }
  }), _methods),
  onLoad: function onLoad(options) {
    var _this6 = this;
    console.log('2025-01-06 11:45:00,100-INFO-[complete][onLoad_001] 舌诊完整页面加载，参数:', options);

    // 检测是否为小程序环境

    this.isMiniProgram = true;
    console.log('2025-01-06 11:45:00,101-INFO-[complete][onLoad_002] 检测到小程序环境');

    // 获取传递的参数
    if (options.record_id || options.recordId) {
      this.recordId = options.record_id || options.recordId;
      console.log('2025-01-06 11:45:00,102-INFO-[complete][onLoad_003] 获取记录ID:', this.recordId);
    }
    if (options.order_no || options.orderNo) {
      this.orderNo = options.order_no || options.orderNo;
      console.log('2025-01-06 11:45:00,103-INFO-[complete][onLoad_004] 获取订单号:', this.orderNo);
    }
    if (options.api_type || options.apiType) {
      this.apiType = options.api_type || options.apiType;
      console.log('2025-01-06 11:45:00,104-INFO-[complete][onLoad_005] 获取API类型:', this.apiType);
    }

    // 2025-01-27 获取订单ID参数
    if (options.order_id) {
      this.orderId = options.order_id;
      console.log('2025-01-27 11:45:00,106-INFO-[complete][onLoad_006] 获取支付订单ID:', this.orderId);
    }

    // 临时添加测试视频数据，方便调试
    setTimeout(function () {
      console.log('2025-01-31 INFO-[complete][onLoad_007] 设置测试视频数据');
      _this6.recommendVideos = [{
        id: 1001,
        name: '测试调理视频 - onLoad',
        type: 'video',
        url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
        pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
        recommend_reason: '适合您的体质类型 - 测试数据',
        view_num: 1500,
        zan_num: 75
      }];
      console.log('2025-01-31 INFO-[complete][onLoad_008] 测试视频数据设置完成，数量:', _this6.recommendVideos.length);
    }, 1000);

    // 检查必要参数
    if (this.recordId) {
      // 获取舌诊分析数据
      this.getAnalysisRecord();
    } else {
      console.error('2025-01-06 11:45:00,105-ERROR-[complete][onLoad_006] 缺少必要参数recordId');
      uni.showToast({
        title: '参数错误，请重新进入',
        icon: 'none'
      });
      setTimeout(function () {
        uni.navigateBack();
      }, 2000);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 6943:
/*!**********************************************************************************************************************************!*\
  !*** D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css& ***!
  \**********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css& */ 6944);
/* harmony import */ var _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_6_oneOf_1_0_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_6_oneOf_1_2_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Users_Administrator_Desktop_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_complete_vue_vue_type_style_index_0_id_39446614_scoped_true_lang_css___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 6944:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--6-oneOf-1-2!./node_modules/postcss-loader/src??ref--6-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/qianhouduankaifabao/tiantianshande/pagesB/shezhen/complete.vue?vue&type=style&index=0&id=39446614&scoped=true&lang=css& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[6937,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pagesB/shezhen/complete.js.map