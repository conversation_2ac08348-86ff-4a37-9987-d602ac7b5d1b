# 梦想启蒙职业传递问题修复说明

## 🐛 问题描述

用户在对话页面选择了梦想是"宇航员"，但是在调用梦想启蒙API时，工作流接收到的职业参数（zhiye）仍然是默认的"医生"，而不是用户选择的"宇航员"。

## 🔍 问题分析

### 原始流程问题
1. **前端传递参数不完整**：
   - 前端只传递了 `gender`、`dream_content`、`image_url` 三个参数
   - 没有单独传递职业信息

2. **后端获取职业方式有误**：
   - 后端从用户信息 `$userInfo['zhiye']` 中获取职业
   - 用户信息中的职业字段可能为空或默认值
   - 没有从前端传递的梦想内容中提取职业信息

3. **工作流参数格式要求**：
   ```json
   {
     "BOT_USER_INPUT": "",
     "gender": "男",
     "image_url": "https://...",
     "zhiye": "医生"  // 这里需要是用户选择的职业
   }
   ```

## 🔧 修复方案

### 1. 前端修改（camera-new.vue）

#### 新增职业参数传递
```javascript
// 确定职业信息 - 优先使用对话页面的梦想，其次使用配置页面的职业
let profession = '';
if (dialogueDream && dialogueDream.trim() !== '') {
    profession = dialogueDream; // 使用对话页面的梦想作为职业
} else if (userProfession && userProfession !== '未设置') {
    profession = userProfession; // 使用配置页面的职业
} else {
    profession = '医生'; // 默认职业
}

// 准备请求参数
const requestData = {
    gender: genderCode,
    dream_content: dreamContent,
    profession: profession, // 新增：职业信息
    image_url: this.capturedImageUrl
};
```

#### 增加调试日志
```javascript
console.log('职业信息:', {
    对话梦想: dialogueDream,
    配置职业: userProfession,
    最终职业: profession
});
```

### 2. 后端修改（ApiDreamInspiration.php）

#### 接收profession参数
```php
public function generateImage(){
    // ...
    $profession = input('profession', '', 'trim'); // 新增：职业信息
    // ...
    
    // 调用工作流生成图片（异步处理）
    $this->executeWorkflow($recordId, $setting['workflow_id'], $dreamContent, $imageUrl, $gender, $profession);
}
```

#### 修改executeWorkflow方法
```php
private function executeWorkflow($recordId, $workflowId, $dreamContent, $imageUrl = '', $gender = 1, $profession = ''){
    try {
        // 确定职业信息 - 优先使用前端传递的职业，其次使用用户信息中的职业，最后使用默认值
        if(empty($profession)){
            $userInfo = $this->member;
            $profession = $userInfo['zhiye'] ?? '医生'; // 从用户信息获取职业，默认为医生
        }

        // 按照您提供的格式准备工作流参数
        $parameters = [
            'BOT_USER_INPUT' => '', // 空字符串
            'gender' => $gender == 1 ? '男' : '女', // 转换为中文
            'image_url' => $imageUrl,
            'zhiye' => $profession // 使用前端传递的职业信息
        ];
        
        // ...
    }
}
```

## ✅ 修复效果

### 修复前
- 用户选择"宇航员"梦想
- 工作流接收到的参数：`"zhiye": "医生"`
- 生成的图片不符合用户期望

### 修复后
- 用户选择"宇航员"梦想
- 工作流接收到的参数：`"zhiye": "宇航员"`
- 生成的图片符合用户的宇航员梦想

## 🔄 数据流向

```
对话页面选择梦想 → 存储到 user_dialogue_dream
                ↓
camera-new.vue 读取梦想信息 → 作为 profession 参数传递
                ↓
ApiDreamInspiration/generateImage 接收 profession
                ↓
executeWorkflow 使用 profession 作为工作流的 zhiye 参数
                ↓
工作流生成符合用户梦想的图片
```

## 📝 测试验证

1. **测试步骤**：
   - 在对话页面选择梦想为"宇航员"
   - 进入拍照页面上传图片
   - 点击生成按钮
   - 查看后端日志中的工作流参数

2. **预期结果**：
   - 后端日志显示：`zhiye=宇航员 (来源:前端传递)`
   - 工作流参数包含：`"zhiye": "宇航员"`

## 🚀 Git提交记录

### 后端提交
```
commit 4038c27a6
修复梦想启蒙功能职业传递问题

- 前端：在API请求中新增profession参数，优先使用对话页面的梦想作为职业
- 后端：修改generateImage和executeWorkflow方法，支持接收和处理前端传递的职业信息
- 解决用户选择宇航员等梦想职业无法正确传递到工作流的问题
- 增加详细的调试日志，便于排查职业信息来源
```

### 前端提交
```
commit 5d0a2254
前端：修复梦想启蒙职业传递问题

- 在camera-new.vue中新增profession参数传递
- 优先使用对话页面的梦想作为职业信息
- 增加详细的调试日志输出
- 确保用户选择的宇航员等梦想能正确传递到后端工作流
```

## 🔍 调试信息

如果问题仍然存在，可以通过以下方式排查：

1. **前端调试**：
   - 查看浏览器控制台中的"职业信息"日志
   - 确认"最终职业"是否为"宇航员"

2. **后端调试**：
   - 查看后端日志中的工作流调用信息
   - 确认职业来源是否为"前端传递"

3. **工作流参数**：
   - 查看工作流原始返回数据日志
   - 确认传递给工作流的参数中 zhiye 字段值
