
.comprehensive-result-container {
	min-height: 100vh;
	background: #f5f5f5;
	padding-bottom: 120rpx;
}
.result-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx 30rpx;
	color: #fff;
}
.header-content {
	display: flex;
	flex-direction: column;
}
.images-section {
	margin-bottom: 30rpx;
}
.image-grid {
	display: flex;
	gap: 20rpx;
	justify-content: center;
}
.image-item {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
	background: rgba(255,255,255,0.1);
}
.image-item image {
	width: 100%;
	height: 100%;
}
.image-label {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(0,0,0,0.7);
	color: #fff;
	font-size: 20rpx;
	text-align: center;
	padding: 6rpx;
}
.result-summary {
	text-align: center;
}
.summary-title {
	font-size: 36rpx;
	font-weight: 600;
	display: block;
	margin-bottom: 10rpx;
}
.summary-date {
	font-size: 26rpx;
	opacity: 0.8;
}
.comprehensive-score {
	margin-top: 30rpx;
}
.score-container {
	display: flex;
	align-items: center;
	justify-content: center;
}
.score-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255,255,255,0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin-right: 30rpx;
}
.score-number {
	font-size: 36rpx;
	font-weight: 600;
	line-height: 1;
}
.score-label {
	font-size: 22rpx;
	opacity: 0.8;
}
.score-details {
	text-align: left;
}
.score-desc {
	font-size: 28rpx;
	display: block;
	margin-bottom: 15rpx;
}
.score-level {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	display: inline-block;
}
.score-level.excellent {
	background: rgba(76, 175, 80, 0.3);
}
.score-level.good {
	background: rgba(139, 195, 74, 0.3);
}
.score-level.normal {
	background: rgba(255, 193, 7, 0.3);
}
.score-level.poor {
	background: rgba(244, 67, 54, 0.3);
}
.level-text {
	font-size: 26rpx;
	color: #fff;
}
.overview-section, .analysis-section {
	margin: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
}
.section-header {
	margin-bottom: 30rpx;
}
.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}
.overview-cards {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.overview-card {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
}
.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.card-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}
.card-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}
.card-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.card-result {
	font-size: 26rpx;
	color: #007aff;
	font-weight: 500;
}
.card-score {
	font-size: 24rpx;
	color: #666;
}
.analysis-tabs {
	display: flex;
	background: #f5f5f5;
	border-radius: 15rpx;
	padding: 8rpx;
	margin-bottom: 30rpx;
}
.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 10rpx;
	transition: all 0.3s;
}
.tab-item.active {
	background: #007aff;
	color: #fff;
}
.tab-text {
	font-size: 26rpx;
	font-weight: 500;
}
.content-panel {
	min-height: 300rpx;
}
.comprehensive-analysis, .individual-analysis, .advice-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.analysis-item, .individual-item, .advice-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
}
.item-header, .advice-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}
.item-title, .advice-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
}
.item-level {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;
}
.item-level.normal {
	background: #e8f5e8;
	color: #4caf50;
}
.item-content, .item-details, .advice-content {
	margin-top: 15rpx;
}
.content-text, .detail-text, .advice-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
.advice-icon {
	font-size: 32rpx;
	margin-right: 15rpx;
}
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;
	padding: 30rpx;
	border-top: 1px solid #eee;
	display: flex;
	gap: 20rpx;
}
.action-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.secondary-btn {
	background: #f5f5f5;
	color: #666;
}
.primary-btn {
	color: #fff;
}
.btn-text {
	font-size: 32rpx;
	font-weight: 600;
}

/* 视频推荐区域样式 */
.video-recommend-section {
	background: #fff;
	margin: 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}
.section-subtitle {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}
.video-list {
	margin-top: 30rpx;
}
.video-item {
	display: flex;
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	border: 1px solid #eee;
}
.video-item:last-child {
	margin-bottom: 0;
}
.video-cover {
	position: relative;
	width: 200rpx;
	height: 120rpx;
	border-radius: 10rpx;
	overflow: hidden;
	margin-right: 20rpx;
	flex-shrink: 0;
}
.cover-image {
	width: 100%;
	height: 100%;
}
.play-icon {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 60rpx;
	height: 60rpx;
	background: rgba(0, 0, 0, 0.6);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.play-text {
	color: #fff;
	font-size: 24rpx;
	margin-left: 4rpx;
}
.video-duration {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	background: rgba(0, 0, 0, 0.7);
	color: #fff;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}
.duration-text {
	font-size: 20rpx;
}
.video-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.video-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
	margin-bottom: 10rpx;
}
.video-reason {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	padding: 8rpx 12rpx;
	background: #e8f5e8;
	color: #4caf50;
	border-radius: 8rpx;
	align-self: flex-start;
}
.video-stats {
	display: flex;
	gap: 20rpx;
}
.stat-item {
	font-size: 22rpx;
	color: #999;
}

/* 视频播放器遮罩层样式 */
.video-player-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}
.video-player-container {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	width: 100%;
	max-width: 640rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}
.video-player-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1px solid #eee;
}
.video-player-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
}
.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	border-radius: 50%;
	cursor: pointer;
}
.close-text {
	font-size: 32rpx;
	color: #666;
	font-weight: bold;
}
.video-player {
	width: 100%;
	height: 400rpx;
	background: #000;
}
.video-player-info {
	padding: 20rpx 30rpx;
	background: #fff;
}
.video-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
	display: block;
}
.video-stats-row {
	display: flex;
	gap: 20rpx;
}
.video-stats-row .stat-item {
	font-size: 24rpx;
	color: #999;
}

