{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?6920", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?6f6b", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?a2a5", "uni-app:///pagesB/diagnosis/comprehensive/result.vue", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?c1dc", "webpack:///D:/qianhouduankaifabao/tiantianshande/pagesB/diagnosis/comprehensive/result.vue?6fd9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "recordId", "resultData", "activeTab", "tongueResult", "faceResult", "sublingualResult", "comprehensiveAnalysis", "tongueAnalysis", "faceAnalysis", "sublingualAnalysis", "careAdvice", "recommendVideos", "showVideoPlayer", "currentVideo", "computed", "currentDate", "String", "scoreLevelClass", "scoreLevelText", "onLoad", "methods", "getAnalysisRecord", "console", "uni", "title", "app", "id", "icon", "parseNewAnalysisResult", "analysisData", "parseNewApiData", "parseNewComprehensiveFeatures", "constitution_type", "constitution_score", "detailed_analysis", "face_status", "face_score", "vein_status", "vein_score", "parseNewComprehensiveAnalysis", "overall_health", "constitution_desc", "health_advice", "parseNewComprehensiveCareAdvice", "dietAdvice", "exerciseAdvice", "sleepAdvice", "diet", "exercise", "sleep", "calculateFeatureScore", "features", "normalCount", "getConstitutionFromFeatures", "getFaceStatusFromFeatures", "getVeinStatusFromFeatures", "generateFeatureAnalysis", "analysis", "getConstitutionDescription", "loadMockData", "comprehensive_score", "tongue_image", "face_image", "sublingual_image", "parseDefaultFeatures", "parseDefaultCareAdvice", "parseAnalysisResult", "switchTab", "goBack", "shareResult", "getRecommendVideos", "constitutionType", "constitutionScore", "playVideo", "processedVideo", "original", "processed", "closeVideoPlayer", "onVideoError", "onVideoPlay", "onVideoPause", "onVideoLoadStart", "onVideoCanPlay"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACoM;AACpM,gBAAgB,2MAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,swBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8S/wB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA,iCACAC,oDACAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACAC;MAEA;MAEAC;QACAC;MACA;;MAEA;MACAC;QACAC;MACA;QACAH;QACAD;QAEA;UACAA;UACA;UACA;QACA;UACAA;UACAC;YACAC;YACAG;UACA;;UAEA;UACA;QACA;MACA;QACAJ;QACAD;QAEAC;UACAC;UACAG;QACA;;QAEA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACAN;QACAA;;QAEA;QACA;QACA;;QAEA;QACA;UACAO;QACA;UACAA;QACA;QAEAP;;QAEA;QACA;UACAA;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;UACA;QACA;MAEA;QACAA;QACA;QACA;MACA;IACA;IAEA;IACAQ;MACAR;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAS;MACAT;;MAEA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;QAAA;MAAA;;MAEA;MACA;QACA;QACA;UACAU;UACAC;QACA;QACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAC;UACAC;QACA;QACA;UACAF;QACA;MACA;;MAEA;MACA;QACA;QACA;UACAG;UACAC;QACA;QACA;UACAJ;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAK;MACA;MACA;MACA;MACA;MAEA;QACAC;QACAR;QACAS;QACAC;MACA;IACA;IAEA;IACAC;MACArB;;MAEA;MACA;MACA;QACAsB;UAAA;QAAA;MACA;;MAEA;MACA;MACA;QACAC;UAAA;QAAA;MACA;;MAEA;MACA;MACA;QACAC;UAAA;QAAA;MACA;MAEA;QACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACAC;QACA;UACAC;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MAEA;MAEA;QACA;UAAA;QAAA;QACAC;MACA;MAEA;QACA;UAAA;QAAA;QACAA;MACA;QACAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACAhC;UACAC;QACA;QACA;UACAC;QACA;MACA;MAEA;QACA;UACAC;UACAC;QACA;QACA;UACAF;QACA;MACA;MAEA;QACA;UACAG;UACAC;QACA;QACA;UACAJ;QACA;MACA;MAEA;QACAM;QACAR;QACAS;QACAC;MACA;IACA;IAEA;IACAuB;MACA;QACAlB;QACAE;QACAD;MACA;IACA;IAEA;IACAkB;MACA;QACA;QACA;;QAEA;QACA;UACA;YACAlC;YACAC;UACA;UACA;YACAC;UACA;QACA;;QAEA;QACA;UACA;YACAC;YACAC;UACA;UACA;YACAF;UACA;QACA;;QAEA;QACA;UACA;YACAG;YACAC;UACA;UACA;YACAJ;UACA;QACA;;QAEA;QACA;UACAM;UACAR;UACAS;UACAC;QACA;;QAEA;QACA;UACAK;UACAE;UACAD;QACA;MAEA;QACA1B;MACA;IACA;IAEA;IACA6C;MACA;IACA;IAEA;IACAC;MACA7C;IACA;IAEA;IACA8C;MACA9C;QACAC;QACAG;MACA;IACA;IAEA;IACA2C;MAAA;MACAhD;;MAEA;MACA;MACA;MAEA;QACAiD;MACA;MACA;QACAC;MACA;;MAEA;MACA;QACAlD;QACA;MACA;MAEA;;MAEA;MACAG;QACAO;QACAC;MACA;QACAX;QAEA;UACA;UACA;YAAA;UAAA;UACA;UACAA;QACA;UACAA;QACA;MACA;QACAA;MACA;IACA;IAEA;IACAmD;MACAnD;MAEA;QACAC;UACAC;UACAG;QACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA+C;MACA;QACA;QACA;UACA;UACA;YACA;YACA;cACA;YACA;YACA;UACA;UACAA;UACApD;YACAqD;YACAC;UACA;QACA;UACAtD;UACAoD;QACA;MACA;;MAEA;MACA;MACA;;MAEA;MACApD;MACAA;IACA;IAEA;IACAuD;MACAvD;MACA;MACA;IACA;IAEA;IACAwD;MACAxD;MACAC;QACAC;QACAG;MACA;IACA;IAEA;IACAoD;MACAzD;IACA;IAEA;IACA0D;MACA1D;IACA;IAEA;IACA2D;MACA3D;IACA;IAEA;IACA4D;MACA5D;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACp4BA;AAAA;AAAA;AAAA;AAA2kC,CAAgB,ujCAAG,EAAC,C;;;;;;;;;;;ACA/lC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesB/diagnosis/comprehensive/result.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesB/diagnosis/comprehensive/result.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./result.vue?vue&type=template&id=c11410ae&\"\nvar renderjs\nimport script from \"./result.vue?vue&type=script&lang=js&\"\nexport * from \"./result.vue?vue&type=script&lang=js&\"\nimport style0 from \"./result.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesB/diagnosis/comprehensive/result.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./result.vue?vue&type=template&id=c11410ae&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recommendVideos.length\n  var m0 = _vm.t(\"color1\")\n  var m1 = _vm.t(\"color1rgb\")\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./result.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./result.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"comprehensive-result-container\">\r\n\t\t<!-- 顶部结果概览 -->\r\n\t\t<view class=\"result-header\">\r\n\t\t\t<view class=\"header-content\">\r\n\t\t\t\t<view class=\"images-section\">\r\n\t\t\t\t\t<view class=\"image-grid\">\r\n\t\t\t\t\t\t<view class=\"image-item\" v-if=\"resultData.tongue_image\">\r\n\t\t\t\t\t\t\t<image :src=\"resultData.tongue_image\" mode=\"aspectFit\"/>\r\n\t\t\t\t\t\t\t<text class=\"image-label\">舌诊</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"image-item\" v-if=\"resultData.face_image\">\r\n\t\t\t\t\t\t\t<image :src=\"resultData.face_image\" mode=\"aspectFit\"/>\r\n\t\t\t\t\t\t\t<text class=\"image-label\">面诊</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"image-item\" v-if=\"resultData.sublingual_image\">\r\n\t\t\t\t\t\t\t<image :src=\"resultData.sublingual_image\" mode=\"aspectFit\"/>\r\n\t\t\t\t\t\t\t<text class=\"image-label\">舌下脉络</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"result-summary\">\r\n\t\t\t\t\t<view class=\"summary-header\">\r\n\t\t\t\t\t\t<text class=\"summary-title\">综合诊疗报告</text>\r\n\t\t\t\t\t\t<text class=\"summary-date\">{{ currentDate }}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"comprehensive-score\" v-if=\"resultData.comprehensive_score\">\r\n\t\t\t\t\t\t<view class=\"score-container\">\r\n\t\t\t\t\t\t\t<view class=\"score-circle\">\r\n\t\t\t\t\t\t\t\t<text class=\"score-number\">{{ resultData.comprehensive_score }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"score-label\">分</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"score-details\">\r\n\t\t\t\t\t\t\t\t<text class=\"score-desc\">综合健康评分</text>\r\n\t\t\t\t\t\t\t\t<view class=\"score-level\" :class=\"scoreLevelClass\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"level-text\">{{ scoreLevelText }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 分析结果概览 -->\r\n\t\t<view class=\"overview-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">分析结果概览</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"overview-cards\">\r\n\t\t\t\t<view class=\"overview-card\" v-if=\"resultData.tongue_image\">\r\n\t\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t\t<text class=\"card-icon\">👅</text>\r\n\t\t\t\t\t\t<text class=\"card-title\">舌诊分析</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t<text class=\"card-result\">{{ tongueResult.constitution_type || '正常' }}</text>\r\n\t\t\t\t\t\t<text class=\"card-score\">评分：{{ tongueResult.constitution_score || '85' }}分</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"overview-card\" v-if=\"resultData.face_image\">\r\n\t\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t\t<text class=\"card-icon\">😊</text>\r\n\t\t\t\t\t\t<text class=\"card-title\">面诊分析</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t<text class=\"card-result\">{{ faceResult.face_status || '气血充足' }}</text>\r\n\t\t\t\t\t\t<text class=\"card-score\">评分：{{ faceResult.face_score || '82' }}分</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"overview-card\" v-if=\"resultData.sublingual_image\">\r\n\t\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t\t<text class=\"card-icon\">🩸</text>\r\n\t\t\t\t\t\t<text class=\"card-title\">舌下脉络</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t\t<text class=\"card-result\">{{ sublingualResult.vein_status || '血液循环良好' }}</text>\r\n\t\t\t\t\t\t<text class=\"card-score\">评分：{{ sublingualResult.vein_score || '88' }}分</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 详细分析 -->\r\n\t\t<view class=\"analysis-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">详细分析报告</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"analysis-tabs\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"tab-item\"\r\n\t\t\t\t\t:class=\"{ active: activeTab === 'comprehensive' }\"\r\n\t\t\t\t\t@click=\"switchTab('comprehensive')\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"tab-text\">综合分析</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"tab-item\"\r\n\t\t\t\t\t:class=\"{ active: activeTab === 'individual' }\"\r\n\t\t\t\t\t@click=\"switchTab('individual')\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"tab-text\">单项分析</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"tab-item\"\r\n\t\t\t\t\t:class=\"{ active: activeTab === 'advice' }\"\r\n\t\t\t\t\t@click=\"switchTab('advice')\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"tab-text\">调理建议</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"tab-content-wrapper\">\r\n\t\t\t\t<!-- 综合分析 -->\r\n\t\t\t\t<view v-if=\"activeTab === 'comprehensive'\" class=\"content-panel comprehensive-panel\">\r\n\t\t\t\t\t<view class=\"comprehensive-analysis\">\r\n\t\t\t\t\t\t<view class=\"analysis-item\">\r\n\t\t\t\t\t\t\t<view class=\"item-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"item-title\">整体健康状况</text>\r\n\t\t\t\t\t\t\t\t<view class=\"item-level normal\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"level-text\">良好</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"content-text\">{{ comprehensiveAnalysis.overall_health || '根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"analysis-item\">\r\n\t\t\t\t\t\t\t<view class=\"item-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"item-title\">体质类型</text>\r\n\t\t\t\t\t\t\t\t<view class=\"item-level normal\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"level-text\">{{ comprehensiveAnalysis.constitution_type || '平和质' }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"content-text\">{{ comprehensiveAnalysis.constitution_desc || '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"analysis-item\">\r\n\t\t\t\t\t\t\t<view class=\"item-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"item-title\">健康建议</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"item-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"content-text\">{{ comprehensiveAnalysis.health_advice || '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 单项分析 -->\r\n\t\t\t\t<view v-if=\"activeTab === 'individual'\" class=\"content-panel individual-panel\">\r\n\t\t\t\t\t<view class=\"individual-analysis\">\r\n\t\t\t\t\t\t<view class=\"individual-item\" v-if=\"resultData.tongue_image\">\r\n\t\t\t\t\t\t\t<view class=\"item-title\">舌诊分析详情</view>\r\n\t\t\t\t\t\t\t<view class=\"item-details\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-text\">{{ tongueAnalysis.detailed_analysis || '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"individual-item\" v-if=\"resultData.face_image\">\r\n\t\t\t\t\t\t\t<view class=\"item-title\">面诊分析详情</view>\r\n\t\t\t\t\t\t\t<view class=\"item-details\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-text\">{{ faceAnalysis.detailed_analysis || '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"individual-item\" v-if=\"resultData.sublingual_image\">\r\n\t\t\t\t\t\t\t<view class=\"item-title\">舌下脉络分析详情</view>\r\n\t\t\t\t\t\t\t<view class=\"item-details\">\r\n\t\t\t\t\t\t\t\t<text class=\"detail-text\">{{ sublingualAnalysis.detailed_analysis || '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 调理建议 -->\r\n\t\t\t\t<view v-if=\"activeTab === 'advice'\" class=\"content-panel advice-panel\">\r\n\t\t\t\t\t<view class=\"advice-list\">\r\n\t\t\t\t\t\t<view class=\"advice-item\">\r\n\t\t\t\t\t\t\t<view class=\"advice-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-icon\">🍎</text>\r\n\t\t\t\t\t\t\t\t<text class=\"advice-title\">饮食调理</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"advice-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-text\">{{ careAdvice.diet || '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"advice-item\">\r\n\t\t\t\t\t\t\t<view class=\"advice-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-icon\">💤</text>\r\n\t\t\t\t\t\t\t\t<text class=\"advice-title\">作息调理</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"advice-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-text\">{{ careAdvice.sleep || '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"advice-item\">\r\n\t\t\t\t\t\t\t<view class=\"advice-header\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-icon\">🏃</text>\r\n\t\t\t\t\t\t\t\t<text class=\"advice-title\">运动调理</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"advice-content\">\r\n\t\t\t\t\t\t\t\t<text class=\"advice-text\">{{ careAdvice.exercise || '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。' }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 视频推荐区域 -->\r\n\t\t<view class=\"video-recommend-section\" v-if=\"recommendVideos.length > 0\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">🎬 体质调理视频推荐</text>\r\n\t\t\t\t<text class=\"section-subtitle\">根据您的体质特点，为您推荐专业调理视频</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"video-list\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"video-item\"\r\n\t\t\t\t\tv-for=\"(video, index) in recommendVideos\"\r\n\t\t\t\t\t:key=\"video.id\"\r\n\t\t\t\t\t@click=\"playVideo(video)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"video-cover\">\r\n\t\t\t\t\t\t<image :src=\"video.pic\" mode=\"aspectFill\" class=\"cover-image\"/>\r\n\t\t\t\t\t\t<view class=\"play-icon\">\r\n\t\t\t\t\t\t\t<text class=\"play-text\">▶</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"video-duration\" v-if=\"video.duration\">\r\n\t\t\t\t\t\t\t<text class=\"duration-text\">{{ video.duration }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"video-info\">\r\n\t\t\t\t\t\t<text class=\"video-title\">{{ video.name }}</text>\r\n\t\t\t\t\t\t<text class=\"video-reason\">{{ video.recommend_reason }}</text>\r\n\t\t\t\t\t\t<view class=\"video-stats\">\r\n\t\t\t\t\t\t\t<text class=\"stat-item\">👁 {{ video.view_num || 0 }}次观看</text>\r\n\t\t\t\t\t\t\t<text class=\"stat-item\">👍 {{ video.zan_num || 0 }}点赞</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部操作 -->\r\n\t\t<view class=\"bottom-actions\">\r\n\t\t\t<view class=\"action-btn secondary-btn\" @click=\"goBack\">\r\n\t\t\t\t<text class=\"btn-text\">返回</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"action-btn primary-btn\" @click=\"shareResult\" :style=\"{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}\">\r\n\t\t\t\t<text class=\"btn-text\">分享报告</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 视频播放器遮罩层 -->\r\n\t\t<view class=\"video-player-overlay\" v-if=\"showVideoPlayer\" @click=\"closeVideoPlayer\">\r\n\t\t\t<view class=\"video-player-container\" @click.stop>\r\n\t\t\t\t<view class=\"video-player-header\">\r\n\t\t\t\t\t<text class=\"video-player-title\">{{ currentVideo.name }}</text>\r\n\t\t\t\t\t<view class=\"close-btn\" @click=\"closeVideoPlayer\">\r\n\t\t\t\t\t\t<text class=\"close-text\">✕</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<video\r\n\t\t\t\t\t:src=\"currentVideo.url\"\r\n\t\t\t\t\t:poster=\"currentVideo.pic\"\r\n\t\t\t\t\tcontrols\r\n\t\t\t\t\tautoplay\r\n\t\t\t\t\tplaysinline\r\n\t\t\t\t\twebkit-playsinline\r\n\t\t\t\t\tx5-playsinline\r\n\t\t\t\t\tclass=\"video-player\"\r\n\t\t\t\t\t@error=\"onVideoError\"\r\n\t\t\t\t\t@play=\"onVideoPlay\"\r\n\t\t\t\t\t@pause=\"onVideoPause\"\r\n\t\t\t\t\t@loadstart=\"onVideoLoadStart\"\r\n\t\t\t\t\t@canplay=\"onVideoCanPlay\"\r\n\t\t\t\t></video>\r\n\t\t\t\t<view class=\"video-player-info\">\r\n\t\t\t\t\t<text class=\"video-description\">{{ currentVideo.recommend_reason }}</text>\r\n\t\t\t\t\t<view class=\"video-stats-row\">\r\n\t\t\t\t\t\t<text class=\"stat-item\">👁 {{ currentVideo.view_num || 0 }}次观看</text>\r\n\t\t\t\t\t\t<text class=\"stat-item\">👍 {{ currentVideo.zan_num || 0 }}点赞</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\trecordId: '',\r\n\t\t\tresultData: {},\r\n\t\t\tactiveTab: 'comprehensive',\r\n\t\t\ttongueResult: {},\r\n\t\t\tfaceResult: {},\r\n\t\t\tsublingualResult: {},\r\n\t\t\tcomprehensiveAnalysis: {},\r\n\t\t\ttongueAnalysis: {},\r\n\t\t\tfaceAnalysis: {},\r\n\t\t\tsublingualAnalysis: {},\r\n\t\t\tcareAdvice: {},\r\n\t\t\trecommendVideos: [], // 推荐视频列表\r\n\t\t\tshowVideoPlayer: false, // 是否显示视频播放器\r\n\t\t\tcurrentVideo: {} // 当前播放的视频\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tcurrentDate() {\r\n\t\t\tconst now = new Date();\r\n\t\t\treturn now.getFullYear() + '-' +\r\n\t\t\t\t   String(now.getMonth() + 1).padStart(2, '0') + '-' +\r\n\t\t\t\t   String(now.getDate()).padStart(2, '0');\r\n\t\t},\r\n\t\tscoreLevelClass() {\r\n\t\t\tif (!this.resultData.comprehensive_score) return 'normal';\r\n\t\t\tconst score = parseInt(this.resultData.comprehensive_score);\r\n\t\t\tif (score >= 80) return 'excellent';\r\n\t\t\tif (score >= 60) return 'good';\r\n\t\t\tif (score >= 40) return 'normal';\r\n\t\t\treturn 'poor';\r\n\t\t},\r\n\t\tscoreLevelText() {\r\n\t\t\tif (!this.resultData.comprehensive_score) return '正常';\r\n\t\t\tconst score = parseInt(this.resultData.comprehensive_score);\r\n\t\t\tif (score >= 80) return '优秀';\r\n\t\t\tif (score >= 60) return '良好';\r\n\t\t\tif (score >= 40) return '正常';\r\n\t\t\treturn '需要关注';\r\n\t\t}\r\n\t},\r\n\tonLoad(options) {\r\n\t\tif (options.recordId) {\r\n\t\t\tthis.recordId = options.recordId;\r\n\t\t\tthis.getAnalysisRecord();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取分析记录 - 2025-07-17 修改为使用新的综合诊疗接口\r\n\t\tgetAnalysisRecord() {\r\n\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_001] 开始获取综合诊疗分析记录');\r\n\r\n\t\t\tconst app = getApp();\r\n\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '加载中...'\r\n\t\t\t});\r\n\r\n\t\t\t// 2025-07-17 使用新的综合诊疗接口获取记录\r\n\t\t\tapp.post('ApiComprehensiveAnalysis/getRecord', {\r\n\t\t\t\tid: this.recordId\r\n\t\t\t}, (response) => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_002] 获取综合诊疗记录结果:', response);\r\n\r\n\t\t\t\tif (response && response.code === 1) {\r\n\t\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][getAnalysisRecord_003] 获取记录成功，开始解析数据');\r\n\t\t\t\t\tthis.resultData = response.data;\r\n\t\t\t\t\tthis.parseNewAnalysisResult();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_004] 获取记录失败:', response?.msg);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response?.msg || '获取记录失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 降级到模拟数据，确保页面能正常显示\r\n\t\t\t\t\tthis.loadMockData();\r\n\t\t\t\t}\r\n\t\t\t}, (error) => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('2025-07-17 ERROR-[comprehensive-result][getAnalysisRecord_005] 获取记录接口调用失败:', error);\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 降级到模拟数据，确保页面能正常显示\r\n\t\t\t\tthis.loadMockData();\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析新综合诊疗接口的分析结果\r\n\t\tparseNewAnalysisResult() {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_001] 开始解析新综合诊疗分析结果');\r\n\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_002] 原始数据:', this.resultData);\r\n\r\n\t\t\t\t// 从新接口数据中提取信息\r\n\t\t\t\tconst analysisResult = this.resultData.analysis_result || {};\r\n\t\t\t\tlet analysisData = {};\r\n\r\n\t\t\t\t// 处理 analysis_result 数据\r\n\t\t\t\tif (typeof analysisResult === 'string') {\r\n\t\t\t\t\tanalysisData = JSON.parse(analysisResult);\r\n\t\t\t\t} else if (typeof analysisResult === 'object') {\r\n\t\t\t\t\tanalysisData = analysisResult;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_003] 解析后的分析数据:', analysisData);\r\n\r\n\t\t\t\t// 检查是否有新接口的数据结构\r\n\t\t\t\tif (analysisData.raw_report_data) {\r\n\t\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_004] 发现新接口数据结构');\r\n\t\t\t\t\tthis.parseNewApiData(analysisData.raw_report_data);\r\n\t\t\t\t} else if (analysisData.physique_name || analysisData.score) {\r\n\t\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_005] 发现API数据结构');\r\n\t\t\t\t\tthis.parseNewApiData(analysisData);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewAnalysisResult_006] 使用旧版解析方法');\r\n\t\t\t\t\tthis.parseAnalysisResult();\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('2025-07-17 ERROR-[comprehensive-result][parseNewAnalysisResult_007] 解析新综合诊疗结果失败:', error);\r\n\t\t\t\t// 回退到旧版解析方法\r\n\t\t\t\tthis.parseAnalysisResult();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析新API数据\r\n\t\tparseNewApiData(apiData) {\r\n\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewApiData_001] 开始解析新API数据:', apiData);\r\n\r\n\t\t\t// 更新基础信息\r\n\t\t\tif (apiData.score) {\r\n\t\t\t\tthis.resultData.comprehensive_score = apiData.score;\r\n\t\t\t}\r\n\r\n\t\t\t// 解析特征数据 - 分类处理舌部、面部、舌下特征\r\n\t\t\tif (apiData.features && Array.isArray(apiData.features)) {\r\n\t\t\t\tthis.parseNewComprehensiveFeatures(apiData.features);\r\n\t\t\t} else {\r\n\t\t\t\tthis.parseDefaultFeatures();\r\n\t\t\t}\r\n\r\n\t\t\t// 解析综合健康分析\r\n\t\t\tthis.parseNewComprehensiveAnalysis(apiData);\r\n\r\n\t\t\t// 解析调理建议 - 从 advices 对象中提取\r\n\t\t\tif (apiData.advices) {\r\n\t\t\t\tthis.parseNewComprehensiveCareAdvice(apiData.advices);\r\n\t\t\t} else {\r\n\t\t\t\tthis.parseDefaultCareAdvice();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析新API的综合特征\r\n\t\tparseNewComprehensiveFeatures(features) {\r\n\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveFeatures_001] 解析综合特征:', features);\r\n\r\n\t\t\t// 分类处理不同部位的特征\r\n\t\t\tconst tongueFeatures = features.filter(f => f.feature_category === '舌部');\r\n\t\t\tconst faceFeatures = features.filter(f => f.feature_category === '面部');\r\n\t\t\tconst sublingualFeatures = features.filter(f => f.feature_category === '舌下');\r\n\r\n\t\t\t// 解析舌诊结果\r\n\t\t\tif (this.resultData.tongue_image && tongueFeatures.length > 0) {\r\n\t\t\t\tconst tongueScore = this.calculateFeatureScore(tongueFeatures);\r\n\t\t\t\tthis.tongueResult = {\r\n\t\t\t\t\tconstitution_type: this.getConstitutionFromFeatures(tongueFeatures),\r\n\t\t\t\t\tconstitution_score: tongueScore\r\n\t\t\t\t};\r\n\t\t\t\tthis.tongueAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: this.generateFeatureAnalysis(tongueFeatures, '舌诊')\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\t// 解析面诊结果\r\n\t\t\tif (this.resultData.face_image && faceFeatures.length > 0) {\r\n\t\t\t\tconst faceScore = this.calculateFeatureScore(faceFeatures);\r\n\t\t\t\tthis.faceResult = {\r\n\t\t\t\t\tface_status: this.getFaceStatusFromFeatures(faceFeatures),\r\n\t\t\t\t\tface_score: faceScore\r\n\t\t\t\t};\r\n\t\t\t\tthis.faceAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: this.generateFeatureAnalysis(faceFeatures, '面诊')\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\t// 解析舌下脉络结果\r\n\t\t\tif (this.resultData.sublingual_image && sublingualFeatures.length > 0) {\r\n\t\t\t\tconst sublingualScore = this.calculateFeatureScore(sublingualFeatures);\r\n\t\t\t\tthis.sublingualResult = {\r\n\t\t\t\t\tvein_status: this.getVeinStatusFromFeatures(sublingualFeatures),\r\n\t\t\t\t\tvein_score: sublingualScore\r\n\t\t\t\t};\r\n\t\t\t\tthis.sublingualAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: this.generateFeatureAnalysis(sublingualFeatures, '舌下脉络')\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\t// 获取推荐视频\r\n\t\t\tthis.getRecommendVideos();\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析新API的综合健康分析\r\n\t\tparseNewComprehensiveAnalysis(apiData) {\r\n\t\t\tconst score = apiData.score || 85;\r\n\t\t\tconst physiqueName = apiData.physique_name || '未知体质';\r\n\t\t\tconst physiqueAnalysis = apiData.physique_analysis || '体质分析中...';\r\n\t\t\tconst riskWarning = apiData.risk_warning || '请注意保持健康的生活方式';\r\n\r\n\t\t\tthis.comprehensiveAnalysis = {\r\n\t\t\t\toverall_health: physiqueAnalysis,\r\n\t\t\t\tconstitution_type: physiqueName,\r\n\t\t\t\tconstitution_desc: this.getConstitutionDescription(physiqueName),\r\n\t\t\t\thealth_advice: riskWarning\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析新API的综合调理建议\r\n\t\tparseNewComprehensiveCareAdvice(advices) {\r\n\t\t\tconsole.log('2025-07-17 INFO-[comprehensive-result][parseNewComprehensiveCareAdvice_001] 解析综合调理建议:', advices);\r\n\r\n\t\t\t// 饮食建议\r\n\t\t\tlet dietAdvice = '保持均衡饮食，多吃新鲜蔬果';\r\n\t\t\tif (advices.food && Array.isArray(advices.food)) {\r\n\t\t\t\tdietAdvice = advices.food.map(item => item.advice || item.title).join('；');\r\n\t\t\t}\r\n\r\n\t\t\t// 运动建议\r\n\t\t\tlet exerciseAdvice = '适当进行有氧运动，增强体质';\r\n\t\t\tif (advices.sport && Array.isArray(advices.sport)) {\r\n\t\t\t\texerciseAdvice = advices.sport.map(item => item.advice || item.title).join('；');\r\n\t\t\t}\r\n\r\n\t\t\t// 生活建议\r\n\t\t\tlet sleepAdvice = '保持规律作息，早睡早起';\r\n\t\t\tif (advices.sleep && Array.isArray(advices.sleep)) {\r\n\t\t\t\tsleepAdvice = advices.sleep.map(item => item.advice || item.title).join('；');\r\n\t\t\t}\r\n\r\n\t\t\tthis.careAdvice = {\r\n\t\t\t\tdiet: dietAdvice,\r\n\t\t\t\texercise: exerciseAdvice,\r\n\t\t\t\tsleep: sleepAdvice\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：计算特征评分\r\n\t\tcalculateFeatureScore(features) {\r\n\t\t\tif (!features || features.length === 0) return 85;\r\n\r\n\t\t\tlet normalCount = 0;\r\n\t\t\tfeatures.forEach(feature => {\r\n\t\t\t\tif (feature.feature_situation === '正常') {\r\n\t\t\t\t\tnormalCount++;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tconst normalRatio = normalCount / features.length;\r\n\t\t\treturn Math.round(60 + normalRatio * 40); // 60-100分范围\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：从特征获取体质类型\r\n\t\tgetConstitutionFromFeatures(features) {\r\n\t\t\t// 简单的体质判断逻辑，可以根据实际需要完善\r\n\t\t\tconst abnormalFeatures = features.filter(f => f.feature_situation === '异常');\r\n\t\t\tif (abnormalFeatures.length === 0) return '平和质';\r\n\t\t\tif (abnormalFeatures.length <= 2) return '偏颇体质';\r\n\t\t\treturn '需要调理';\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：从面部特征获取面部状态\r\n\t\tgetFaceStatusFromFeatures(features) {\r\n\t\t\tconst abnormalFeatures = features.filter(f => f.feature_situation === '异常');\r\n\t\t\tif (abnormalFeatures.length === 0) return '气血充足';\r\n\t\t\tif (abnormalFeatures.length <= 1) return '气血较好';\r\n\t\t\treturn '需要调理';\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：从舌下特征获取脉络状态\r\n\t\tgetVeinStatusFromFeatures(features) {\r\n\t\t\tconst abnormalFeatures = features.filter(f => f.feature_situation === '异常');\r\n\t\t\tif (abnormalFeatures.length === 0) return '血液循环良好';\r\n\t\t\tif (abnormalFeatures.length <= 1) return '血液循环较好';\r\n\t\t\treturn '血液循环需要改善';\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：生成特征分析文本\r\n\t\tgenerateFeatureAnalysis(features, type) {\r\n\t\t\tconst normalFeatures = features.filter(f => f.feature_situation === '正常');\r\n\t\t\tconst abnormalFeatures = features.filter(f => f.feature_situation === '异常');\r\n\r\n\t\t\tlet analysis = `${type}显示：`;\r\n\r\n\t\t\tif (normalFeatures.length > 0) {\r\n\t\t\t\tconst normalNames = normalFeatures.map(f => f.feature_name).join('、');\r\n\t\t\t\tanalysis += `${normalNames}正常；`;\r\n\t\t\t}\r\n\r\n\t\t\tif (abnormalFeatures.length > 0) {\r\n\t\t\t\tconst abnormalNames = abnormalFeatures.map(f => f.feature_name).join('、');\r\n\t\t\t\tanalysis += `${abnormalNames}需要关注。`;\r\n\t\t\t} else {\r\n\t\t\t\tanalysis += '整体状况良好。';\r\n\t\t\t}\r\n\r\n\t\t\treturn analysis;\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：获取体质描述\r\n\t\tgetConstitutionDescription(constitutionType) {\r\n\t\t\tconst descriptions = {\r\n\t\t\t\t'平和质': '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',\r\n\t\t\t\t'气虚质': '元气不足，以疲乏、气短、自汗等气虚表现为主要特征。',\r\n\t\t\t\t'阳虚质': '阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。',\r\n\t\t\t\t'阴虚质': '阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。',\r\n\t\t\t\t'痰湿质': '痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。',\r\n\t\t\t\t'湿热质': '湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。',\r\n\t\t\t\t'血瘀质': '血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。',\r\n\t\t\t\t'气郁质': '气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。',\r\n\t\t\t\t'特禀质': '先天失常，以生理缺陷、过敏反应等为主要特征。'\r\n\t\t\t};\r\n\t\t\treturn descriptions[constitutionType] || '体质特征分析中，请咨询专业医师。';\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：加载模拟数据\r\n\t\tloadMockData() {\r\n\t\t\tthis.resultData = {\r\n\t\t\t\tcomprehensive_score: 85,\r\n\t\t\t\ttongue_image: this.resultData?.tongue_image || '',\r\n\t\t\t\tface_image: this.resultData?.face_image || '',\r\n\t\t\t\tsublingual_image: this.resultData?.sublingual_image || ''\r\n\t\t\t};\r\n\r\n\t\t\tthis.parseDefaultFeatures();\r\n\t\t\tthis.parseDefaultCareAdvice();\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析默认特征\r\n\t\tparseDefaultFeatures() {\r\n\t\t\tif (this.resultData.tongue_image) {\r\n\t\t\t\tthis.tongueResult = {\r\n\t\t\t\t\tconstitution_type: '平和质',\r\n\t\t\t\t\tconstitution_score: 85\r\n\t\t\t\t};\r\n\t\t\t\tthis.tongueAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\tif (this.resultData.face_image) {\r\n\t\t\t\tthis.faceResult = {\r\n\t\t\t\t\tface_status: '气血充足',\r\n\t\t\t\t\tface_score: 82\r\n\t\t\t\t};\r\n\t\t\t\tthis.faceAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\tif (this.resultData.sublingual_image) {\r\n\t\t\t\tthis.sublingualResult = {\r\n\t\t\t\t\tvein_status: '血液循环良好',\r\n\t\t\t\t\tvein_score: 88\r\n\t\t\t\t};\r\n\t\t\t\tthis.sublingualAnalysis = {\r\n\t\t\t\t\tdetailed_analysis: '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'\r\n\t\t\t\t};\r\n\t\t\t}\r\n\r\n\t\t\tthis.comprehensiveAnalysis = {\r\n\t\t\t\toverall_health: '根据综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',\r\n\t\t\t\tconstitution_type: '平和质',\r\n\t\t\t\tconstitution_desc: '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',\r\n\t\t\t\thealth_advice: '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// 2025-07-17 新增：解析默认调理建议\r\n\t\tparseDefaultCareAdvice() {\r\n\t\t\tthis.careAdvice = {\r\n\t\t\t\tdiet: '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',\r\n\t\t\t\tsleep: '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',\r\n\t\t\t\texercise: '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// 解析分析结果（保留旧版本兼容）\r\n\t\tparseAnalysisResult() {\r\n\t\t\ttry {\r\n\t\t\t\t// 解析主要分析结果\r\n\t\t\t\tconst analysisData = JSON.parse(this.resultData.analysis_result || '{}');\r\n\r\n\t\t\t\t// 解析舌诊结果\r\n\t\t\t\tif (this.resultData.tongue_image) {\r\n\t\t\t\t\tthis.tongueResult = {\r\n\t\t\t\t\t\tconstitution_type: analysisData.constitution_type || '平和质',\r\n\t\t\t\t\t\tconstitution_score: analysisData.constitution_score || 85\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.tongueAnalysis = {\r\n\t\t\t\t\t\tdetailed_analysis: analysisData.tongue_detailed || '舌质淡红，舌苔薄白，舌体大小适中，说明脾胃功能正常，气血充足。'\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析面诊结果\r\n\t\t\t\tif (this.resultData.face_image) {\r\n\t\t\t\t\tthis.faceResult = {\r\n\t\t\t\t\t\tface_status: analysisData.face_status || '气血充足',\r\n\t\t\t\t\t\tface_score: analysisData.face_score || 82\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.faceAnalysis = {\r\n\t\t\t\t\t\tdetailed_analysis: analysisData.face_detailed || '面色红润有光泽，眼神明亮有神，唇色淡红，说明气血运行良好，精神状态佳。'\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析舌下脉络结果\r\n\t\t\t\tif (this.resultData.sublingual_image) {\r\n\t\t\t\t\tthis.sublingualResult = {\r\n\t\t\t\t\t\tvein_status: analysisData.vein_status || '血液循环良好',\r\n\t\t\t\t\t\tvein_score: analysisData.vein_score || 88\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.sublingualAnalysis = {\r\n\t\t\t\t\t\tdetailed_analysis: analysisData.sublingual_detailed || '舌下脉络清晰可见，颜色正常，粗细适中，说明血液循环良好，无明显瘀血现象。'\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析综合分析\r\n\t\t\t\tthis.comprehensiveAnalysis = {\r\n\t\t\t\t\toverall_health: analysisData.overall_health || '根据舌诊、面诊和舌下脉络综合分析，您的整体健康状况良好，气血运行正常，脏腑功能协调。',\r\n\t\t\t\t\tconstitution_type: analysisData.comprehensive_constitution || '平和质',\r\n\t\t\t\t\tconstitution_desc: analysisData.comprehensive_desc || '体质平和，阴阳气血调和，脏腑功能正常，适应能力强。',\r\n\t\t\t\t\thealth_advice: analysisData.comprehensive_advice || '保持现有的良好生活习惯，注意饮食均衡，适量运动，保持心情愉悦。'\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// 解析调理建议\r\n\t\t\t\tthis.careAdvice = {\r\n\t\t\t\t\tdiet: analysisData.diet_advice || '保持均衡饮食，多吃新鲜蔬果，适量摄入优质蛋白质，少食辛辣刺激食物。',\r\n\t\t\t\t\tsleep: analysisData.sleep_advice || '保持规律作息，早睡早起，保证充足睡眠，避免熬夜。',\r\n\t\t\t\t\texercise: analysisData.exercise_advice || '适当进行有氧运动，如散步、慢跑、太极拳等，增强体质。'\r\n\t\t\t\t};\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('解析分析结果失败:', error);\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 切换标签\r\n\t\tswitchTab(tab) {\r\n\t\t\tthis.activeTab = tab;\r\n\t\t},\r\n\r\n\t\t// 返回\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack();\r\n\t\t},\r\n\r\n\t\t// 分享结果\r\n\t\tshareResult() {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '分享功能开发中',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 获取推荐视频\r\n\t\tgetRecommendVideos() {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_001] 开始获取推荐视频');\r\n\r\n\t\t\t// 获取体质类型和得分\r\n\t\t\tlet constitutionType = '';\r\n\t\t\tlet constitutionScore = 0;\r\n\r\n\t\t\tif (this.tongueResult && this.tongueResult.constitution_type) {\r\n\t\t\t\tconstitutionType = this.tongueResult.constitution_type;\r\n\t\t\t}\r\n\t\t\tif (this.tongueResult && this.tongueResult.constitution_score) {\r\n\t\t\t\tconstitutionScore = parseInt(this.tongueResult.constitution_score);\r\n\t\t\t}\r\n\r\n\t\t\t// 如果没有体质信息，不获取推荐视频\r\n\t\t\tif (!constitutionType && !constitutionScore) {\r\n\t\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_002] 无体质信息，跳过视频推荐');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconst app = getApp();\r\n\r\n\t\t\t// 调用推荐视频接口\r\n\t\t\tapp.post('ApiSheZhen/getRecommendProducts', {\r\n\t\t\t\tconstitution_type: constitutionType,\r\n\t\t\t\tconstitution_score: constitutionScore\r\n\t\t\t}, (response) => {\r\n\t\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_003] 获取推荐视频结果:', response);\r\n\r\n\t\t\t\tif (response && response.code === 1 && response.data && response.data.products) {\r\n\t\t\t\t\t// 筛选出视频类型的推荐\r\n\t\t\t\t\tconst videos = response.data.products.filter(item => item.type === 'video');\r\n\t\t\t\t\tthis.recommendVideos = videos;\r\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][getRecommendVideos_005] 无推荐视频数据');\r\n\t\t\t\t}\r\n\t\t\t}, (error) => {\r\n\t\t\t\tconsole.error('2025-01-31 ERROR-[comprehensive-result][getRecommendVideos_006] 获取推荐视频失败:', error);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 播放视频\r\n\t\tplayVideo(video) {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][playVideo_001] 播放视频:', video);\r\n\r\n\t\t\tif (!video.url) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '视频地址无效',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 处理视频URL，确保中文字符正确编码\r\n\t\t\tlet processedVideo = { ...video };\r\n\t\t\tif (video.url && video.url.includes('%')) {\r\n\t\t\t\t// URL已经编码，直接使用\r\n\t\t\t\tprocessedVideo.url = video.url;\r\n\t\t\t} else if (video.url) {\r\n\t\t\t\t// 对URL中的中文字符进行编码\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst urlParts = video.url.split('/');\r\n\t\t\t\t\tconst encodedParts = urlParts.map(part => {\r\n\t\t\t\t\t\t// 只对文件名部分进行编码，保留协议和域名\r\n\t\t\t\t\t\tif (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {\r\n\t\t\t\t\t\t\treturn encodeURIComponent(part);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn part;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tprocessedVideo.url = encodedParts.join('/');\r\n\t\t\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][playVideo_001.5] URL编码处理:', {\r\n\t\t\t\t\t\toriginal: video.url,\r\n\t\t\t\t\t\tprocessed: processedVideo.url\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tconsole.warn('2025-01-31 WARN-[comprehensive-result][playVideo_001.6] URL编码失败，使用原始URL:', e);\r\n\t\t\t\t\tprocessedVideo.url = video.url;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 设置当前播放视频并显示播放器\r\n\t\t\tthis.currentVideo = processedVideo;\r\n\t\t\tthis.showVideoPlayer = true;\r\n\r\n\t\t\t// 记录视频播放事件\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][playVideo_002] 开始播放视频:', video.name);\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][playVideo_003] 视频URL:', processedVideo.url);\r\n\t\t},\r\n\r\n\t\t// 关闭视频播放器\r\n\t\tcloseVideoPlayer() {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][closeVideoPlayer_001] 关闭视频播放器');\r\n\t\t\tthis.showVideoPlayer = false;\r\n\t\t\tthis.currentVideo = {};\r\n\t\t},\r\n\r\n\t\t// 视频播放错误处理\r\n\t\tonVideoError(e) {\r\n\t\t\tconsole.error('2025-01-31 ERROR-[comprehensive-result][onVideoError_001] 视频播放错误:', e);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '视频播放失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t// 视频开始播放\r\n\t\tonVideoPlay(e) {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][onVideoPlay_001] 视频开始播放:', e);\r\n\t\t},\r\n\r\n\t\t// 视频暂停播放\r\n\t\tonVideoPause(e) {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][onVideoPause_001] 视频暂停播放:', e);\r\n\t\t},\r\n\r\n\t\t// 视频开始加载\r\n\t\tonVideoLoadStart(e) {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][onVideoLoadStart_001] 视频开始加载:', e);\r\n\t\t},\r\n\r\n\t\t// 视频可以播放\r\n\t\tonVideoCanPlay(e) {\r\n\t\t\tconsole.log('2025-01-31 INFO-[comprehensive-result][onVideoCanPlay_001] 视频可以播放:', e);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n.comprehensive-result-container {\r\n\tmin-height: 100vh;\r\n\tbackground: #f5f5f5;\r\n\tpadding-bottom: 120rpx;\r\n}\r\n\r\n.result-header {\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tpadding: 40rpx 30rpx;\r\n\tcolor: #fff;\r\n}\r\n\r\n.header-content {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.images-section {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.image-grid {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tjustify-content: center;\r\n}\r\n\r\n.image-item {\r\n\tposition: relative;\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 15rpx;\r\n\toverflow: hidden;\r\n\tbackground: rgba(255,255,255,0.1);\r\n}\r\n\r\n.image-item image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.image-label {\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: rgba(0,0,0,0.7);\r\n\tcolor: #fff;\r\n\tfont-size: 20rpx;\r\n\ttext-align: center;\r\n\tpadding: 6rpx;\r\n}\r\n\r\n.result-summary {\r\n\ttext-align: center;\r\n}\r\n\r\n.summary-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 600;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.summary-date {\r\n\tfont-size: 26rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.comprehensive-score {\r\n\tmargin-top: 30rpx;\r\n}\r\n\r\n.score-container {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.score-circle {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 50%;\r\n\tbackground: rgba(255,255,255,0.2);\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tmargin-right: 30rpx;\r\n}\r\n\r\n.score-number {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 600;\r\n\tline-height: 1;\r\n}\r\n\r\n.score-label {\r\n\tfont-size: 22rpx;\r\n\topacity: 0.8;\r\n}\r\n\r\n.score-details {\r\n\ttext-align: left;\r\n}\r\n\r\n.score-desc {\r\n\tfont-size: 28rpx;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.score-level {\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: inline-block;\r\n}\r\n\r\n.score-level.excellent {\r\n\tbackground: rgba(76, 175, 80, 0.3);\r\n}\r\n\r\n.score-level.good {\r\n\tbackground: rgba(139, 195, 74, 0.3);\r\n}\r\n\r\n.score-level.normal {\r\n\tbackground: rgba(255, 193, 7, 0.3);\r\n}\r\n\r\n.score-level.poor {\r\n\tbackground: rgba(244, 67, 54, 0.3);\r\n}\r\n\r\n.level-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #fff;\r\n}\r\n\r\n.overview-section, .analysis-section {\r\n\tmargin: 30rpx;\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 40rpx;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);\r\n}\r\n\r\n.section-header {\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.overview-cards {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.overview-card {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.card-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.card-icon {\r\n\tfont-size: 32rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.card-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.card-content {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n}\r\n\r\n.card-result {\r\n\tfont-size: 26rpx;\r\n\tcolor: #007aff;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.card-score {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.analysis-tabs {\r\n\tdisplay: flex;\r\n\tbackground: #f5f5f5;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 8rpx;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 10rpx;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground: #007aff;\r\n\tcolor: #fff;\r\n}\r\n\r\n.tab-text {\r\n\tfont-size: 26rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.content-panel {\r\n\tmin-height: 300rpx;\r\n}\r\n\r\n.comprehensive-analysis, .individual-analysis, .advice-list {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.analysis-item, .individual-item, .advice-item {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.item-header, .advice-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.item-title, .advice-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n}\r\n\r\n.item-level {\r\n\tpadding: 6rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n\tfont-size: 22rpx;\r\n}\r\n\r\n.item-level.normal {\r\n\tbackground: #e8f5e8;\r\n\tcolor: #4caf50;\r\n}\r\n\r\n.item-content, .item-details, .advice-content {\r\n\tmargin-top: 15rpx;\r\n}\r\n\r\n.content-text, .detail-text, .advice-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.advice-icon {\r\n\tfont-size: 32rpx;\r\n\tmargin-right: 15rpx;\r\n}\r\n\r\n.bottom-actions {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbackground: #fff;\r\n\tpadding: 30rpx;\r\n\tborder-top: 1px solid #eee;\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 88rpx;\r\n\tborder-radius: 44rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.secondary-btn {\r\n\tbackground: #f5f5f5;\r\n\tcolor: #666;\r\n}\r\n\r\n.primary-btn {\r\n\tcolor: #fff;\r\n}\r\n\r\n.btn-text {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n}\r\n\r\n/* 视频推荐区域样式 */\r\n.video-recommend-section {\r\n\tbackground: #fff;\r\n\tmargin: 20rpx;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.section-subtitle {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.video-list {\r\n\tmargin-top: 30rpx;\r\n}\r\n\r\n.video-item {\r\n\tdisplay: flex;\r\n\tmargin-bottom: 30rpx;\r\n\tpadding: 20rpx;\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 15rpx;\r\n\tborder: 1px solid #eee;\r\n}\r\n\r\n.video-item:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.video-cover {\r\n\tposition: relative;\r\n\twidth: 200rpx;\r\n\theight: 120rpx;\r\n\tborder-radius: 10rpx;\r\n\toverflow: hidden;\r\n\tmargin-right: 20rpx;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.cover-image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.play-icon {\r\n\tposition: absolute;\r\n\ttop: 50%;\r\n\tleft: 50%;\r\n\ttransform: translate(-50%, -50%);\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tbackground: rgba(0, 0, 0, 0.6);\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.play-text {\r\n\tcolor: #fff;\r\n\tfont-size: 24rpx;\r\n\tmargin-left: 4rpx;\r\n}\r\n\r\n.video-duration {\r\n\tposition: absolute;\r\n\tbottom: 8rpx;\r\n\tright: 8rpx;\r\n\tbackground: rgba(0, 0, 0, 0.7);\r\n\tcolor: #fff;\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 6rpx;\r\n}\r\n\r\n.duration-text {\r\n\tfont-size: 20rpx;\r\n}\r\n\r\n.video-info {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.video-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n\tline-height: 1.4;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.video-reason {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 15rpx;\r\n\tpadding: 8rpx 12rpx;\r\n\tbackground: #e8f5e8;\r\n\tcolor: #4caf50;\r\n\tborder-radius: 8rpx;\r\n\talign-self: flex-start;\r\n}\r\n\r\n.video-stats {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.stat-item {\r\n\tfont-size: 22rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n/* 视频播放器遮罩层样式 */\r\n.video-player-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.8);\r\n\tz-index: 9999;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tpadding: 40rpx;\r\n}\r\n\r\n.video-player-container {\r\n\tbackground: #fff;\r\n\tborder-radius: 20rpx;\r\n\toverflow: hidden;\r\n\twidth: 100%;\r\n\tmax-width: 640rpx;\r\n\tmax-height: 80vh;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.video-player-header {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: #f8f9fa;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.video-player-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 500;\r\n\tcolor: #333;\r\n\tflex: 1;\r\n\tmargin-right: 20rpx;\r\n}\r\n\r\n.close-btn {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: #f0f0f0;\r\n\tborder-radius: 50%;\r\n\tcursor: pointer;\r\n}\r\n\r\n.close-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #666;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.video-player {\r\n\twidth: 100%;\r\n\theight: 400rpx;\r\n\tbackground: #000;\r\n}\r\n\r\n.video-player-info {\r\n\tpadding: 20rpx 30rpx;\r\n\tbackground: #fff;\r\n}\r\n\r\n.video-description {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n\tmargin-bottom: 15rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.video-stats-row {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.video-stats-row .stat-item {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n</style>", "import mod from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./result.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Users\\\\<USER>\\\\Desktop\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./result.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754042130479\n      var cssReload = require(\"C:/Users/<USER>/Desktop/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}