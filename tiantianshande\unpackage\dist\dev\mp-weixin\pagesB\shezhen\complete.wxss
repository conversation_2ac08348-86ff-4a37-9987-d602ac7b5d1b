


















































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































































/* 2025-01-03 22:55:53,565-INF0-[complete][style_001] 舌诊完整页面样式定义 */
.tongue-complete.data-v-39446614 {
	background-color: #ffffff;
	min-height: 100vh;
	padding-bottom: 120rpx; /* 为底部浮动按钮留出空间 */
}

/* 顶部导航样式 */
.top-nav.data-v-39446614 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}
.nav-left.data-v-39446614 {
	display: flex;
	align-items: center;
}
.back-icon.data-v-39446614 {
	font-size: 40rpx;
	color: #333;
}
.nav-title.data-v-39446614 {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
}
.nav-right.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.menu-icon.data-v-39446614 {
	font-size: 30rpx;
	color: #666;
}
.record-icon.data-v-39446614 {
	font-size: 30rpx;
	color: #333;
}

/* 医院信息 */
.hospital-info.data-v-39446614 {
	background-color: #e8f4ff;
	padding: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: #666;
}

/* 报告标签页 */
.report-tabs.data-v-39446614 {
	display: flex;
	background-color: white;
	padding: 0 30rpx;
}
.tab-item.data-v-39446614 {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	position: relative;
}
.tab-item.active .tab-text.data-v-39446614 {
	color: #1890ff;
	font-weight: 500;
}
.tab-line.data-v-39446614 {
	position: absolute;
	bottom: 0;
	left: 50%;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #1890ff;
	border-radius: 3rpx;
}
.tab-text.data-v-39446614 {
	font-size: 32rpx;
	color: #333;
}

/* 健康得分卡片 */
.health-card.data-v-39446614 {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	border-radius: 20rpx;
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(79, 172, 254, 0.3);
}
.health-content.data-v-39446614 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.health-left.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.health-avatar.data-v-39446614 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	padding: 10rpx;
	object-fit: cover;
	object-position: center;
	flex-shrink: 0;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.health-symptoms.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	flex: 1;
	min-width: 0;
}
.main-symptom.data-v-39446614, .sub-symptom.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	flex-wrap: wrap;
}
.symptom-label.data-v-39446614 {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 400;
	white-space: nowrap;
}
.symptom-value.data-v-39446614 {
	font-size: 32rpx;
	color: white;
	font-weight: 600;
	background: rgba(255, 255, 255, 0.2);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	word-break: break-all;
	line-height: 1.4;
}
.health-right.data-v-39446614 {
	display: flex;
	align-items: center;
}
.score-circle.data-v-39446614 {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.4);
	border: 3rpx solid rgba(255, 255, 255, 0.5);
}
.score-inner.data-v-39446614 {
	text-align: center;
}
.score-number.data-v-39446614 {
	font-size: 48rpx;
	font-weight: 700;
	color: #4facfe;
	display: block;
	line-height: 1;
}
.score-number.no-score.data-v-39446614 {
	font-size: 48rpx;
	font-weight: 700;
	color: #cccccc;
	display: block;
	line-height: 1;
}
.score-text.data-v-39446614 {
	font-size: 24rpx;
	color: #4facfe;
	font-weight: 500;
	margin-top: 8rpx;
	display: block;
}

/* 智能问诊 */
.ai-diagnosis.data-v-39446614 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx;
	padding: 30rpx;
	background-color: white;
	border-radius: 15rpx;
}
.ai-left.data-v-39446614 {
	flex: 1;
}
.ai-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}
.ai-subtitle.data-v-39446614 {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 5rpx;
}
.ai-description.data-v-39446614 {
	font-size: 24rpx;
	color: #999;
}
.login-btn.data-v-39446614 {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}
.analysis-complete-btn.data-v-39446614 {
	background-color: #52c41a;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}
.loading-btn.data-v-39446614 {
	background-color: #faad14;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	-webkit-animation: pulse-data-v-39446614 1.5s infinite;
	        animation: pulse-data-v-39446614 1.5s infinite;
}
@-webkit-keyframes pulse-data-v-39446614 {
0% { opacity: 1;
}
50% { opacity: 0.6;
}
100% { opacity: 1;
}
}
@keyframes pulse-data-v-39446614 {
0% { opacity: 1;
}
50% { opacity: 0.6;
}
100% { opacity: 1;
}
}

/* 可能体征 */
.symptoms-section.data-v-39446614 {
	margin: 30rpx 20rpx;
}
.section-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
}
.symptoms-grid.data-v-39446614 {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
	margin-top: 20rpx;
}
.symptom-tag.data-v-39446614 {
	background: rgba(79, 172, 254, 0.08);
	color: #4facfe;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	border: 1rpx solid rgba(79, 172, 254, 0.2);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}

/* 舌象分析 */
.tongue-analysis.data-v-39446614 {
	margin: 30rpx;
}
.tongue-diagram.data-v-39446614 {
	background-color: white;
	border-radius: 15rpx;
	padding: 40rpx;
	position: relative;
	text-align: center;
}
.tongue-svg.data-v-39446614 {
	width: 600rpx;
	height: 480rpx;
	position: relative;
	background: #ffffff;
	border-radius: 15rpx;
	overflow: hidden;
	margin: 0 auto;
}

/* CSS绘制的舌头形状 */
.tongue-shape.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 350rpx;
	height: 400rpx;
	background: linear-gradient(135deg, #ffb3ba, #ff8a9b);
	border-radius: 85rpx 85rpx 175rpx 175rpx;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	box-shadow: inset 0 15rpx 30rpx rgba(255, 255, 255, 0.3),
	            inset 0 -15rpx 30rpx rgba(0, 0, 0, 0.1),
	            0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 舌根区域高光效果 */
.tongue-shape.data-v-39446614::before {
	content: '';
	position: absolute;
	top: 20rpx;
	left: 50%;
	width: 200rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
}

/* 舌苔覆盖区域 */
.tongue-shape.data-v-39446614::after {
	content: '';
	position: absolute;
	top: 15%;
	left: 50%;
	width: 280rpx;
	height: 280rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 60rpx 60rpx 140rpx 140rpx;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
	box-shadow: inset 0 5rpx 15rpx rgba(255, 255, 255, 0.2);
}
.tongue-markers.data-v-39446614 {
	position: relative;
	width: 100%;
	height: 100%;
	/* 确保小程序端的定位基准 */
	overflow: visible;
	z-index: 1;
}
.marker.data-v-39446614 {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 10;
	/* 确保小程序端的样式继承 */
	box-sizing: border-box;
	/* 默认居中对齐 */
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	/* 移除动态样式依赖，使用CSS类定位 */
	/* 适当缩小标记整体尺寸 */
	zoom: 0.9;
}
.marker.abnormal .marker-icon.data-v-39446614 {
	background: #ff4d4f;
	color: white;
}
.marker.normal .marker-icon.data-v-39446614 {
	background: #52c41a;
	color: white;
}
.marker-icon.data-v-39446614 {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}
.marker-text.data-v-39446614 {
	font-size: 20rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.95);
	padding: 3rpx 8rpx;
	border-radius: 10rpx;
	text-align: center;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
	white-space: nowrap;
	font-weight: 500;
	max-width: 120rpx; /* 限制文本宽度 */
	overflow: hidden;
	text-overflow: ellipsis;
}
.tongue-photo-btn.data-v-39446614 {
	position: absolute;
	bottom: 15rpx;
	left: 50%;
	-webkit-transform: translateX(-50%);
	        transform: translateX(-50%);
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 10rpx 25rpx;
	border-radius: 25rpx;
	font-size: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.photo-btn-text.data-v-39446614 {
	color: white;
	font-weight: 500;
}

/* 体征异常 */
.abnormal-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.abnormal-title.data-v-39446614 {
	margin-bottom: 30rpx;
}
.abnormal-count.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.abnormal-list.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
.abnormal-item.data-v-39446614 {
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 30rpx;
}
.abnormal-item.data-v-39446614:last-child {
	border-bottom: none;
	padding-bottom: 0;
}
.abnormal-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
}
.abnormal-icon.data-v-39446614 {
	width: 32rpx;
	height: 32rpx;
	background-color: #ff4d4f;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
}
.abnormal-name.data-v-39446614 {
	font-size: 30rpx;
	font-weight: 500;
	color: #ff4d4f;
}
.abnormal-desc.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}
.abnormal-desc-container.data-v-39446614 {
	display: flex;
	align-items: flex-end;
	gap: 20rpx;
}
.doctor-avatar.data-v-39446614 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	flex-shrink: 0;
}

/* 舌象体征论述 - 简约医疗风格 */
.theory-section.data-v-39446614 {
	margin: 0rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	/* box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2); */
}
.theory-header-wrapper.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid rgba(24, 144, 255, 0.1);
}
.theory-icon.data-v-39446614 {
	font-size: 40rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.3);
}
.theory-subtitle.data-v-39446614 {
	font-size: 24rpx;
	color: #8a92b2;
	font-weight: 500;
	margin-left: auto;
	background: rgba(255, 255, 255, 0.8);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 理论卡片 */
.theory-card.data-v-39446614 {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}
.theory-card.data-v-39446614:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}
.theory-card.data-v-39446614:last-child {
	margin-bottom: 0;
}

/* 体质分析卡片 */
.constitution-card.data-v-39446614 {
	border-left: 6rpx solid #52c41a;
	background: linear-gradient(135deg, rgba(246, 255, 237, 0.9), rgba(240, 249, 235, 0.7));
}
.constitution-card.data-v-39446614::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	background: radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, transparent 70%);
	border-radius: 50%;
}
.constitution-tag.data-v-39446614 {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}

/* 证候介绍卡片 */
.syndrome-card.data-v-39446614 {
	border-left: 6rpx solid #1890ff;
	background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(230, 247, 255, 0.7));
}
.syndrome-card.data-v-39446614::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
	border-radius: 50%;
}
.syndrome-tag.data-v-39446614 {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: white;
}

/* 卡片头部 */
.card-header.data-v-39446614 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}
.header-left.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 15rpx;
	flex: 1;
}
.theory-tag.data-v-39446614 {
	font-size: 22rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.theory-title.data-v-39446614 {
	font-size: 30rpx;
	font-weight: 600;
	color: #1a202c;
	letter-spacing: 0.5rpx;
}
.header-right.data-v-39446614 {
	display: flex;
	align-items: center;
}

/* 状态指示器 */
.status-indicator.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(255, 255, 255, 0.8);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.status-indicator.active .status-dot.data-v-39446614 {
	color: #52c41a;
	-webkit-animation: pulse-data-v-39446614 2s infinite;
	        animation: pulse-data-v-39446614 2s infinite;
}
.status-text.data-v-39446614 {
	font-size: 22rpx;
	font-weight: 500;
	color: #52c41a;
}
@keyframes pulse-data-v-39446614 {
0%, 100% { opacity: 1;
}
50% { opacity: 0.7;
}
}

/* 卡片内容 */
.card-content.data-v-39446614 {
	position: relative;
}
.content-wrapper.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.theory-description.data-v-39446614 {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	font-weight: 400;
	letter-spacing: 0.5rpx;
	text-align: justify;
	text-justify: inter-ideograph;
}

/* 医学印章 */
.medical-stamp.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 16rpx;
	padding: 12rpx 16rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 30rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	align-self: flex-end;
}
.doctor-avatar-small.data-v-39446614 {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	flex-shrink: 0;
}
.stamp-text.data-v-39446614 {
	font-size: 22rpx;
	color: #718096;
	font-weight: 500;
	letter-spacing: 0.5rpx;
}

/* 空状态 */
.empty-theory-state.data-v-39446614 {
	text-align: center;
	padding: 80rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 20rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
	position: relative;
}
.empty-icon.data-v-39446614 {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.6;
}
.empty-title.data-v-39446614 {
	font-size: 32rpx;
	color: #64748b;
	font-weight: 600;
	margin-bottom: 12rpx;
	letter-spacing: 0.5rpx;
}
.empty-description.data-v-39446614 {
	font-size: 26rpx;
	color: #94a3b8;
	font-weight: 400;
	line-height: 1.6;
	margin-bottom: 30rpx;
}

/* 加载动画 */
.loading-dots.data-v-39446614 {
	display: flex;
	justify-content: center;
	gap: 8rpx;
}
.dot.data-v-39446614 {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	-webkit-animation: loading-data-v-39446614 1.4s infinite ease-in-out both;
	        animation: loading-data-v-39446614 1.4s infinite ease-in-out both;
}
.dot.data-v-39446614:nth-child(1) { -webkit-animation-delay: -0.32s; animation-delay: -0.32s;
}
.dot.data-v-39446614:nth-child(2) { -webkit-animation-delay: -0.16s; animation-delay: -0.16s;
}
.dot.data-v-39446614:nth-child(3) { -webkit-animation-delay: 0s; animation-delay: 0s;
}
@-webkit-keyframes loading-data-v-39446614 {
0%, 80%, 100% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		opacity: 0.5;
}
40% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		opacity: 1;
}
}
@keyframes loading-data-v-39446614 {
0%, 80%, 100% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		opacity: 0.5;
}
40% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		opacity: 1;
}
}

/* 患病风险 */
.risk-section.data-v-39446614 {
	margin: 30rpx 20rpx;
}
.risk-grid.data-v-39446614 {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
	margin-top: 30rpx;
	justify-items: center;
}
.risk-item.data-v-39446614 {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}
.risk-progress-circle.data-v-39446614 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	position: relative;
	padding: 6rpx;
	background: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.15);
}
.progress-ring.data-v-39446614 {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: absolute;
	top: 0;
	left: 0;
	padding: 2rpx;
	box-sizing: border-box;
}
.progress-inner.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	text-align: center;
	width: 100rpx;
	height: 100rpx;
	background: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}
.progress-percentage.data-v-39446614 {
	font-size: 24rpx;
	font-weight: 600;
	color: #4facfe;
}
.risk-name.data-v-39446614 {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	font-weight: 500;
	line-height: 1.4;
	max-width: 120rpx;
}

/* 需要警惕 */
.warning-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.warning-list.data-v-39446614 {
	margin-top: 30rpx;
}
.warning-item.data-v-39446614 {
	margin-bottom: 30rpx;
	position: relative;
}
.warning-item.data-v-39446614:last-child {
	margin-bottom: 0;
}
.warning-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}
.warning-bullet.data-v-39446614 {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
}
.warning-title.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}
.warning-desc.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding-right: 100rpx;
}
.doctor-avatar.data-v-39446614 {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 结果解读 */
.result-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.constitution-chart.data-v-39446614 {
	margin: 40rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}
.constitution-circle.data-v-39446614 {
	width: 400rpx;
	height: 400rpx;
	border: 4rpx solid #e6f4ff;
	border-radius: 50%;
	position: relative;
	background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
	box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);
}
.constitution-inner.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}
.constitution-center.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	text-align: center;
	background: linear-gradient(135deg, #ffffff 0%, #f8fdff 100%);
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.15);
	border: 2rpx solid #e6f4ff;
}
.constitution-type.data-v-39446614 {
	font-size: 36rpx;
	font-weight: 600;
	color: #1890ff;
	margin-bottom: 5rpx;
}
.constitution-indicator.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.indicator-dot.data-v-39446614 {
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	-webkit-animation: pulse-data-v-39446614 2s ease-in-out infinite;
	        animation: pulse-data-v-39446614 2s ease-in-out infinite;
}
@keyframes pulse-data-v-39446614 {
0% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
}
50% {
		-webkit-transform: scale(1.2);
		        transform: scale(1.2);
		box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.6);
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
}
}
.direction-label.data-v-39446614 {
	position: absolute;
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
	background: rgba(255, 255, 255, 0.9);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid #e6f4ff;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
}
.direction-label.top.data-v-39446614 {
	top: 20rpx;
	left: 50%;
}
.direction-label.right.data-v-39446614 {
	right: 20rpx;
	top: 50%;
	-webkit-transform: translate(50%, -50%);
	        transform: translate(50%, -50%);
}
.direction-label.bottom.data-v-39446614 {
	bottom: 20rpx;
	left: 50%;
	-webkit-transform: translate(-50%, 50%);
	        transform: translate(-50%, 50%);
}
.direction-label.left.data-v-39446614 {
	left: 20rpx;
	top: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
}
.scale-marks.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}
.scale-mark.data-v-39446614 {
	position: absolute;
	width: 2rpx;
	height: 20rpx;
	background: linear-gradient(to bottom, #1890ff, rgba(24, 144, 255, 0.3));
	top: 10rpx;
	left: 50%;
	-webkit-transform-origin: 50% 190rpx;
	        transform-origin: 50% 190rpx;
	border-radius: 1rpx;
}
.constitution-status.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.status-indicator.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(255, 255, 255, 0.9);
	padding: 12rpx 20rpx;
	border-radius: 25rpx;
	border: 1rpx solid #e6f4ff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.status-dot.data-v-39446614 {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}
.status-text.data-v-39446614 {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
}
.result-description.data-v-39446614 {
	margin: 30rpx 0;
}
.result-text.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 15rpx;
}
.result-text.secondary.data-v-39446614 {
	font-size: 24rpx;
	color: #999;
}
.result-note.data-v-39446614 {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 15rpx;
	position: relative;
}
.note-content.data-v-39446614 {
	padding-right: 100rpx;
}
.note-text.data-v-39446614 {
	font-size: 24rpx;
	color: #999;
	line-height: 1.6;
}
.doctor-avatar-note.data-v-39446614 {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* VIP会员专享 */
.vip-section.data-v-39446614 {
	margin: 30rpx;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 15rpx;
	padding: 30rpx;
}
.vip-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}
.vip-icon.data-v-39446614 {
	font-size: 32rpx;
}
.vip-text.data-v-39446614 {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}
.vip-card.data-v-39446614 {
	text-align: center;
}
.vip-title.data-v-39446614 {
	font-size: 32rpx;
	color: #8b4513;
	margin-bottom: 10rpx;
	display: block;
}
.vip-subtitle.data-v-39446614 {
	font-size: 48rpx;
	color: #8b4513;
	font-weight: bold;
}

/* 报告解读 */
.report-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.report-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}
.report-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.member-badge.data-v-39446614 {
	background-color: #1890ff;
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
}
.symptoms-summary.data-v-39446614 {
	margin-bottom: 30rpx;
}
.summary-text.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
	margin-left: 10rpx;
}
.symptom-highlight.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
}
.main-symptom-detail.data-v-39446614 {
	margin-bottom: 30rpx;
}
.symptom-label.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}
.symptom-description.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}
.view-detail-btn.data-v-39446614 {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}
.doctor-section.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 20rpx;
}
.doctor-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.doctor-avatar.data-v-39446614 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 调理原则 */
.principle-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.principle-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}
.principle-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.principle-list.data-v-39446614 {
	margin-bottom: 30rpx;
}
.principle-item.data-v-39446614 {
	margin-bottom: 15rpx;
}
.principle-bullet.data-v-39446614 {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 10rpx;
}
.principle-text.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}
.view-principle-btn.data-v-39446614 {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}
.custom-plan-text.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 今日营养目标 */
.nutrition-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.nutrition-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}
.nutrition-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.chart-container.data-v-39446614 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.chart-left.data-v-39446614 {
	flex: 1;
}
.pie-chart.data-v-39446614 {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	position: relative;
	margin-bottom: 20rpx;
}
.pie-center.data-v-39446614 {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%, -50%);
	        transform: translate(-50%, -50%);
	text-align: center;
	background-color: white;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.calories-number.data-v-39446614 {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
	color: #333;
}
.calories-unit.data-v-39446614 {
	font-size: 20rpx;
	color: #666;
}
.chart-right.data-v-39446614 {
	flex: 1;
}
.nutrition-item.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}
.nutrition-color.data-v-39446614 {
	width: 20rpx;
	height: 20rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}
.nutrition-name.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
}
.nutrition-percent.data-v-39446614 {
	font-size: 24rpx;
	color: #333;
	width: 60rpx;
	text-align: right;
}
.nutrition-amount.data-v-39446614 {
	font-size: 24rpx;
	color: #666;
	width: 80rpx;
	text-align: right;
}

/* 今日专属膳方 */
.recipe-section.data-v-39446614 {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}
.recipe-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}
.recipe-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}
.adjust-plan.data-v-39446614 {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-left: auto;
}
.recipe-card.data-v-39446614 {
	background-color: #f0f0f0;
	border-radius: 15rpx;
	padding: 30rpx;
}
.recipe-content.data-v-39446614 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.recipe-info.data-v-39446614 {
	flex: 1;
}
.recipe-name.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}
.recipe-effect.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
}
.recipe-image-container.data-v-39446614 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
}
.recipe-image.data-v-39446614 {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.doctor-recommendation.data-v-39446614 {
	margin-top: 20rpx;
}

/* 底部按钮 - 浮动样式 */
.bottom-buttons.data-v-39446614 {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	gap: 20rpx;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配刘海屏 */
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 1) 100%);
	-webkit-backdrop-filter: blur(20rpx);
	        backdrop-filter: blur(20rpx);
	border-top: 1rpx solid rgba(0, 0, 0, 0.08);
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
	z-index: 999;
}
.btn-secondary.data-v-39446614 {
	flex: 1;
	padding: 28rpx 25rpx;
	text-align: center;
	background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 28rpx;
	font-size: 28rpx;
	font-weight: 500;
	color: #64748b;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}
.btn-secondary.data-v-39446614:active {
	-webkit-transform: translateY(2rpx);
	        transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.btn-primary.data-v-39446614 {
	flex: 1;
	padding: 28rpx 25rpx;
	text-align: center;
	background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
	color: white;
	border-radius: 28rpx;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}
.btn-primary.data-v-39446614::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transition: left 0.5s;
}
.btn-primary.data-v-39446614:active {
	-webkit-transform: translateY(2rpx);
	        transform: translateY(2rpx);
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);
}
.btn-primary.data-v-39446614:active::before {
	left: 100%;
}

/* 详细舌象特征列表 */
.tongue-features-detail.data-v-39446614 {
	margin-top: 30rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	-webkit-backdrop-filter: blur(10rpx);
	        backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.features-header.data-v-39446614 {
	text-align: center;
	margin-bottom: 30rpx;
}
.features-title.data-v-39446614 {
	font-size: 36rpx;
	font-weight: 700;
	background: linear-gradient(135deg, #667eea, #764ba2);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: 8rpx;
	letter-spacing: 1rpx;
}
.features-subtitle.data-v-39446614 {
	font-size: 24rpx;
	color: #8a92b2;
	text-align: center;
	margin-bottom: 10rpx;
	font-weight: 500;
}
.features-grid.data-v-39446614 {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	margin-top: 20rpx;
}
.feature-card.data-v-39446614 {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}
.feature-card.data-v-39446614::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
	transition: all 0.3s ease;
}
.feature-card.data-v-39446614:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}
.normal-card.data-v-39446614 {
	border-left: 4rpx solid transparent;
	background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(183, 235, 143, 0.05));
}
.normal-card.data-v-39446614::before {
	background: linear-gradient(90deg, #52c41a, #b7eb8f, #52c41a);
}
.abnormal-card.data-v-39446614 {
	border-left: 4rpx solid transparent;
	background: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 158, 158, 0.05));
}
.abnormal-card.data-v-39446614::before {
	background: linear-gradient(90deg, #ff4d4f, #ff9e9e, #ff4d4f);
}
.card-header.data-v-39446614 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}
.feature-badge.data-v-39446614 {
	font-size: 20rpx;
	font-weight: 600;
	padding: 6rpx 14rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	text-transform: uppercase;
}
.normal-badge.data-v-39446614 {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}
.abnormal-badge.data-v-39446614 {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}
.feature-category.data-v-39446614 {
	font-size: 22rpx;
	color: #8a92b2;
	background: rgba(255, 255, 255, 0.6);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	-webkit-backdrop-filter: blur(5rpx);
	        backdrop-filter: blur(5rpx);
}
.feature-name.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 12rpx;
	line-height: 1.4;
}
.feature-description.data-v-39446614 {
	font-size: 24rpx;
	color: #64748b;
	line-height: 1.6;
	margin-top: 8rpx;
	font-weight: 400;
}
.no-features.data-v-39446614 {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}
.empty-icon.data-v-39446614 {
	font-size: 48rpx;
	margin-bottom: 16rpx;
	opacity: 0.7;
}
.empty-text.data-v-39446614 {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}
.empty-hint.data-v-39446614 {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}

/* 动态显示检测到的舌象特征 */
.displayedTongueFeatures.data-v-39446614 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

/* 获取舌象特征的动态位置 */
.getMarkerPosition.data-v-39446614 {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

/* 保养建议样式 */
.care-suggestions-section.data-v-39446614 {
	/* margin: 30rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2); */
}
.care-category.data-v-39446614 {
	margin-bottom: 40rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 16rpx;
	padding: 30rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}
.care-category.data-v-39446614:last-child {
	margin-bottom: 0;
}
.category-header.data-v-39446614 {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid rgba(79, 172, 254, 0.1);
}
.category-icon.data-v-39446614 {
	font-size: 48rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.food-icon.data-v-39446614 {
	background: linear-gradient(135deg, #ff9a56, #ffad56);
}
.sport-icon.data-v-39446614 {
	background: linear-gradient(135deg, #4facfe, #00f2fe);
}
.life-icon.data-v-39446614 {
	background: linear-gradient(135deg, #a8edea, #fed6e3);
}
.music-icon.data-v-39446614 {
	background: linear-gradient(135deg, #667eea, #764ba2);
}
.treatment-icon.data-v-39446614 {
	background: linear-gradient(135deg, #f093fb, #f5576c);
}
.category-title.data-v-39446614 {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	letter-spacing: 1rpx;
}
.category-subtitle.data-v-39446614 {
	font-size: 24rpx;
	color: #8a92b2;
	font-weight: 500;
	margin-left: auto;
	background: rgba(255, 255, 255, 0.8);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}
.suggestion-list.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.suggestion-item.data-v-39446614 {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
	border-radius: 12rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}
.suggestion-item.data-v-39446614:hover {
	-webkit-transform: translateY(-2rpx);
	        transform: translateY(-2rpx);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.item-header.data-v-39446614 {
	margin-bottom: 16rpx;
}
.item-tag.data-v-39446614 {
	display: inline-block;
	font-size: 22rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}
.forbidden-tag.data-v-39446614 {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}
.recommend-tag.data-v-39446614 {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}
.life-tag.data-v-39446614 {
	background: linear-gradient(135deg, #722ed1, #9254de);
}
.music-tag.data-v-39446614 {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
}
.treatment-tag.data-v-39446614 {
	background: linear-gradient(135deg, #fa8c16, #ffa940);
}
.item-content.data-v-39446614 {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	font-weight: 400;
	letter-spacing: 0.5rpx;
}
.treatment-note.data-v-39446614 {
	margin-top: 16rpx;
	background: rgba(255, 193, 7, 0.1);
	border: 1rpx solid rgba(255, 193, 7, 0.3);
	border-radius: 8rpx;
	padding: 12rpx 16rpx;
}
.note-warning.data-v-39446614 {
	font-size: 24rpx;
	color: #fa8c16;
	font-weight: 500;
}
.care-reminder.data-v-39446614 {
	background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.03));
	border-radius: 16rpx;
	padding: 30rpx;
	border: 2rpx solid rgba(24, 144, 255, 0.1);
	position: relative;
	margin-top: 30rpx;
}
.reminder-header.data-v-39446614 {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}
.reminder-icon.data-v-39446614 {
	font-size: 40rpx;
	margin-right: 16rpx;
}
.reminder-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 600;
	color: #1890ff;
}
.reminder-content.data-v-39446614 {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	padding-right: 100rpx;
}
.doctor-avatar-reminder.data-v-39446614 {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 无保养建议数据提示 */
.no-care-suggestions.data-v-39446614 {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}
.no-care-text.data-v-39446614 {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}
.no-care-hint.data-v-39446614 {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}
.theory-item.data-v-39446614 {
	margin-bottom: 40rpx;
}
.theory-item.data-v-39446614:last-child {
	margin-bottom: 0;
}
.theory-header.data-v-39446614 {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
	margin-bottom: 15rpx;
}
.theory-name.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
}
.theory-name.water-wet.data-v-39446614 {
	color: #1890ff;
}
.theory-name.spleen-weak.data-v-39446614 {
	color: #52c41a;
}
.theory-summary.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
}
.theory-subtitle.data-v-39446614 {
	margin-bottom: 20rpx;
}
.theory-subtitle text.data-v-39446614 {
	font-size: 28rpx;
	color: #333;
}
.theory-content.data-v-39446614 {
	position: relative;
}
.theory-text.data-v-39446614 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	padding-right: 100rpx;
}

/* 体质分析卡片 */
.theory-card.data-v-39446614 {
	background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(24, 144, 255, 0.1);
	box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);
}
.card-header.data-v-39446614 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}
.header-left.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.theory-tag.data-v-39446614 {
	font-size: 24rpx;
	font-weight: 600;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	letter-spacing: 0.5rpx;
	text-transform: uppercase;
}
.theory-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 600;
	color: #1890ff;
}
.header-right.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.status-indicator.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.status-dot.data-v-39446614 {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}
.status-text.data-v-39446614 {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
}
.content-wrapper.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.medical-stamp.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.doctor-avatar-small.data-v-39446614 {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	flex-shrink: 0;
}
.stamp-text.data-v-39446614 {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

/* 空状态 */
.empty-theory-state.data-v-39446614 {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}
.empty-title.data-v-39446614 {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}
.empty-description.data-v-39446614 {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}
.loading-dots.data-v-39446614 {
	display: flex;
	justify-content: center;
	gap: 10rpx;
	margin-top: 20rpx;
}
.dot.data-v-39446614 {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #999;
	-webkit-animation: blink-data-v-39446614 1s infinite;
	        animation: blink-data-v-39446614 1s infinite;
}
@-webkit-keyframes blink-data-v-39446614 {
0%, 100% { opacity: 1;
}
50% { opacity: 0.5;
}
}
@keyframes blink-data-v-39446614 {
0%, 100% { opacity: 1;
}
50% { opacity: 0.5;
}
}

/* 舌象特征标记位置定义 - 优化后的紧凑布局 */
.marker-root.data-v-39446614 {
	top: 20% !important;
	left: 50% !important;
}
.marker-tongue.data-v-39446614 {
	top: 38% !important;
	left: 50% !important;
}
.marker-thickness.data-v-39446614 {
	top: 45% !important;
	left: 50% !important;
}
.marker-center.data-v-39446614 {
	top: 50% !important;
	left: 50% !important;
}
.marker-indent.data-v-39446614 {
	top: 45% !important;
	left: 18% !important;
}
.marker-sides.data-v-39446614 {
	top: 40% !important;
	left: 15% !important;
}
.marker-spots.data-v-39446614 {
	top: 50% !important;
	left: 85% !important;
}
.marker-cracks.data-v-39446614 {
	top: 42% !important;
	left: 82% !important;
}
.marker-punctures.data-v-39446614 {
	top: 38% !important;
	left: 28% !important;
}
.marker-mucus.data-v-39446614 {
	top: 38% !important;
	left: 50% !important;
}
.marker-thick.data-v-39446614 {
	top: 35% !important;
	left: 35% !important;
}
.marker-decay.data-v-39446614 {
	top: 35% !important;
	left: 65% !important;
}
.marker-moisture.data-v-39446614 {
	top: 38% !important;
	left: 65% !important;
}
.marker-peeling.data-v-39446614 {
	top: 32% !important;
	left: 50% !important;
}
.marker-color.data-v-39446614 {
	top: 65% !important;
	left: 50% !important;
}
.marker-tip.data-v-39446614 {
	top: 75% !important;
	left: 50% !important;
}

/* 小程序特殊定位 - 优化后的紧凑布局 */
.marker-root.data-v-39446614 {
	top: 95rpx !important;
	left: 300rpx !important;
}
.marker-tongue.data-v-39446614 {
	top: 180rpx !important;
	left: 300rpx !important;
}
.marker-thickness.data-v-39446614 {
	top: 216rpx !important;
	left: 300rpx !important;
}
.marker-center.data-v-39446614 {
	top: 240rpx !important;
	left: 300rpx !important;
}
.marker-indent.data-v-39446614 {
	top: 216rpx !important;
	left: 90rpx !important;
}
.marker-sides.data-v-39446614 {
	top: 192rpx !important;
	left: 75rpx !important;
}
.marker-spots.data-v-39446614 {
	top: 240rpx !important;
	left: 510rpx !important;
}
.marker-cracks.data-v-39446614 {
	top: 202rpx !important;
	left: 450rpx !important;
}
.marker-punctures.data-v-39446614 {
	top: 182rpx !important;
	left: 160rpx !important;
}
.marker-mucus.data-v-39446614 {
	top: 182rpx !important;
	left: 300rpx !important;
}
.marker-thick.data-v-39446614 {
	top: 168rpx !important;
	left: 210rpx !important;
}
.marker-decay.data-v-39446614 {
	top: 168rpx !important;
	left: 390rpx !important;
}
.marker-moisture.data-v-39446614 {
	top: 182rpx !important;
	left: 390rpx !important;
}
.marker-peeling.data-v-39446614 {
	top: 154rpx !important;
	left: 300rpx !important;
}
.marker-color.data-v-39446614 {
	top: 330rpx !important;
	left: 300rpx !important;
}
.marker-tip.data-v-39446614 {
	top: 380rpx !important;
	left: 300rpx !important;
}



/* 推荐商品 - 新增模块 */
.recommend-products-section.data-v-39446614 {
	margin: 30rpx 20rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.recommend-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.recommend-icon-wrapper.data-v-39446614 {
	display: flex;
	align-items: center;
	justify-content: center;
}
.recommend-icon.data-v-39446614 {
	font-size: 24rpx;
	color: white;
	font-weight: 600;
	width: 60rpx;
	height: 60rpx;
	border-radius: 12rpx;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.2);
}
.recommend-title-wrapper.data-v-39446614 {
	flex: 1;
}
.section-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	line-height: 1.4;
}
.recommend-subtitle.data-v-39446614 {
	font-size: 24rpx;
	color: #999999;
	font-weight: 400;
}
.products-list.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}
.product-item.data-v-39446614 {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #ffffff;
	border-radius: 12rpx;
	border: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
	position: relative;
	gap: 20rpx;
}
.product-item.data-v-39446614:active {
	background: #f8f9fa;
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
}
.product-image-wrapper.data-v-39446614 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	overflow: hidden;
	background: #f5f5f5;
	position: relative;
	flex-shrink: 0;
}
.product-image.data-v-39446614 {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.product-type-badge.data-v-39446614 {
	position: absolute;
	top: 4rpx;
	left: 4rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	font-size: 18rpx;
	font-weight: 500;
}
.course-badge.data-v-39446614 {
	background: rgba(255, 154, 86, 0.9);
	color: white;
}
.goods-badge.data-v-39446614 {
	background: rgba(79, 172, 254, 0.9);
	color: white;
}
.type-text.data-v-39446614 {
	font-size: 18rpx;
	font-weight: 500;
	color: white;
}
.product-info-wrapper.data-v-39446614 {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	min-width: 0;
}
.product-title.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	line-height: 1.4;
	margin-bottom: 8rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}
.product-price-wrapper.data-v-39446614 {
	display: flex;
	align-items: baseline;
	gap: 2rpx;
	margin-bottom: 8rpx;
}
.price-symbol.data-v-39446614 {
	font-size: 24rpx;
	color: #ff6b35;
	font-weight: 600;
}
.price-value.data-v-39446614 {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: 700;
}
.price-unit.data-v-39446614 {
	font-size: 20rpx;
	color: #ff6b35;
	font-weight: 500;
}
.product-reason.data-v-39446614 {
	margin-bottom: 8rpx;
}
.reason-label.data-v-39446614 {
	font-size: 22rpx;
	color: #666666;
	margin-right: 8rpx;
}
.reason-text.data-v-39446614 {
	font-size: 22rpx;
	color: #4facfe;
	font-weight: 500;
}
.product-sales.data-v-39446614 {
	margin-bottom: 0;
}
.sales-text.data-v-39446614 {
	font-size: 20rpx;
	color: #999999;
}
.product-cart-btn.data-v-39446614 {
	width: 56rpx;
	height: 56rpx;
	border-radius: 28rpx;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.3);
	transition: all 0.2s ease;
	flex-shrink: 0;
}
.product-cart-btn.data-v-39446614:active {
	-webkit-transform: scale(0.9);
	        transform: scale(0.9);
	box-shadow: 0 1rpx 4rpx rgba(79, 172, 254, 0.4);
}
.cart-icon.data-v-39446614 {
	font-size: 28rpx;
	color: white;
}

/* 推荐说明 */
.recommend-notice.data-v-39446614 {
	margin-top: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	border: 1rpx solid #e9ecef;
}
.notice-header.data-v-39446614 {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 12rpx;
}
.notice-icon.data-v-39446614 {
	font-size: 16rpx;
	color: white;
	font-weight: bold;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background: #4facfe;
	display: flex;
	align-items: center;
	justify-content: center;
}
.notice-title.data-v-39446614 {
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;
}
.notice-content.data-v-39446614 {
	font-size: 22rpx;
	color: #666666;
	line-height: 1.6;
}
.free-text.data-v-39446614 {
	font-size: 28rpx;
	color: #52c41a;
	font-weight: 600;
}

/* 视频推荐区域样式 */
.video-recommend-section.data-v-39446614 {
	margin: 30rpx 20rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.video-list.data-v-39446614 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.video-item.data-v-39446614 {
	display: flex;
	flex-direction: column;
	background: #f8f9fa;
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	transition: all 0.3s ease;
}
.video-item.data-v-39446614:active {
	-webkit-transform: scale(0.98);
	        transform: scale(0.98);
	background: #e9ecef;
}
.video-player-container.data-v-39446614 {
	width: 100%;
	height: 400rpx;
	position: relative;
	background: #000;
	border-radius: 12rpx 12rpx 0 0;
	overflow: hidden;
}
.video-player.data-v-39446614 {
	width: 100%;
	height: 100%;
	object-fit: contain;
}
.video-cover.data-v-39446614 {
	position: relative;
	width: 200rpx;
	height: 120rpx;
	flex-shrink: 0;
	display: none; /* 隐藏，因为现在使用video组件 */
}
.cover-image.data-v-39446614 {
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.play-overlay.data-v-39446614 {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
}
.play-button.data-v-39446614 {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}
.play-icon.data-v-39446614 {
	font-size: 24rpx;
	color: #333;
	margin-left: 4rpx;
}
.video-info.data-v-39446614 {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	background: #fff;
	border-radius: 0 0 12rpx 12rpx;
}
.video-title.data-v-39446614 {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
	margin-bottom: 10rpx;
}
.video-reason.data-v-39446614 {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	padding: 8rpx 12rpx;
	background: #e8f5e8;
	color: #4caf50;
	border-radius: 8rpx;
	align-self: flex-start;
}
.video-stats.data-v-39446614 {
	display: flex;
	gap: 20rpx;
}
.stat-item.data-v-39446614 {
	font-size: 22rpx;
	color: #999;
}

/* 视频播放器遮罩层样式 */
.video-player-overlay.data-v-39446614 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}
.video-player-container.data-v-39446614 {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	width: 100%;
	max-width: 640rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}
.video-player-header.data-v-39446614 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1px solid #eee;
}
.video-player-title.data-v-39446614 {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
}
.close-btn.data-v-39446614 {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	border-radius: 50%;
	cursor: pointer;
}
.close-text.data-v-39446614 {
	font-size: 32rpx;
	color: #666;
	font-weight: bold;
}
.video-player.data-v-39446614 {
	width: 100%;
	height: 400rpx;
	background: #000;
}
.video-player-info.data-v-39446614 {
	padding: 20rpx 30rpx;
	background: #fff;
}
.video-description.data-v-39446614 {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
	display: block;
}
.video-stats-row.data-v-39446614 {
	display: flex;
	gap: 20rpx;
}
.video-stats-row .stat-item.data-v-39446614 {
	font-size: 24rpx;
	color: #999;
}

