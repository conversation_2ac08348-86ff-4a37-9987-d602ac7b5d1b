<template>
	<view class="tongue-complete">
		<!-- 2025-01-03 22:55:53,565-INF0-[complete][init_001] 舌诊完整结果页面初始化 -->
		
		<!-- 顶部导航 -->
		<!-- <view class="top-nav">
			<view class="nav-left" @click="goBack">
				<text class="back-icon">‹</text>
			</view>
			<view class="nav-title">健康报告</view>
			<view class="nav-right">
				<text class="menu-icon">•••</text>
				<text class="record-icon">●</text>
			</view>
		</view> -->
		
		<!-- 医院标识 -->
		<view class="hospital-info">
			<text> 国家重点实验室项目 | 国家重大专项</text>
		</view>
		
		<!-- 报告类型选择 -->
		<view class="report-tabs">
			<view class="tab-item active">
				<text class="tab-text">舌象报告</text>
				<view class="tab-line"></view>
			</view>
			<view class="tab-item">
				<text class="tab-text">面诊报告</text>
			</view>
		</view>
		
		<!-- 健康得分卡片 -->
		<view class="health-card" v-if="!!displaySettings.show_score">
			<view class="health-content">
				<view class="health-left">
					<image class="health-avatar" :src="analysisData && analysisData.tongue_image ? analysisData.tongue_image : '/static/images/moisture-avatar.png'"></image>
					<view class="health-symptoms">
						<view class="main-symptom">
							<text class="symptom-label">体质类型：</text>
							<text class="symptom-value">{{constitutionType}}</text>
						</view>
						<view class="sub-symptom" v-if="analysisData && analysisData.syndrome_name">
							<text class="symptom-label">证候：</text>
							<text class="symptom-value">{{analysisData.syndrome_name}}</text>
						</view>
					</view>
				</view>
				<view class="health-right">
					<view class="score-circle">
						<view class="score-inner">
							<!-- 2025-01-27 根据show_score_value设置控制分值数字显示 -->
							<text class="score-number" v-if="!!displaySettings.show_score_value">{{healthScore}}</text>
							<text class="score-number no-score" v-else>--</text>
							<text class="score-text">健康得分</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 智能问诊 -->
		<!-- <view class="ai-diagnosis">
			<view class="ai-left">
				<view class="ai-title">智能问诊</view>
				<text class="ai-subtitle" v-if="healthSuggestions">体质分析结果</text>
				<text class="ai-subtitle" v-else>分析中...</text>
				<text class="ai-description" v-if="healthSuggestions">{{healthSuggestions}}</text>
				<text class="ai-description" v-else>正在为您生成详细的体质分析报告...</text>
			</view>
			<view class="ai-right" v-if="healthSuggestions">
				<view class="analysis-complete-btn">已完成</view>
			</view>
			<view class="ai-right" v-else>
				<view class="loading-btn">分析中</view>
			</view>
		</view>
		 -->
		<!-- 可能体征 -->
		<view class="symptoms-section" v-if="!!displaySettings.show_symptoms">
			<text class="section-title">您可能有以下体征</text>
			<view class="symptoms-grid">
				<view class="symptom-tag" v-for="(symptom, index) in typicalSymptoms" :key="index">
					{{symptom}}
				</view>
				<view v-if="typicalSymptoms.length === 0" class="no-symptoms">
					<text>暂无典型症状数据</text>
				</view>
			</view>
		</view>
		
		<!-- 舌象分析 -->
		<view class="tongue-analysis" v-if="!!displaySettings.show_tongue_analysis">
			<text class="section-title">舌象分析</text>
			<view class="tongue-diagram">
				<!-- CSS绘制的舌头图 -->
				<view class="tongue-svg">
					<view class="tongue-shape"></view>
				<view class="tongue-markers">
						<!-- 动态显示检测到的舌象特征（优先显示异常特征） -->
						<view 
							v-for="(feature, index) in displayedTongueFeatures" 
							:key="index"
							class="marker" 
						:class="[
							feature.feature_situation === '异常' ? 'abnormal' : 'normal',
							'marker-' + getFeaturePositionClass(feature.feature_group)
						]"
							:style="feature.markerStyle"
						>
							<text class="marker-icon">{{ feature.feature_situation === '异常' ? '!' : '✓' }}</text>
							<text class="marker-text">{{ feature.feature_name }}</text>
					</view>
					</view>
					</view>
				<view class="tongue-photo-btn" @click="viewTongueImage">
					<text class="photo-btn-text">查看我的舌象</text>
					</view>
					</view>
			
			<!-- 详细舌象特征列表 -->
			<view class="tongue-features-detail">
				<view class="features-header">
					<text class="features-title">舌象特征分析</text>
					<text class="features-subtitle">AI智能识别 · 专业解读</text>
				</view>
				<view class="features-grid">
					<view 
						v-for="(feature, index) in tongueFeatures" 
						:key="index"
						class="feature-card"
						:class="feature.feature_situation === '异常' ? 'abnormal-card' : 'normal-card'"
					>
						<view class="card-header">
							<view class="feature-badge" :class="feature.feature_situation === '异常' ? 'abnormal-badge' : 'normal-badge'">
								{{ feature.feature_situation }}
							</view>
							<text class="feature-category">{{ feature.feature_group }}</text>
						</view>
						<text class="feature-name">{{ feature.feature_name }}</text>
						<text class="feature-description">{{ feature.feature_interpret }}</text>
					</view>
				</view>
				<view v-if="tongueFeatures.length === 0" class="no-features">
					<view class="empty-icon">🔍</view>
					<text class="empty-text">暂无舌象特征数据</text>
					<text class="empty-hint">请重新拍摄或检查图片质量</text>
				</view>
			</view>
		</view>
		
		<!-- 体征异常 -->
		<view class="abnormal-section" v-if="!!displaySettings.show_symptoms">
			<view class="abnormal-title">
				<text class="abnormal-count">您有{{abnormalFeatures.length}}项表征异常</text>
			</view>
			<view class="abnormal-list">
				<view class="abnormal-item" v-for="(feature, index) in abnormalFeatures" :key="index">
					<view class="abnormal-header">
						<text class="abnormal-icon">!</text>
						<text class="abnormal-name">{{feature.name || feature.feature_name}}：</text>
					</view>
					<text class="abnormal-desc">{{feature.interpret || feature.feature_interpret}}</text>
				</view>
				<view v-if="abnormalFeatures.length === 0" class="no-abnormal">
					<text>恭喜您！暂无异常特征发现</text>
				</view>
			</view>
		</view>
		
		<!-- 舌象体征论述 - 简约医疗风格 -->
		<view class="theory-section" v-if="!!displaySettings.show_tongue_analysis">
			<view class="theory-header-wrapper">
				<view class="theory-icon">📋</view>
				<text class="section-title">舌象体征论述</text>
				<text class="theory-subtitle">专业医学解读</text>
			</view>
			
			<!-- 体质分析卡片 -->
			<view class="theory-card constitution-card" v-if="healthSuggestions">
				<view class="card-header">
					<view class="header-left">
						<view class="theory-tag constitution-tag">体质分析</view>
						<text class="theory-title">{{constitutionType}}</text>
					</view>
					<view class="header-right">
						<view class="status-indicator active">
							<text class="status-dot">●</text>
							<text class="status-text">已完成</text>
						</view>
					</view>
				</view>
				<view class="card-content">
					<view class="content-wrapper">
						<text class="theory-description">{{healthSuggestions}}</text>
						<view class="medical-stamp">
							<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
							<text class="stamp-text">专业解读</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 证候介绍卡片 -->
			<view class="theory-card syndrome-card" v-if="analysisData && analysisData.syndrome_introduction">
				<view class="card-header">
					<view class="header-left">
						<view class="theory-tag syndrome-tag">证候介绍</view>
						<text class="theory-title">{{analysisData.syndrome_name}}</text>
					</view>
					<view class="header-right">
						<view class="status-indicator active">
							<text class="status-dot">●</text>
							<text class="status-text">已完成</text>
						</view>
					</view>
				</view>
				<view class="card-content">
					<view class="content-wrapper">
						<text class="theory-description">{{analysisData.syndrome_introduction}}</text>
						<view class="medical-stamp">
							<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
							<text class="stamp-text">医学权威</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!healthSuggestions && (!analysisData || !analysisData.syndrome_introduction)" class="empty-theory-state">
				<view class="empty-icon">🔍</view>
				<text class="empty-title">暂无体征论述数据</text>
				<text class="empty-description">正在为您生成专业的医学解读报告</text>
				<view class="loading-dots">
					<view class="dot"></view>
					<view class="dot"></view>
					<view class="dot"></view>
				</view>
			</view>
		</view>
		
		<!-- 患病风险 -->
		<!-- <view class="risk-section">
			<text class="section-title">患病风险</text>
			<view class="risk-grid">
				<view class="risk-item" v-for="(item, index) in riskData" :key="index">
					
					<view class="risk-progress-circle">
						<view class="progress-ring" :style="riskProgressStyles[index]">
							<view class="progress-inner">
								<text class="progress-percentage">{{item.percentage}}%</text>
							</view>
						</view>
					</view>
					<text class="risk-name">{{item.name}}</text>
				</view>
			</view>
		</view>
		 -->
		<!-- 需要警惕 -->
		<!-- <view class="warning-section">
			<text class="section-title">需要警惕</text>
			<view class="warning-list">
				<view class="warning-item" v-for="(warning, index) in warningList" :key="index">
					<view class="warning-header">
						<text class="warning-bullet">•</text>
						<text class="warning-title">{{warning.name || warning.title}}</text>
					</view>
					<text class="warning-desc">{{warning.description || warning.advice}}</text>
					<image v-if="warning.showDoctor" class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
				</view>
				<view v-if="analysisData && analysisData.risk_assessment" class="risk-assessment-detail">
					<text class="risk-detail-text">{{analysisData.risk_assessment}}</text>
				</view>
				<view v-if="warningList.length === 0 && (!analysisData || !analysisData.risk_assessment)" class="no-warning">
					<text>暂无特别需要警惕的风险</text>
				</view>
			</view>
		</view> -->
		
		<!-- 保养建议 -->
		<view class="tongue-analysis" v-if="!!displaySettings.show_care_advice && careSuggestions && Object.keys(careSuggestions).length > 0">
			<!-- <text class="section-title">专业保养建议</text> -->
			
			<!-- 饮食建议 -->
			<view class="care-category" v-if="careSuggestions.food && careSuggestions.food.length > 0">
				<view class="category-header">
					<view class="category-icon food-icon">🍽️</view>
					<text class="category-title">饮食调养</text>
					<view class="category-subtitle">科学膳食，合理营养</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.food" :key="index">
						<view class="item-header">
							<view class="item-tag" :class="item.title === '禁忌饮食' ? 'forbidden-tag' : 'recommend-tag'">
								{{item.title}}
							</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>
			
			<!-- 运动建议 -->
			<view class="care-category" v-if="careSuggestions.sport && careSuggestions.sport.length > 0">
				<view class="category-header">
					<view class="category-icon sport-icon">🏃</view>
					<text class="category-title">运动保健</text>
					<view class="category-subtitle">适度运动，强身健体</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.sport" :key="index">
						<view class="item-header">
							<view class="item-tag recommend-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>
			
			<!-- 生活建议 -->
			<view class="care-category" v-if="careSuggestions.sleep && careSuggestions.sleep.length > 0">
				<view class="category-header">
					<view class="category-icon life-icon">🌱</view>
					<text class="category-title">生活调理</text>
					<view class="category-subtitle">规律作息，身心平衡</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.sleep" :key="index">
						<view class="item-header">
							<view class="item-tag life-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>
			
			<!-- 音乐建议 -->
			<view class="care-category" v-if="careSuggestions.music && careSuggestions.music.length > 0">
				<view class="category-header">
					<view class="category-icon music-icon">🎵</view>
					<text class="category-title">音乐疗法</text>
					<view class="category-subtitle">调和阴阳，怡情养性</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.music" :key="index">
						<view class="item-header">
							<view class="item-tag music-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
					</view>
				</view>
			</view>
			
			<!-- 治疗建议 -->
			<view class="care-category" v-if="careSuggestions.treatment && careSuggestions.treatment.length > 0">
				<view class="category-header">
					<view class="category-icon treatment-icon">⚕️</view>
					<text class="category-title">保健疗法</text>
					<view class="category-subtitle">中医保健，养生调理</view>
				</view>
				<view class="suggestion-list">
					<view class="suggestion-item" v-for="(item, index) in careSuggestions.treatment" :key="index">
						<view class="item-header">
							<view class="item-tag treatment-tag">{{item.title}}</view>
						</view>
						<text class="item-content">{{item.advice}}</text>
						<view class="treatment-note" v-if="item.title === '艾灸保健'">
							<text class="note-warning">⚠️ 注意事项：请在专业指导下进行，注意防烫伤</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 专家提醒 -->
			<view class="care-reminder">
				<view class="reminder-header">
					<view class="reminder-icon">👨‍⚕️</view>
					<text class="reminder-title">专家提醒</text>
				</view>
				<text class="reminder-content">以上建议仅供参考，具体调理方案请咨询专业医师。坚持执行，循序渐进，必有成效。</text>
				<image class="doctor-avatar-reminder" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view>
		
		<!-- 推荐商品 - 新增模块 -->
		<view class="recommend-products-section" v-if="!!displaySettings.show_product_recommend && recommendProducts && recommendProducts.length > 0">
			<view class="recommend-header">
				<view class="recommend-icon-wrapper">
					<text class="recommend-icon">商品</text>
				</view>
				<view class="recommend-title-wrapper">
					<text class="section-title">{{recommendTitle || '根据您的舌诊结果，为您推荐以下产品'}}</text>
					<text class="recommend-subtitle">专业推荐，精准调理</text>
				</view>
			</view>
			
			<view class="products-list">
				<view 
					class="product-item" 
					v-for="(product, index) in recommendProducts" 
					:key="index"
					@click="navigateToProduct(product)"
				>
					<view class="product-image-wrapper">
						<image 
							class="product-image" 
							:src="product.pic || '/static/images/default-product.png'"
							mode="aspectFill"
						></image>
						<view class="product-type-badge" :class="product.type === 'course' ? 'course-badge' : 'goods-badge'">
							<text class="type-text">{{product.type === 'course' ? '课程' : '商品'}}</text>
						</view>
					</view>
					
					<view class="product-info-wrapper">
						<view class="product-title">{{product.name}}</view>
						
						<view class="product-price-wrapper" v-if="product.price || product.sell_price || product.market_price">
							<text class="price-symbol">¥</text>
							<text class="price-value">{{product.price || product.sell_price || product.market_price || '0.00'}}</text>
							<text class="price-unit" v-if="product.type === 'course'">/课程</text>
						</view>
						
						<!-- 免费课程显示 -->
						<view class="product-price-wrapper" v-else-if="product.type === 'course'">
							<text class="free-text">免费</text>
						</view>
						
						<view class="product-reason" v-if="product.recommend_reason">
							<text class="reason-label">推荐：</text>
							<text class="reason-text">{{product.recommend_reason}}</text>
						</view>
						
						<view class="product-sales" v-if="product.sales">
							<text class="sales-text">已售{{product.sales}}件</text>
						</view>
					</view>
					
					<!-- 购物车按钮独立放在右侧 -->
					<view class="product-cart-btn" @click.stop="addToCart(product)">
						<text class="iconfont icon_gouwuche cart-icon"></text>
					</view>
				</view>
			</view>
			
			<!-- 推荐说明 -->
			<view class="recommend-notice">
				<view class="notice-header">
					<view class="notice-icon">i</view>
					<text class="notice-title">推荐说明</text>
				</view>
				<text class="notice-content">以上推荐商品基于您的体质分析结果和健康得分，由专业团队精心挑选。建议根据实际情况选择使用，如有疑问请咨询专业医师。</text>
			</view>
		</view>

		<!-- 视频推荐区域 -->
		<view class="video-recommend-section" v-if="recommendVideos.length > 0 || true">
			<view class="section-header">
				<view class="recommend-icon-wrapper">
					<text class="recommend-icon">🎬</text>
				</view>
				<view class="recommend-title-wrapper">
					<text class="section-title">体质调理视频推荐</text>
					<text class="recommend-subtitle">根据您的体质特点，为您推荐专业调理视频</text>
				</view>
			</view>

			<!-- 调试信息 -->
			<view v-if="recommendVideos.length === 0" style="padding: 20rpx; background: #f0f0f0; margin: 20rpx 0; border-radius: 8rpx;">
				<text style="color: #666; font-size: 24rpx;">调试信息：当前推荐视频数量 = {{ recommendVideos.length }}</text>
			</view>

			<view class="video-list" v-if="recommendVideos.length > 0">
				<view
					class="video-item"
					v-for="(video, index) in recommendVideos"
					:key="video.id"
				>
					<!-- 直接嵌入视频播放器 -->
					<view class="video-player-container">
						<video
							:src="video.url"
							:poster="video.pic"
							controls
							autoplay="false"
							:show-center-play-btn="true"
							:show-play-btn="true"
							:show-fullscreen-btn="true"
							:show-progress="true"
							:show-loading="true"
							:enable-progress-gesture="true"
							:object-fit="'contain'"
							:playsinline="true"
							:webkit-playsinline="true"
							class="video-player"
							@error="onVideoError"
							@play="onVideoPlay(video)"
							@pause="onVideoPause(video)"
						></video>
					</view>
					<view class="video-info">
						<text class="video-title">{{ video.name }}</text>
						<text class="video-reason" v-if="video.recommend_reason">{{ video.recommend_reason }}</text>
						<view class="video-stats">
							<text class="stat-item">👁 {{ video.view_num || 0 }}次观看</text>
							<text class="stat-item">👍 {{ video.zan_num || 0 }}点赞</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 视频推荐说明 -->
			<view class="recommend-notice">
				<view class="notice-header">
					<view class="notice-icon">i</view>
					<text class="notice-title">视频推荐说明</text>
				</view>
				<text class="notice-content">以上推荐视频基于您的体质分析结果，由专业团队精心挑选。建议根据实际情况观看学习，如有疑问请咨询专业医师。</text>
			</view>
		</view>

		<!-- 视频播放器遮罩层已移除，现在视频直接在列表中播放 -->


		<!-- VIP会员专享 -->
		<!-- <view class="vip-section">
			<view class="vip-header">
				<text class="vip-icon">👑</text>
				<text class="vip-text">VIP会员专享</text>
			</view>
			<view class="vip-card">
				<text class="vip-title">广东省中医院为您专属定制</text>
				<text class="vip-subtitle">健康调理方案</text>
			</view>
		</view> -->
		
		<!-- 报告解读 -->
		<!-- <view class="report-section">
			<view class="report-header">
				<text class="report-title">报告解读</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="symptoms-summary">
				<text class="summary-text">您的主症为</text>
				<text class="symptom-highlight water">水湿</text>
				<text class="summary-text">，副症为</text>
				<text class="symptom-highlight spleen">脾胃虚</text>
			</view>
			
			<view class="main-symptom-detail">
				<view class="symptom-label">主症【水湿】：</view>
				<text class="symptom-description">您体内水液代谢出现了异常，停滞在体内，也可以理解为人体新陈代谢速率变慢，导致体内的水分不能被正常的排出。</text>
			</view>
			
			<view class="view-detail-btn" @click="viewDetailContent">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看体征解读内容</text>
			</view>
			
			<view class="doctor-section">
				<text class="doctor-title">名医为您解读体征</text>
				<image class="doctor-avatar" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view> -->
		
		<!-- 调理原则 -->
		<!-- <view class="principle-section">
			<view class="principle-header">
				<text class="principle-title">调理原则</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="principle-list">
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">日常饮食不要吃得过于油腻，油腻的食物会</text>
				</view>
				<view class="principle-item">
					<text class="principle-bullet">•</text>
					<text class="principle-text">每天积累运动要达到半小时以上，可以有效改善水湿体质，增强自己的体质。</text>
				</view>
			</view>
			
			<view class="view-principle-btn" @click="viewPrincipleContent">
				<text class="lock-icon">🔒</text>
				<text class="btn-text">查看病症调理原则</text>
			</view>
			
			<view class="custom-plan-text">
				<text>名医为您定制专属调理方案</text>
			</view>
		</view> -->
		
		<!-- 今日营养目标 -->
		<!-- <view class="nutrition-section">
			<view class="nutrition-header">
				<text class="nutrition-title">今日营养目标</text>
				<view class="member-badge">会员专享</view>
			</view>
			
			<view class="chart-container">
				<view class="chart-left">
					<view class="pie-chart" :style="pieChartStyle">
						<view class="pie-center">
							<text class="calories-number">{{nutritionData.totalCalories}}</text>
							<text class="calories-unit">千卡</text>
						</view>
					</view>
				</view>
				
				<view class="chart-right">
					<view class="nutrition-item" v-for="(item, index) in nutritionData.nutritionItems" :key="index">
						<view class="nutrition-color" :style="{'background-color': item.color}"></view>
						<text class="nutrition-name">{{item.name}}</text>
						<text class="nutrition-percent">{{item.percent}}%</text>
						<text class="nutrition-amount">{{item.amount}}克</text>
					</view>
				</view>
			</view>
		</view> -->
		
		<!-- 今日专属膳方 -->
		<!-- <view class="recipe-section">
			<view class="recipe-header">
				<text class="recipe-title">今日专属膳方</text>
				<text class="adjust-plan" @click="adjustRecipePlan">调整膳方计划 ></text>
			</view>
			
			<view class="recipe-card">
				<view class="recipe-content">
					<view class="recipe-info">
						<text class="recipe-name">黄酒煮鸡</text>
						<text class="recipe-effect">温中养血，散寒通络</text>
					</view>
					<view class="recipe-image-container">
						<image class="recipe-image" src="/static/images/huangjiu-chicken.png"></image>
					</view>
				</view>
			</view>
			
			<view class="doctor-recommendation">
				<image class="doctor-avatar-small" src="/static/images/doctor-avatar.png"></image>
			</view>
		</view> -->
		
		<!-- 底部按钮 - 浮动样式 -->
		<view class="bottom-buttons">
			<view class="btn-secondary" @click="goBack">返回</view>
			<view class="btn-secondary" @click="retakePhoto">重新拍照</view>
			<view class="btn-primary" @click="shareReport">分享报告</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 2025-01-03 22:55:53,565-INF0-[complete][data_001] 初始化页面数据
				recordId: 0,
				analysisData: null,
				isLoading: true,
				loadingText: '分析中...',
				
				// 健康得分和体质信息
				healthScore: 89,
				constitutionType: '湿热体质、血瘀体质',
				mainSymptom: '水湿',
				subSymptom: '脾胃虚',
				
				// 2025-01-27 新增前端显示设置 - 控制各模块显示状态
				displaySettings: {
					show_score: 1, // 是否显示评分，默认显示
					show_score_value: 1, // 2025-01-27 是否显示评分分值，默认显示
					show_symptoms: 1, // 是否显示体征，默认显示
					show_tongue_analysis: 1, // 是否显示舌象分析，默认显示
					show_care_advice: 1, // 是否显示调理建议，默认显示
					show_product_recommend: 1 // 是否显示商品推荐，默认显示
				},
				
				// 舌象特征数据
				tongueFeatures: [],
				abnormalFeatures: [],
				
				// 体征症状
				typicalSymptoms: [],
				
				// 风险评估数据
				riskData: [],
				
				// 警告列表
				warningList: [],
				
				// 饮食建议
				dietSuggestions: [],
				
				// 运动建议
				exerciseSuggestions: [],
				
				// 健康建议
				healthSuggestions: '',
				
				// 保养建议数据
				careSuggestions: {},
				
				// 推荐商品数据 - 新增
				recommendProducts: [],
				recommendTitle: '',

				// 推荐视频数据 - 新增
				recommendVideos: [],
				showVideoPlayer: false, // 是否显示视频播放器
				currentVideo: {}, // 当前播放的视频
				
				// 营养目标数据
				nutritionData: {
					carbohydrate: { percent: 62, amount: 385 },
					protein: { percent: 10, amount: 62 },
					fat: { percent: 28, amount: 77 },
					totalCalories: 2484,
					nutritionItems: [
						{ name: '碳水化物', percent: 62, amount: 385, color: '#8884d8' },
						{ name: '蛋白质', percent: 10, amount: 62, color: '#82ca9d' },
						{ name: '脂肪', percent: 28, amount: 77, color: '#ffc658' }
					]
				},
				
				// 今日膳方数据
				recipeData: {
					name: '黄酒煮鸡',
					effect: '温中养血，散寒通络',
					image: '/static/images/huangjiu-chicken.png'
				},
				
				// 体质分析数据
				constitutionData: {
					type: '平和',
					position: { x: -20, y: 15 },
					statusColor: '#52c41a',
					statusText: '体质改善',
					description: '通过健康天平分析得出，您的身体偏寒且正气偏虚，处于痰湿状态。',
					explanation: '健康天平圆点越靠近中心身体越健康，越偏离中心健康状态越严重。',
					note: '注：「健康天平」由省中医院副院长杨志敏教授带领的健康辨识团队运用中医健康辨识体系，结合经络脏腑阴阳五行、气血津液等理论得出。'
				},
				
				// 圆形刻度标记数据
				scaleMarks: (() => {
					const marks = [];
					for (let i = 0; i < 24; i++) {
						const angle = i * 15;
						const isMainMark = i % 6 === 0;
						marks.push({
							angle: angle,
							opacity: isMainMark ? 0.3 : 0.1
						});
					}
					return marks;
				})(),
				
				// 分析接口相关
				analysisApiCompleted: false,
				orderNo: '',
				apiType: '',
				
				// 2025-01-27 新增支付订单ID
				orderId: '',
			}
		},
		computed: {
			// 2025-01-03 22:55:53,565-INF0-[complete][computed_001] 动态计算饼状图样式
			pieChartStyle() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][computed_002] 生成饼状图样式');
				return {
					background: this.calculatePieChartGradient(this.nutritionData.nutritionItems)
				};
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][computed_003] 动态计算风险进度条样式
			riskProgressStyles() {
				return this.riskData.map(item => ({
					background: `conic-gradient(#ff6b6b 0% ${item.percentage}%, #f0f0f0 ${item.percentage}% 100%)`
				}));
			},
			
			// 体质数据
			constitutionData() {
					return {
					type: this.constitutionType || '平和体质',
					position: { x: 30, y: -20 }, // 根据体质类型调整位置
					statusColor: '#ff9a9e',
					statusText: '偏热',
					description: this.healthSuggestions || '您的体质整体较为平衡，建议保持良好的生活习惯。',
					explanation: '体质分析基于舌象特征、症状表现等多维度评估得出。',
					note: '以上分析仅供参考，具体调理方案请咨询专业医师。'
				};
			},
			
			// 刻度标记
			scaleMarks() {
				const marks = [];
				for (let i = 0; i < 12; i++) {
					marks.push({
						angle: i * 30,
						opacity: i % 3 === 0 ? 1 : 0.5
					});
				}
				return marks;
			},
			
			// 显示的舌象特征（优先显示异常的，最多6个）
			displayedTongueFeatures() {
				if (!this.tongueFeatures || this.tongueFeatures.length === 0) {
					return [];
				}
				
				// 优先选择异常特征
				const abnormalFeatures = this.tongueFeatures.filter(f => f.feature_situation === '异常');
				const normalFeatures = this.tongueFeatures.filter(f => f.feature_situation === '正常');
				
				// 最多显示6个，优先异常特征
				let displayed = [...abnormalFeatures];
				if (displayed.length < 6) {
					displayed = displayed.concat(normalFeatures.slice(0, 6 - displayed.length));
				} else {
					displayed = displayed.slice(0, 6);
				}
				
				// 预先计算每个特征的样式，避免在模板中调用函数
				return displayed.map((feature, index) => {
					// 计算偏移样式
					const offset = this.calculateOffsetForSamePosition(feature.feature_group, index, displayed.length);
					
					// 返回带有预计算样式的特征对象
					return {
						...feature,
						markerStyle: {
							zIndex: (10 + index).toString(), // 确保层级正确
							...offset
						}
					};
				});
			}
		},
		methods: {
			// 2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 返回上一页功能
			goBack() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][goBack_001] 用户点击返回');
				uni.navigateBack();
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 查看体征解读详情功能
			viewDetailContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][viewDetail_001] 用户点击查看体征解读');
				// 需要VIP权限才能查看详细内容
				uni.showToast({
					title: '需要VIP权限',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 查看调理原则详情功能
			viewPrincipleContent() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][viewPrinciple_001] 用户点击查看调理原则');
				// 需要VIP权限才能查看详细内容
				uni.showToast({
					title: '需要VIP权限',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_001] 调整膳方计划
			adjustRecipePlan() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_002] 用户点击调整膳方计划');
				console.log('2025-01-03 22:55:53,565-INF0-[complete][adjustRecipePlan_003] 当前膳方:', this.recipeData);
				uni.navigateTo({
					url: '/pages/recipe/adjust'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_001] 显示详细健康报告
			onShowHealthReport() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_002] 导航到健康详细报告');
				uni.navigateTo({
					url: '/pages/tongue/detail'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_003] 分享功能
			onShare() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_004] 用户点击分享');
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_005] 查看营养详情
			onViewNutritionDetail() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_006] 导航到营养详情页面');
				uni.navigateTo({
					url: '/pages/nutrition/detail'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_007] 查看完整调理方案
			onViewCompleteRegulation() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_008] 导航到调理方案页面');
				uni.navigateTo({
					url: '/pages/tongue/plan'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_009] 开始舌诊拍摄
			onStartTongueDetection() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_010] 启动舌诊拍摄功能');
				uni.navigateTo({
					url: '/pages/tongue/camera'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_011] 计算饼状图角度
			calculatePieChartGradient(nutritionItems) {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 计算饼状图渐变');
				let cumulativePercent = 0;
				const segments = nutritionItems.map(item => {
					const startPercent = cumulativePercent;
					const endPercent = cumulativePercent + item.percent;
					cumulativePercent = endPercent;
					return `${item.color} ${startPercent}% ${endPercent}%`;
				});
				return `conic-gradient(${segments.join(', ')})`;
			},
			
			/**
			 * 获取舌诊分析记录
			 * 调用后端接口获取完整的舌诊分析数据
			 */
			getAnalysisRecord() {
				console.log('2025-01-06 11:45:00,002-INFO-[complete][getAnalysisRecord_001] 开始获取舌诊分析记录:', this.recordId);
				
				if (!this.recordId) {
					console.error('2025-01-06 11:45:00,003-ERROR-[complete][getAnalysisRecord_002] 记录ID为空');
					uni.showToast({
						title: '参数错误，请重新扫描',
						icon: 'none'
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 2000);
					return;
				}
				
				uni.showLoading({
					title: '加载中...'
				});
				
				// 使用正确的getApp().post方法调用后端接口获取分析记录
				const app = getApp();
				app.post('ApiSheZhen/getRecord', {
					id: this.recordId
				}, (response) => {
					console.log('2025-01-06 11:45:00,004-INFO-[complete][getAnalysisRecord_003] API响应:', response);
					
					uni.hideLoading();
					
					if (response && (response.status === 1 || response.code === 1)) {
						console.log('2025-01-06 11:45:00,005-INFO-[complete][getAnalysisRecord_004] 获取分析记录成功');
						// 解析并设置分析数据
						this.parseAnalysisData(response.data);
					} else {
						console.error('2025-01-06 11:45:00,006-ERROR-[complete][getAnalysisRecord_005] 获取分析记录失败:', response?.msg || response?.message);
						uni.showToast({
							title: response?.msg || response?.message || '获取分析记录失败',
							icon: 'none'
						});
						setTimeout(() => {
							uni.navigateBack();
						}, 2000);
					}
				}, (err) => {
					console.error('2025-01-06 11:45:00,007-ERROR-[complete][getAnalysisRecord_006] 获取分析记录异常:', err);
					uni.hideLoading();
					uni.showToast({
						title: '网络异常，请重试',
						icon: 'none'
					});
					// 使用模拟数据进行测试
					this.loadSimulateData();
				});
			},
			
			/**
			 * 解析舌诊分析数据
			 * @param {Object} data 后端返回的分析数据
			 */
			parseAnalysisData(data) {
				console.log('2025-01-06 11:45:00,008-INFO-[complete][parseAnalysisData_001] 开始解析分析数据');
				console.log('2025-01-06 11:45:00,008-DEBUG-[complete][parseAnalysisData_debug] 完整API数据:', JSON.stringify(data, null, 2));
				
				try {
					// 保存原始分析数据
					this.analysisData = data;
					
					// 2025-01-27 解析显示设置配置
					if (data.display_settings) {
						console.log('2025-01-27 11:45:00,008-INFO-[complete][parseAnalysisData_001] 原始显示设置数据:', JSON.stringify(data.display_settings));
						this.displaySettings = {
							show_score: data.display_settings.show_score !== undefined ? data.display_settings.show_score : 1,
							show_score_value: data.display_settings.show_score_value !== undefined ? data.display_settings.show_score_value : 1, // 2025-01-27 解析评分分值显示设置
							show_symptoms: data.display_settings.show_symptoms !== undefined ? data.display_settings.show_symptoms : 1,
							show_tongue_analysis: data.display_settings.show_tongue_analysis !== undefined ? data.display_settings.show_tongue_analysis : 1,
							show_care_advice: data.display_settings.show_care_advice !== undefined ? data.display_settings.show_care_advice : 1,
							show_product_recommend: data.display_settings.show_product_recommend !== undefined ? data.display_settings.show_product_recommend : 1
						};
						console.log('2025-01-27 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析后显示设置:', JSON.stringify(this.displaySettings));
						console.log('2025-01-27 11:45:00,010-INFO-[complete][parseAnalysisData_003] show_score_value类型和值:', typeof data.display_settings.show_score_value, data.display_settings.show_score_value);
						console.log('2025-01-27 11:45:00,011-INFO-[complete][parseAnalysisData_004] 最终show_score_value:', this.displaySettings.show_score_value, typeof this.displaySettings.show_score_value);
					}
					
					// 解析基本信息
					// 优先从report的raw_report_json中获取分数和体质类型，因为constitution_score可能为"0.00"
					let reportScore = 89; // 默认值
					let reportConstitutionType = ''; // 从报告中获取的体质类型
					if (data.report && data.report.raw_report_json) {
						try {
							const reportData = JSON.parse(data.report.raw_report_json);
							if (reportData.score) {
								reportScore = parseFloat(reportData.score);
							}
							if (reportData.physique_name) {
								reportConstitutionType = reportData.physique_name;
							}
						} catch (e) {
							console.warn('2025-01-31 WARN-[complete][parseAnalysisData_005] 解析报告JSON失败:', e);
						}
					}

					this.healthScore = reportScore || parseFloat(data.constitution_score) || 89;
					// 优先使用报告中的体质类型，然后是API返回的体质类型，最后是默认值
					this.constitutionType = reportConstitutionType || data.constitution_type || '湿热体质、血瘀体质';

					console.log('2025-01-31 INFO-[complete][parseAnalysisData_006] 解析后的基本信息:', {
						healthScore: this.healthScore,
						constitutionType: this.constitutionType,
						originalScore: data.constitution_score,
						originalType: data.constitution_type,
						reportScore: reportScore,
						reportConstitutionType: reportConstitutionType
					});

					// 额外调试：检查体质类型是否包含"未知"
					if (this.constitutionType && this.constitutionType.includes('未知')) {
						console.warn('2025-01-31 WARN-[complete][parseAnalysisData_007] 检测到体质类型包含"未知":', this.constitutionType);
					}

					// 调试：输出原始报告数据
					if (data.report && data.report.raw_report_json) {
						console.log('2025-01-31 INFO-[complete][parseAnalysisData_008] 原始报告数据片段:', data.report.raw_report_json.substring(0, 500));
					}
					
					// 解析典型症状
					if (data.typical_symptoms) {
						this.typicalSymptoms = data.typical_symptoms.split('；').filter(symptom => symptom.trim());
						console.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 解析典型症状:', this.typicalSymptoms);
					} else if (data.analysis_result && data.analysis_result.typical_symptom) {
						// 处理嵌套在analysis_result中的typical_symptom字段
						this.typicalSymptoms = data.analysis_result.typical_symptom.split('；').filter(symptom => symptom.trim());
						console.log('2025-01-06 11:45:00,009-INFO-[complete][parseAnalysisData_002] 从analysis_result解析典型症状:', this.typicalSymptoms);
					} else {
						console.log('2025-01-06 11:45:00,009-WARN-[complete][parseAnalysisData_002] 未找到典型症状数据');
						this.typicalSymptoms = [];
					}
					
					// 解析舌象特征
					this.parseTongueFeatures(data);
					
					// 解析饮食建议
					this.parseDietSuggestions(data);
					
					// 解析运动建议
					this.parseExerciseSuggestions(data);
					
					// 解析健康建议
					this.healthSuggestions = data.health_suggestions || data.constitution_analysis || '';
					
					// 解析风险评估
					this.parseRiskAssessment(data);
					
					// 解析保养建议数据（新增）
					this.parseCareSuggestions(data);
					
					// 解析推荐商品数据（新增）
					this.parseRecommendProducts(data);

					// 获取推荐视频（新增）
					this.getRecommendVideos();

					// 解析报告数据
					if (data.report && data.report.raw_report_json) {
						this.parseReportData(data.report.raw_report_json);
					}
					
					console.log('2025-01-06 11:45:00,010-INFO-[complete][parseAnalysisData_003] 分析数据解析完成');
					
				} catch (error) {
					console.error('2025-01-06 11:45:00,011-ERROR-[complete][parseAnalysisData_004] 解析分析数据异常:', error);
					uni.showToast({
						title: '数据解析失败',
						icon: 'none'
					});
				}
			},
			
			/**
			 * 解析舌象特征数据
			 */
			parseTongueFeatures(data) {
				console.log('2025-01-06 11:45:00,012-INFO-[complete][parseTongueFeatures_001] 解析舌象特征');
				
				try {
					if (data.tongue_features) {
						const features = typeof data.tongue_features === 'string' 
							? JSON.parse(data.tongue_features) 
							: data.tongue_features;
						
						if (Array.isArray(features)) {
							this.tongueFeatures = features;
							// 提取异常特征
							this.abnormalFeatures = features.filter(feature => 
								feature.situation === '异常' && feature.interpret
							);
							console.log('2025-01-06 11:45:00,013-INFO-[complete][parseTongueFeatures_002] 异常特征:', this.abnormalFeatures.length);
						}
					}
				} catch (error) {
					console.error('2025-01-06 11:45:00,014-ERROR-[complete][parseTongueFeatures_003] 解析舌象特征失败:', error);
				}
			},
			
			/**
			 * 解析饮食建议
			 */
			parseDietSuggestions(data) {
				console.log('2025-01-06 11:45:00,015-INFO-[complete][parseDietSuggestions_001] 解析饮食建议');
				
				try {
					if (data.diet_suggestions) {
						const suggestions = typeof data.diet_suggestions === 'string' 
							? JSON.parse(data.diet_suggestions) 
							: data.diet_suggestions;
						
						if (Array.isArray(suggestions)) {
							this.dietSuggestions = suggestions;
							console.log('2025-01-06 11:45:00,016-INFO-[complete][parseDietSuggestions_002] 饮食建议数量:', suggestions.length);
						}
					}
				} catch (error) {
					console.error('2025-01-06 11:45:00,017-ERROR-[complete][parseDietSuggestions_003] 解析饮食建议失败:', error);
				}
			},
			
			/**
			 * 解析运动建议
			 */
			parseExerciseSuggestions(data) {
				console.log('2025-01-06 11:45:00,018-INFO-[complete][parseExerciseSuggestions_001] 解析运动建议');
				
				try {
					if (data.exercise_suggestions) {
						const suggestions = typeof data.exercise_suggestions === 'string' 
							? JSON.parse(data.exercise_suggestions) 
							: data.exercise_suggestions;
						
						if (Array.isArray(suggestions)) {
							this.exerciseSuggestions = suggestions;
							console.log('2025-01-06 11:45:00,019-INFO-[complete][parseExerciseSuggestions_002] 运动建议数量:', suggestions.length);
						}
					}
				} catch (error) {
					console.error('2025-01-06 11:45:00,020-ERROR-[complete][parseExerciseSuggestions_003] 解析运动建议失败:', error);
				}
			},
			
			/**
			 * 解析风险评估数据
			 */
			parseRiskAssessment(data) {
				console.log('2025-01-06 11:45:00,021-INFO-[complete][parseRiskAssessment_001] 解析风险评估');
				
				try {
					// 从风险评估文本中提取风险项目
					if (data.risk_assessment) {
						const riskText = data.risk_assessment;
						const riskLines = riskText.split('\n').filter(line => line.includes('：'));
						
						this.riskData = riskLines.map((line, index) => {
							const name = line.split('：')[0].replace(/^\d+\./, '').trim();
							// 根据内容严重程度估算风险百分比
							const severity = this.calculateRiskPercentage(line);
							return {
								name: name,
								percentage: severity
							};
						}).slice(0, 6); // 最多显示6个风险项
						
						console.log('2025-01-06 11:45:00,022-INFO-[complete][parseRiskAssessment_002] 风险数据:', this.riskData.length);
					}
				} catch (error) {
					console.error('2025-01-06 11:45:00,023-ERROR-[complete][parseRiskAssessment_003] 解析风险评估失败:', error);
				}
			},
			
			/**
			 * 计算风险百分比
			 */
			calculateRiskPercentage(riskText) {
				// 根据关键词估算风险程度
				const highRiskKeywords = ['严重', '高风险', '心血管', '糖尿病'];
				const mediumRiskKeywords = ['可能', '容易', '影响'];
				const lowRiskKeywords = ['轻微', '注意'];
				
				let percentage = 15; // 基础风险值
				
				if (highRiskKeywords.some(keyword => riskText.includes(keyword))) {
					percentage += 25;
				} else if (mediumRiskKeywords.some(keyword => riskText.includes(keyword))) {
					percentage += 15;
				} else if (lowRiskKeywords.some(keyword => riskText.includes(keyword))) {
					percentage += 5;
				}
				
				return Math.min(percentage, 60); // 最高不超过60%
			},
			
			/**
			 * 解析报告JSON数据
			 */
			parseReportData(rawReportJson) {
				console.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 解析报告JSON数据');
				
				try {
					const reportData = typeof rawReportJson === 'string' 
						? JSON.parse(rawReportJson) 
						: rawReportJson;
					
					// 解析典型症状（优先从reportData中获取）
					if (reportData.typical_symptom && this.typicalSymptoms.length === 0) {
						this.typicalSymptoms = reportData.typical_symptom.split('；').filter(symptom => symptom.trim());
						console.log('2025-01-06 11:45:00,024-INFO-[complete][parseReportData_001] 从报告数据解析典型症状:', this.typicalSymptoms);
					}
					
					// 解析更详细的特征数据
					if (reportData.features && Array.isArray(reportData.features)) {
						this.tongueFeatures = reportData.features;
						this.abnormalFeatures = reportData.features.filter(feature => 
							feature.feature_situation === '异常'
						);
					}
					
					// 解析建议数据
					if (reportData.advices) {
						// 保存完整的保养建议数据
						this.careSuggestions = reportData.advices;
						console.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 解析保养建议:', this.careSuggestions);
						
						if (reportData.advices.food) {
							this.dietSuggestions = reportData.advices.food;
						}
						if (reportData.advices.sport) {
							this.exerciseSuggestions = reportData.advices.sport;
						}
					}
					
					console.log('2025-01-06 11:45:00,025-INFO-[complete][parseReportData_002] 报告数据解析完成');
					
				} catch (error) {
					console.error('2025-01-06 11:45:00,026-ERROR-[complete][parseReportData_003] 解析报告数据失败:', error);
				}
			},
			
			/**
			 * 加载模拟数据用于测试
			 */
			loadSimulateData() {
				console.log('2025-01-06 11:45:00,027-INFO-[complete][loadSimulateData_001] 加载模拟保养建议数据');
				
				// 模拟保养建议数据
				this.careSuggestions = {
					food: [
						{
							advice: "避免高盐、高糖、高脂、火锅、烧烤等饮食。",
							title: "禁忌饮食"
						},
						{
							advice: "宜食性平之物，少食寒凉之物，如螃蟹、西瓜、苦瓜、冬瓜、梨、绿豆、冷饮等。",
							title: "建议饮食"
						}
					],
					music: [
						{
							advice: "音乐疗法：道教音乐崇尚'中和'的审美特征，具体来说体现为'阴、阳调和'、'动、静结合'和'散、正相间'等方面，常听这类曲目能让体内脏腑、气血平衡，使人心情愉快，精神饱满；改善睡眠；增强抗压能力等。其代表性曲目有：《啸咏朱陵府》《卫灵咒》《华夏颂》等。",
							title: "音乐建议"
						}
					],
					sleep: [
						{
							advice: "注意休息，劳逸结合，不可过劳，顺应四时，遵循春生、夏长、秋收、冬藏的规律，春夏应夜卧早起，秋季应早卧早起，冬季应早卧晚起。",
							title: "生活建议"
						},
						{
							advice: "保持情志舒畅，减少思虑，多与人沟通交流。",
							title: "情志建议"
						},
						{
							advice: "病室宜安静、舒适，有良好的通风环境。",
							title: "居住环境建议"
						}
					],
					sport: [
						{
							advice: "避免久坐、久站、久卧。成人可适当于上午时刻（避寒冷、避炎热）参与如舞蹈、气功、太极拳、八段锦、五禽戏等运动，强度宜低强度，一周2-3次为宜，每次30-40分钟，以自我感觉不过度劳累为主。",
							title: "运动建议"
						}
					],
					treatment: [
						{
							advice: "可做保健性艾灸。先将艾条点燃，放在灸盒中的铁纱上，并将温灸盒置于关元穴上方，盖好封盖以调节温度。每次灸20~30分钟。每日1次，7~10次为1个疗程。注意预防烫伤。",
							title: "艾灸保健"
						},
						{
							advice: "可做保健性耳穴疗法。取神门、心、脾、颈椎、肩、颈、等耳穴。将耳穴消毒，在耳穴上贴王不留行籽或耳穴压丸，用拇、食指进行垂直按压，施压至患出现沉、重、胀、痛感。每穴按压1分钟左右。每穴重复操作2~3遍，每天3~5次。双侧耳穴轮流使用，2日1次替换。",
							title: "耳穴疗法"
						},
						{
							advice: "可用六字诀进行呼吸训练以达到保健效果。六字诀是一种吐纳法。它是通过呬、呵、呼、嘘、吹、嘻六个字的不同发音口型，唇齿喉舌的用力不同，以牵动不同的脏腑经络气血的运行。\n方法：首先预备姿势，两足开立，与肩同宽，头正颈直，含胸拔背，松腰松胯，双膝微屈，全身放松，呼吸自然。\n其次联系呼吸，顺腹式呼吸，先呼后吸，呼所时读字，同时提肛缩肾，体重移至足跟。\n最后调息， 每个字读六遍后，调息一次，以稍事休息，恢复自然。",
							title: "功法导引"
						},
						{
							advice: "可行叩齿保健法以达到健脾益胃，纳气补肾的效果，古人认为齿健则身健，身健则长寿。方法：口唇轻闭，首先，上下门牙齿叩击9次，然后左侧上下牙齿叩击9次，右侧上下齿叩击9次，最后上下门齿再叩击9次。每日早晚各一次，每次3分钟左右。叩齿时可用双手指有节律地搓双侧耳孔，提拉双耳廓直到发热为止。",
							title: "叩齿保健法"
						}
					]
				};
				
				console.log('2025-01-06 11:45:00,028-INFO-[complete][loadSimulateData_002] 模拟数据加载完成');
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_012] 重新拍照功能
			retakePhoto() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_012] 用户点击重新拍照');
				uni.navigateTo({
					url: '/pagesB/shezhen/guide'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_013] 分享报告功能
			shareReport() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_013] 用户点击分享报告');
				uni.showToast({
					title: '分享功能开发中',
					icon: 'none'
				});
			},
			
			// 2025-01-03 22:55:53,565-INF0-[complete][method_014] 查看舌象图片
			viewTongueImage() {
				console.log('2025-01-03 22:55:53,565-INF0-[complete][method_014] 用户点击查看舌象图片');
				// 实现查看舌象图片的功能
				uni.navigateTo({
					url: '/pages/tongue/image'
				});
			},
			
			/**
			 * 根据舌象特征组获取标记样式
			 * @param {String} featureGroup 特征组名称
			 * @returns {Object} 包含定位样式的对象
			 */
			getMarkerStyle(featureGroup) {
				const positions = {
					// 舌根区域（现在在上方）
					'舌根部': { top: '15%', left: '50%' },
					
					// 舌质相关 - 分布在舌体中部
					'舌质': { top: '35%', left: '50%' },
					'舌形胖瘦': { top: '45%', left: '50%' },
					'舌中央': { top: '50%', left: '50%' },
					
					// 舌两侧 - 更靠边缘
					'舌齿痕': { top: '45%', left: '8%' },
					'舌两侧': { top: '40%', left: '5%' },
					'舌瘀斑瘀点': { top: '50%', left: '92%' },
					
					// 舌面特征 - 分散在不同位置
					'舌裂纹': { top: '42%', left: '78%' },
					'舌点刺': { top: '38%', left: '22%' },
					
					// 舌苔相关 - 主要分布在中上部
					'苔色': { top: '40%', left: '50%' },
					'舌苔腻': { top: '35%', left: '30%' },
					'舌苔腐': { top: '35%', left: '70%' },
					'舌苔厚薄': { top: '38%', left: '35%' },
					'舌苔润燥': { top: '38%', left: '65%' },
					'舌苔剥脱': { top: '32%', left: '50%' },
					
					// 舌尖区域（现在在下方）
					'舌色': { top: '75%', left: '50%' },
					'舌尖色': { top: '85%', left: '50%' },
					'舌尖': { top: '90%', left: '50%' }
				};
				
				// 获取基础位置
				const basePosition = positions[featureGroup] || { top: '50%', left: '50%' };
				
				// 小程序环境下使用不同的定位策略
				if (this.isMiniProgram) {
					// 转换百分比为rpx值，基于600rpx宽度和480rpx高度的容器
					const topRpx = Math.round((parseFloat(basePosition.top) / 100) * 480);
					const leftRpx = Math.round((parseFloat(basePosition.left) / 100) * 600);
					
					return {
						position: 'absolute',
						top: topRpx + 'rpx',
						left: leftRpx + 'rpx',
						marginTop: '-30rpx', // marker高度的一半
						marginLeft: '-50rpx', // marker宽度的一半
						zIndex: 10
					};
				} else {
					// 浏览器环境保持原有样式
				return {
					position: 'absolute',
					top: basePosition.top,
					left: basePosition.left,
						transform: 'translate(-50%, -50%)',
						zIndex: 10
				};
				}
			},
			
			/**
			 * 解析保养建议数据
			 * @param {Object} data 后端返回的分析数据
			 */
			parseCareSuggestions(data) {
				console.log('2025-01-06 11:45:00,029-INFO-[complete][parseCareSuggestions_001] 解析保养建议数据');
				
				try {
					// 直接从数据中获取advices字段
					if (data.advices) {
						this.careSuggestions = data.advices;
						console.log('2025-01-06 11:45:00,030-INFO-[complete][parseCareSuggestions_002] 保养建议数据解析成功:', this.careSuggestions);
						
						// 解析各类建议到原有字段（兼容性）
						if (data.advices.food) {
							this.dietSuggestions = data.advices.food;
						}
						if (data.advices.sport) {
							this.exerciseSuggestions = data.advices.sport;
						}
						
						return;
					}
					
					// 如果没有advices字段，尝试从其他字段构建
					let suggestions = {};
					
					// 检查是否有字符串形式的建议数据
					if (data.diet_suggestions) {
						try {
							const dietData = typeof data.diet_suggestions === 'string' 
								? JSON.parse(data.diet_suggestions) 
								: data.diet_suggestions;
							if (Array.isArray(dietData)) {
								suggestions.food = dietData;
							}
						} catch (e) {
							console.error('2025-01-06 11:45:00,031-ERROR-[complete][parseCareSuggestions_003] 解析饮食建议失败:', e);
						}
					}
					
					if (data.exercise_suggestions) {
						try {
							const exerciseData = typeof data.exercise_suggestions === 'string' 
								? JSON.parse(data.exercise_suggestions) 
								: data.exercise_suggestions;
							if (Array.isArray(exerciseData)) {
								suggestions.sport = exerciseData;
							}
						} catch (e) {
							console.error('2025-01-06 11:45:00,032-ERROR-[complete][parseCareSuggestions_004] 解析运动建议失败:', e);
						}
					}
					
					// 如果构建了建议数据，设置到careSuggestions
					if (Object.keys(suggestions).length > 0) {
						this.careSuggestions = suggestions;
						console.log('2025-01-06 11:45:00,033-INFO-[complete][parseCareSuggestions_005] 从其他字段构建保养建议:', this.careSuggestions);
					} else {
						console.log('2025-01-06 11:45:00,034-WARN-[complete][parseCareSuggestions_006] 未找到保养建议数据');
					}
					
				} catch (error) {
					console.error('2025-01-06 11:45:00,035-ERROR-[complete][parseCareSuggestions_007] 解析保养建议异常:', error);
				}
			},
			
			/**
			 * 获取优化的标记样式，避免重叠
			 * @param {String} featureGroup 特征组名称
			 * @param {Number} index 当前索引
			 * @param {Number} total 总数量
			 * @returns {Object} 包含定位样式的对象
			 */
			getOptimizedMarkerStyle(featureGroup, index, total) {
				// 先获取基础样式
				const baseStyle = this.getMarkerStyle(featureGroup);
				
				// 如果是小程序环境且有多个标签，需要避免重叠
				if (this.isMiniProgram && total > 1) {
					// 如果多个特征位置相同，进行偏移处理
					const samePositionOffset = this.calculateOffsetForSamePosition(featureGroup, index, total);
					if (samePositionOffset) {
						return {
							...baseStyle,
							top: `calc(${baseStyle.top} + ${samePositionOffset.top}rpx)`,
							left: `calc(${baseStyle.left} + ${samePositionOffset.left}rpx)`
						};
					}
				}
				
				return baseStyle;
			},
			
			/**
			 * 计算相同位置标签的偏移量
			 * @param {String} featureGroup 特征组名称
			 * @param {Number} index 当前索引
			 * @param {Number} total 总数量
			 * @returns {Object|null} 偏移量对象
			 */
			calculateOffsetForSamePosition(featureGroup, index, total) {
				// 定义容易重叠的特征组
				const centerGroups = ['舌质', '舌形胖瘦', '舌中央', '苔色'];
				const leftGroups = ['舌齿痕', '舌两侧'];
				const rightGroups = ['舌瘀斑瘀点', '舌裂纹'];
				
				if (centerGroups.includes(featureGroup)) {
					// 中心区域的标签呈环形分布，但半径更小，更紧凑
					const angle = (index * 360 / total) * Math.PI / 180;
					const radius = this.isMiniProgram ? 25 : 20; // 减小偏移半径
					return {
						marginTop: (Math.sin(angle) * radius) + (this.isMiniProgram ? 'rpx' : 'px'),
						marginLeft: (Math.cos(angle) * radius) + (this.isMiniProgram ? 'rpx' : 'px')
					};
				} else if (leftGroups.includes(featureGroup)) {
					// 左侧标签垂直分布，间距更小
					const offset = (index - total / 2) * 30; // 减小间隔
					return {
						marginTop: offset + (this.isMiniProgram ? 'rpx' : 'px')
					};
				} else if (rightGroups.includes(featureGroup)) {
					// 右侧标签垂直分布，间距更小
					const offset = (index - total / 2) * 30; // 减小间隔
					return {
						marginTop: offset + (this.isMiniProgram ? 'rpx' : 'px')
					};
				}
				
				return {};
			},
			getFeaturePositionClass(featureGroup) {
				const positionClasses = {
					'舌根部': 'root',
					'舌质': 'tongue',
					'舌形胖瘦': 'thickness',
					'舌中央': 'center',
					'舌齿痕': 'indent',
					'舌两侧': 'sides',
					'舌瘀斑瘀点': 'spots',
					'舌裂纹': 'cracks',
					'舌点刺': 'punctures',
					'苔色': 'mucus',
					'舌苔腻': 'thick',
					'舌苔腐': 'decay',
					'舌苔厚薄': 'thickness',
					'舌苔润燥': 'moisture',
					'舌苔剥脱': 'peeling',
					'舌色': 'color',
					'舌尖色': 'tip',
					'舌尖': 'tip'
				};
				const className = positionClasses[featureGroup] || '';
				console.log(`2025-01-06 12:30:00,001-INFO-[complete][getFeaturePositionClass] 特征组: ${featureGroup} -> CSS类: marker-${className}`);
				return className;
			},
			getSimpleMarkerStyle(featureGroup, index) {
				// 现在主要通过CSS类定位，只需要简单的样式补充
				const offset = this.calculateOffsetForSamePosition(featureGroup, index, this.displayedTongueFeatures.length);
				
				const style = {
					zIndex: 10 + index, // 确保层级正确
					...offset
				};
				
				console.log(`2025-01-06 12:30:00,002-INFO-[complete][getSimpleMarkerStyle] 特征组: ${featureGroup}, 索引: ${index}, 样式:`, style);
				
				// 只返回偏移样式，基础定位由CSS类处理
				return style;
			},
			
			/**
			 * 解析推荐商品数据 - 新增方法
			 * @param {Object} data 后端返回的分析数据
			 */
			parseRecommendProducts(data) {
				console.log('2025-01-06 11:45:00,036-INFO-[complete][parseRecommendProducts_001] 解析推荐商品数据');
				
				try {
					// 从 recommend_products 字段解析推荐商品
					if (data.recommend_products && Array.isArray(data.recommend_products)) {
						this.recommendProducts = data.recommend_products;
						console.log('2025-01-06 11:45:00,037-INFO-[complete][parseRecommendProducts_002] 推荐商品数据解析成功:', this.recommendProducts.length, '个商品');
					}
					
					// 获取推荐标题
					if (data.recommend_title) {
						this.recommendTitle = data.recommend_title;
					}
					
					// 如果没有推荐商品，尝试调用独立的推荐商品接口
					if ((!this.recommendProducts || this.recommendProducts.length === 0) && this.constitutionType && this.healthScore) {
						console.log('2025-01-06 11:45:00,038-INFO-[complete][parseRecommendProducts_003] 数据中无推荐商品，调用独立接口获取');
						this.getRecommendProductsFromApi();
					}
					
				} catch (error) {
					console.error('2025-01-06 11:45:00,039-ERROR-[complete][parseRecommendProducts_004] 解析推荐商品异常:', error);
				}
			},
			
			/**
			 * 从API获取推荐商品 - 新增方法
			 */
			getRecommendProductsFromApi() {
				console.log('2025-01-06 11:45:00,040-INFO-[complete][getRecommendProductsFromApi_001] 开始从API获取推荐商品');
				
				const app = getApp();
				app.post('ApiSheZhen/getRecommendProducts', {
					constitution_type: this.constitutionType,
					constitution_score: this.healthScore
				}, (response) => {
					console.log('2025-01-06 11:45:00,041-INFO-[complete][getRecommendProductsFromApi_002] 推荐商品API响应:', response);
					
					if (response && (response.status === 1 || response.code === 1)) {
						const recommendData = response.data;
						if (recommendData && recommendData.products && Array.isArray(recommendData.products)) {
							this.recommendProducts = recommendData.products;
							if (recommendData.recommend_title) {
								this.recommendTitle = recommendData.recommend_title;
							}
							console.log('2025-01-06 11:45:00,042-INFO-[complete][getRecommendProductsFromApi_003] 成功获取推荐商品:', this.recommendProducts.length, '个');
						}
					} else {
						console.log('2025-01-06 11:45:00,043-WARN-[complete][getRecommendProductsFromApi_004] 获取推荐商品失败:', response?.msg);
					}
				}, (err) => {
					console.error('2025-01-06 11:45:00,044-ERROR-[complete][getRecommendProductsFromApi_005] 获取推荐商品异常:', err);
				});
			},

			/**
			 * 获取推荐视频 - 新增方法
			 */
			getRecommendVideos() {
				console.log('2025-01-31 INFO-[complete][getRecommendVideos_001] 开始获取推荐视频');

				// 获取体质类型和得分
				let constitutionType = this.constitutionType || '';
				let constitutionScore = this.healthScore || 0;

				console.log('2025-01-31 INFO-[complete][getRecommendVideos_001.5] 体质信息:', {
					constitutionType: constitutionType,
					constitutionScore: constitutionScore
				});

				// 如果没有体质信息，不获取推荐视频
				if (!constitutionType && !constitutionScore) {
					console.log('2025-01-31 INFO-[complete][getRecommendVideos_002] 无体质信息，跳过视频推荐');
					return;
				}

				const app = getApp();

				// 调用推荐视频接口
				app.post('ApiSheZhen/getRecommendProducts', {
					constitution_type: constitutionType,
					constitution_score: constitutionScore
				}, (response) => {
					console.log('2025-01-31 INFO-[complete][getRecommendVideos_003] 获取推荐视频结果:', response);

					// 兼容多种响应格式
					if (response && (response.code === 1 || response.status === 1) && response.data && response.data.products) {
						// 筛选出视频类型的推荐
						const videos = response.data.products.filter(item => item.type === 'video');
						this.recommendVideos = videos;
						console.log('2025-01-31 INFO-[complete][getRecommendVideos_004] 获取到推荐视频数量:', videos.length);

						// 如果有视频数据，输出详细信息
						if (videos.length > 0) {
							console.log('2025-01-31 INFO-[complete][getRecommendVideos_004.5] 视频详情:', videos);
						}
					} else {
						console.log('2025-01-31 INFO-[complete][getRecommendVideos_005] 无推荐视频数据，响应详情:', {
							hasResponse: !!response,
							code: response?.code,
							status: response?.status,
							hasData: !!response?.data,
							hasProducts: !!response?.data?.products,
							productsLength: response?.data?.products?.length
						});

						// 临时添加测试数据，方便调试
						console.log('2025-01-31 INFO-[complete][getRecommendVideos_005.5] 添加测试视频数据');
						this.recommendVideos = [{
							id: 999,
							name: '测试调理视频',
							type: 'video',
							url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
							pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
							recommend_reason: '适合您的体质类型',
							view_num: 1000,
							zan_num: 50
						}];
					}
				}, (error) => {
					console.error('2025-01-31 ERROR-[complete][getRecommendVideos_006] 获取推荐视频失败:', error);

					// 出错时也添加测试数据
					console.log('2025-01-31 INFO-[complete][getRecommendVideos_006.5] 接口出错，添加测试视频数据');
					this.recommendVideos = [{
						id: 998,
						name: '错误测试视频',
						type: 'video',
						url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
						pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
						recommend_reason: '测试推荐理由',
						view_num: 500,
						zan_num: 25
					}];
				});
			},

			/**
			 * 播放视频 - 新增方法
			 */
			playVideo(video) {
				console.log('2025-01-31 INFO-[complete][playVideo_001] 播放视频:', video);

				if (!video.url) {
					uni.showToast({
						title: '视频地址无效',
						icon: 'none'
					});
					return;
				}

				// 处理视频URL，确保中文字符正确编码
				let processedVideo = { ...video };
				if (video.url && video.url.includes('%')) {
					// URL已经编码，直接使用
					processedVideo.url = video.url;
				} else if (video.url) {
					// 对URL中的中文字符进行编码
					try {
						const urlParts = video.url.split('/');
						const encodedParts = urlParts.map(part => {
							// 只对文件名部分进行编码，保留协议和域名
							if (part.includes('.') && (part.includes('mp4') || part.includes('avi') || part.includes('mov'))) {
								return encodeURIComponent(part);
							}
							return part;
						});
						processedVideo.url = encodedParts.join('/');
						console.log('2025-01-31 INFO-[complete][playVideo_001.5] URL编码处理:', {
							original: video.url,
							processed: processedVideo.url
						});
					} catch (e) {
						console.warn('2025-01-31 WARN-[complete][playVideo_001.6] URL编码失败，使用原始URL:', e);
						processedVideo.url = video.url;
					}
				}

				// 设置当前播放视频并显示播放器
				this.currentVideo = processedVideo;
				this.showVideoPlayer = true;

				// 记录视频播放事件
				console.log('2025-01-31 INFO-[complete][playVideo_002] 开始播放视频:', video.name);
				console.log('2025-01-31 INFO-[complete][playVideo_003] 视频URL:', processedVideo.url);
			},

			/**
			 * 关闭视频播放器 - 新增方法
			 */
			closeVideoPlayer() {
				console.log('2025-01-31 INFO-[complete][closeVideoPlayer_001] 关闭视频播放器');
				this.showVideoPlayer = false;
				this.currentVideo = {};
			},

			/**
			 * 视频播放错误处理 - 新增方法
			 */
			onVideoError(e) {
				console.error('2025-01-31 ERROR-[complete][onVideoError_001] 视频播放错误:', e);
				uni.showToast({
					title: '视频播放失败',
					icon: 'none'
				});
			},

			/**
			 * 视频开始播放事件 - 新增方法
			 */
			onVideoPlay(video) {
				console.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', video.name);
			},

			/**
			 * 视频暂停事件 - 新增方法
			 */
			onVideoPause(video) {
				console.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停:', video.name);
			},

			/**
			 * 视频开始播放 - 新增方法
			 */
			onVideoPlay(e) {
				console.log('2025-01-31 INFO-[complete][onVideoPlay_001] 视频开始播放:', e);
			},

			/**
			 * 视频暂停播放 - 新增方法
			 */
			onVideoPause(e) {
				console.log('2025-01-31 INFO-[complete][onVideoPause_001] 视频暂停播放:', e);
			},

			/**
			 * 视频开始加载 - 新增方法
			 */
			onVideoLoadStart(e) {
				console.log('2025-01-31 INFO-[complete][onVideoLoadStart_001] 视频开始加载:', e);
			},

			/**
			 * 视频可以播放 - 新增方法
			 */
			onVideoCanPlay(e) {
				console.log('2025-01-31 INFO-[complete][onVideoCanPlay_001] 视频可以播放:', e);
			},

			/**
			 * 获取推荐徽章文本 - 新增方法
			 */
			getRecommendBadge(reason) {
				if (!reason) return '';
				
				if (reason.includes('体质匹配')) return '体质';
				if (reason.includes('得分推荐')) return '得分';
				if (reason.includes('热销')) return '热销';
				if (reason.includes('专业')) return '专业';
				return '推荐';
			},
			
			/**
			 * 查看商品详情 - 新增方法
			 */
			viewProductDetail(product) {
				console.log('2025-01-06 11:45:00,045-INFO-[complete][viewProductDetail_001] 查看商品详情:', product);
				
				// 根据商品类型跳转到对应的详情页面
				if (product.product_type === 2) {
					// 课程详情
					uni.navigateTo({
						url: `/pages/course/detail?id=${product.id}`
					});
				} else {
					// 商品详情
					uni.navigateTo({
						url: `/pages/product/detail?id=${product.id}`
					});
				}
			},
			
			/**
			 * 购买商品 - 新增方法
			 */
			buyProduct(product) {
				console.log('2025-01-06 11:45:00,046-INFO-[complete][buyProduct_001] 购买商品:', product);
				
				// 确认购买对话框
				uni.showModal({
					title: '确认购买',
					content: `确定要购买"${product.name}"吗？`,
					success: (res) => {
						if (res.confirm) {
							// 根据商品类型进行不同的购买流程
							if (product.product_type === 2) {
								// 课程购买
								uni.navigateTo({
									url: `/pages/course/buy?id=${product.id}`
								});
							} else {
								// 商品购买 - 加入购物车或直接购买
								uni.navigateTo({
									url: `/pages/product/buy?id=${product.id}&type=direct`
								});
							}
						}
					}
				});
			},
			
			/**
			 * 导航到商品详情页 - 新增方法
			 */
			navigateToProduct(product) {
				console.log('2025-01-06 11:45:00,047-INFO-[complete][navigateToProduct_001] 导航到商品详情:', product);
				
				// 根据产品类型使用不同的路径
				let url = '';
				const productId = product.id || 290; // 如果没有id则使用默认值290
				
				if (product.type === 'course') {
					// 课程路径
					url = `/activity/kecheng/product?id=${productId}`;
				} else {
					// 商品路径
					url = `/shopPackage/shop/product?id=${productId}`;
				}
				
				console.log('2025-01-06 11:45:00,048-INFO-[complete][navigateToProduct_002] 准备导航到:', url);
				
				uni.navigateTo({
					url: url,
					success: () => {
						console.log('2025-01-06 11:45:00,049-INFO-[complete][navigateToProduct_003] 成功导航到详情页:', url);
					},
					fail: (err) => {
						console.error('2025-01-06 11:45:00,050-ERROR-[complete][navigateToProduct_004] 导航失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},
			
			/**
			 * 添加到购物车 - 新增方法
			 */
			addToCart(product) {
				console.log('2025-01-06 11:45:00,051-INFO-[complete][addToCart_001] 点击购物车按钮:', product);
				
				if (product.type === 'course') {
					// 课程类型：直接跳转到课程详情页
					this.navigateToProduct(product);
				} else {
					// 商品类型：添加到购物车
					uni.showToast({
						title: '已添加到购物车',
						icon: 'success',
						duration: 1500
					});
					
					// 这里可以调用实际的购物车API
					// const app = getApp();
					// app.post('Cart/add', {
					//     product_id: product.id,
					//     quantity: 1
					// }, (response) => {
					//     if (response.status === 1) {
					//         uni.showToast({
					//             title: '添加成功',
					//             icon: 'success'
					//         });
					//     }
					// });
				}
			},
		},
		onLoad(options) {
			console.log('2025-01-06 11:45:00,100-INFO-[complete][onLoad_001] 舌诊完整页面加载，参数:', options);
			
			// 检测是否为小程序环境
			// #ifdef MP
			this.isMiniProgram = true;
			console.log('2025-01-06 11:45:00,101-INFO-[complete][onLoad_002] 检测到小程序环境');
			// #endif
			
			// #ifndef MP
			console.log('2025-01-06 11:45:00,101-INFO-[complete][onLoad_002] 检测到非小程序环境(浏览器)');
			// #endif
			
			// 获取传递的参数
			if (options.record_id || options.recordId) {
				this.recordId = options.record_id || options.recordId;
				console.log('2025-01-06 11:45:00,102-INFO-[complete][onLoad_003] 获取记录ID:', this.recordId);
			}
			
			if (options.order_no || options.orderNo) {
				this.orderNo = options.order_no || options.orderNo;
				console.log('2025-01-06 11:45:00,103-INFO-[complete][onLoad_004] 获取订单号:', this.orderNo);
			}
			
			if (options.api_type || options.apiType) {
				this.apiType = options.api_type || options.apiType;
				console.log('2025-01-06 11:45:00,104-INFO-[complete][onLoad_005] 获取API类型:', this.apiType);
			}
			
			// 2025-01-27 获取订单ID参数
			if (options.order_id) {
				this.orderId = options.order_id;
				console.log('2025-01-27 11:45:00,106-INFO-[complete][onLoad_006] 获取支付订单ID:', this.orderId);
			}
			
			// 临时添加测试视频数据，方便调试
			setTimeout(() => {
				console.log('2025-01-31 INFO-[complete][onLoad_007] 设置测试视频数据');
				this.recommendVideos = [{
					id: 1001,
					name: '测试调理视频 - onLoad',
					type: 'video',
					url: 'https://weiyiia1.azheteng.cn/%E6%8C%89%E6%91%A9/%E4%B8%8A%E8%82%A2%E9%83%A8%E6%8C%89%E6%91%A9.mp4',
					pic: 'https://kuaifengimg.azheteng.cn/upload/106/20250707/49b755be53058df7601bdc55b4bd5de5_thumb.jpg',
					recommend_reason: '适合您的体质类型 - 测试数据',
					view_num: 1500,
					zan_num: 75
				}];
				console.log('2025-01-31 INFO-[complete][onLoad_008] 测试视频数据设置完成，数量:', this.recommendVideos.length);
			}, 1000);

			// 检查必要参数
			if (this.recordId) {
				// 获取舌诊分析数据
				this.getAnalysisRecord();
			} else {
				console.error('2025-01-06 11:45:00,105-ERROR-[complete][onLoad_006] 缺少必要参数recordId');
				uni.showToast({
					title: '参数错误，请重新进入',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 2000);
			}
		}
	}
</script>

<style scoped>
/* 2025-01-03 22:55:53,565-INF0-[complete][style_001] 舌诊完整页面样式定义 */

.tongue-complete {
	background-color: #ffffff;
	min-height: 100vh;
	padding-bottom: 120rpx; /* 为底部浮动按钮留出空间 */
}

/* 顶部导航样式 */
.top-nav {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: white;
	position: relative;
}

.nav-left {
	display: flex;
	align-items: center;
}

.back-icon {
	font-size: 40rpx;
	color: #333;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 500;
	color: #333;
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.menu-icon {
	font-size: 30rpx;
	color: #666;
}

.record-icon {
	font-size: 30rpx;
	color: #333;
}

/* 医院信息 */
.hospital-info {
	background-color: #e8f4ff;
	padding: 20rpx;
	text-align: center;
	font-size: 24rpx;
	color: #666;
}

/* 报告标签页 */
.report-tabs {
	display: flex;
	background-color: white;
	padding: 0 30rpx;
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 30rpx 0;
	position: relative;
}

.tab-item.active .tab-text {
	color: #1890ff;
	font-weight: 500;
}

.tab-line {
	position: absolute;
	bottom: 0;
	left: 50%;
	transform: translateX(-50%);
	width: 60rpx;
	height: 6rpx;
	background-color: #1890ff;
	border-radius: 3rpx;
}

.tab-text {
	font-size: 32rpx;
	color: #333;
}

/* 健康得分卡片 */
.health-card {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
	border-radius: 20rpx;
	margin: 30rpx 20rpx;
	padding: 40rpx 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(79, 172, 254, 0.3);
}

.health-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.health-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.health-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	padding: 10rpx;
	object-fit: cover;
	object-position: center;
	flex-shrink: 0;
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.health-symptoms {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	flex: 1;
	min-width: 0;
}

.main-symptom, .sub-symptom {
	display: flex;
	align-items: center;
	gap: 10rpx;
	flex-wrap: wrap;
}

.symptom-label {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 400;
	white-space: nowrap;
}

.symptom-value {
	font-size: 32rpx;
	color: white;
	font-weight: 600;
	background: rgba(255, 255, 255, 0.2);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	word-break: break-all;
	line-height: 1.4;
}

.health-right {
	display: flex;
	align-items: center;
}

.score-circle {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 20rpx rgba(79, 172, 254, 0.4);
	border: 3rpx solid rgba(255, 255, 255, 0.5);
}

.score-inner {
	text-align: center;
}

.score-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #4facfe;
	display: block;
	line-height: 1;
}

.score-number.no-score {
	font-size: 48rpx;
	font-weight: 700;
	color: #cccccc;
	display: block;
	line-height: 1;
}

.score-text {
	font-size: 24rpx;
	color: #4facfe;
	font-weight: 500;
	margin-top: 8rpx;
	display: block;
}

/* 智能问诊 */
.ai-diagnosis {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 30rpx;
	padding: 30rpx;
	background-color: white;
	border-radius: 15rpx;
}

.ai-left {
	flex: 1;
}

.ai-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.ai-subtitle {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 5rpx;
}

.ai-description {
	font-size: 24rpx;
	color: #999;
}

.login-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}

.analysis-complete-btn {
	background-color: #52c41a;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
}

.loading-btn {
	background-color: #faad14;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	animation: pulse 1.5s infinite;
}

@keyframes pulse {
	0% { opacity: 1; }
	50% { opacity: 0.6; }
	100% { opacity: 1; }
}

/* 可能体征 */
.symptoms-section {
	margin: 30rpx 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 30rpx;
}

.symptoms-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
	margin-top: 20rpx;
}

.symptom-tag {
	background: rgba(79, 172, 254, 0.08);
	color: #4facfe;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
	font-weight: 500;
	border: 1rpx solid rgba(79, 172, 254, 0.2);
	backdrop-filter: blur(10rpx);
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}

/* 舌象分析 */
.tongue-analysis {
	margin: 30rpx;
}

.tongue-diagram {
	background-color: white;
	border-radius: 15rpx;
	padding: 40rpx;
	position: relative;
	text-align: center;
}

.tongue-svg {
	width: 600rpx;
	height: 480rpx;
	position: relative;
	background: #ffffff;
	border-radius: 15rpx;
	overflow: hidden;
	margin: 0 auto;
}

/* CSS绘制的舌头形状 */
.tongue-shape {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 350rpx;
	height: 400rpx;
	background: linear-gradient(135deg, #ffb3ba, #ff8a9b);
	border-radius: 85rpx 85rpx 175rpx 175rpx;
	transform: translate(-50%, -50%);
	box-shadow: inset 0 15rpx 30rpx rgba(255, 255, 255, 0.3),
	            inset 0 -15rpx 30rpx rgba(0, 0, 0, 0.1),
	            0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 舌根区域高光效果 */
.tongue-shape::before {
	content: '';
	position: absolute;
	top: 20rpx;
	left: 50%;
	width: 200rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.3);
	border-radius: 50%;
	transform: translateX(-50%);
}

/* 舌苔覆盖区域 */
.tongue-shape::after {
	content: '';
	position: absolute;
	top: 15%;
	left: 50%;
	width: 280rpx;
	height: 280rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 60rpx 60rpx 140rpx 140rpx;
	transform: translateX(-50%);
	box-shadow: inset 0 5rpx 15rpx rgba(255, 255, 255, 0.2);
}

.tongue-markers {
	position: relative;
	width: 100%;
	height: 100%;
	/* 确保小程序端的定位基准 */
	overflow: visible;
	z-index: 1;
}

.marker {
	position: absolute;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 10;
	/* 确保小程序端的样式继承 */
	box-sizing: border-box;
	/* 默认居中对齐 */
	transform: translate(-50%, -50%);
	/* 移除动态样式依赖，使用CSS类定位 */
	/* 适当缩小标记整体尺寸 */
	zoom: 0.9;
}

.marker.abnormal .marker-icon {
	background: #ff4d4f;
	color: white;
}

.marker.normal .marker-icon {
	background: #52c41a;
	color: white;
}

.marker-icon {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: bold;
	margin-bottom: 6rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.marker-text {
	font-size: 20rpx;
	color: #333;
	background: rgba(255, 255, 255, 0.95);
	padding: 3rpx 8rpx;
	border-radius: 10rpx;
	text-align: center;
	box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
	white-space: nowrap;
	font-weight: 500;
	max-width: 120rpx; /* 限制文本宽度 */
	overflow: hidden;
	text-overflow: ellipsis;
}

.tongue-photo-btn {
	position: absolute;
	bottom: 15rpx;
	left: 50%;
	transform: translateX(-50%);
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 10rpx 25rpx;
	border-radius: 25rpx;
	font-size: 24rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.photo-btn-text {
	color: white;
	font-weight: 500;
}

/* 体征异常 */
.abnormal-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.abnormal-title {
	margin-bottom: 30rpx;
}

.abnormal-count {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.abnormal-list {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.abnormal-item {
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 30rpx;
}

.abnormal-item:last-child {
	border-bottom: none;
	padding-bottom: 0;
}

.abnormal-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.abnormal-icon {
	width: 32rpx;
	height: 32rpx;
	background-color: #ff4d4f;
	color: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
}

.abnormal-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #ff4d4f;
}

.abnormal-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.abnormal-desc-container {
	display: flex;
	align-items: flex-end;
	gap: 20rpx;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	flex-shrink: 0;
}

/* 舌象体征论述 - 简约医疗风格 */
.theory-section {
	margin: 0rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	/* box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2); */
}

.theory-header-wrapper {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid rgba(24, 144, 255, 0.1);
}

.theory-icon {
	font-size: 40rpx;
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.3);
}

.theory-subtitle {
	font-size: 24rpx;
	color: #8a92b2;
	font-weight: 500;
	margin-left: auto;
	background: rgba(255, 255, 255, 0.8);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

/* 理论卡片 */
.theory-card {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.theory-card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.theory-card:last-child {
	margin-bottom: 0;
}

/* 体质分析卡片 */
.constitution-card {
	border-left: 6rpx solid #52c41a;
	background: linear-gradient(135deg, rgba(246, 255, 237, 0.9), rgba(240, 249, 235, 0.7));
}

.constitution-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	background: radial-gradient(circle, rgba(82, 196, 26, 0.1) 0%, transparent 70%);
	border-radius: 50%;
}

.constitution-tag {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}

/* 证候介绍卡片 */
.syndrome-card {
	border-left: 6rpx solid #1890ff;
	background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(230, 247, 255, 0.7));
}

.syndrome-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 100rpx;
	height: 100rpx;
	background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 70%);
	border-radius: 50%;
}

.syndrome-tag {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: white;
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 15rpx;
	flex: 1;
}

.theory-tag {
	font-size: 22rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.theory-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #1a202c;
	letter-spacing: 0.5rpx;
}

.header-right {
	display: flex;
	align-items: center;
}

/* 状态指示器 */
.status-indicator {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(255, 255, 255, 0.8);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.status-indicator.active .status-dot {
	color: #52c41a;
	animation: pulse 2s infinite;
}

.status-text {
	font-size: 22rpx;
	font-weight: 500;
	color: #52c41a;
}

@keyframes pulse {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.7; }
}

/* 卡片内容 */
.card-content {
	position: relative;
}

.content-wrapper {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.theory-description {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	font-weight: 400;
	letter-spacing: 0.5rpx;
	text-align: justify;
	text-justify: inter-ideograph;
}

/* 医学印章 */
.medical-stamp {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-top: 16rpx;
	padding: 12rpx 16rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 30rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	align-self: flex-end;
}

.doctor-avatar-small {
	width: 36rpx;
	height: 36rpx;
	border-radius: 50%;
	border: 2rpx solid rgba(255, 255, 255, 0.8);
	flex-shrink: 0;
}

.stamp-text {
	font-size: 22rpx;
	color: #718096;
	font-weight: 500;
	letter-spacing: 0.5rpx;
}

/* 空状态 */
.empty-theory-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 20rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
	position: relative;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
	opacity: 0.6;
}

.empty-title {
	font-size: 32rpx;
	color: #64748b;
	font-weight: 600;
	margin-bottom: 12rpx;
	letter-spacing: 0.5rpx;
}

.empty-description {
	font-size: 26rpx;
	color: #94a3b8;
	font-weight: 400;
	line-height: 1.6;
	margin-bottom: 30rpx;
}

/* 加载动画 */
.loading-dots {
	display: flex;
	justify-content: center;
	gap: 8rpx;
}

.dot {
	width: 8rpx;
	height: 8rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	animation: loading 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes loading {
	0%, 80%, 100% {
		transform: scale(0);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 患病风险 */
.risk-section {
	margin: 30rpx 20rpx;
}

.risk-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
	margin-top: 30rpx;
	justify-items: center;
}

.risk-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15rpx;
}

.risk-progress-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	position: relative;
	padding: 6rpx;
	background: #ffffff;
	box-shadow: 0 4rpx 16rpx rgba(79, 172, 254, 0.15);
}

.progress-ring {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	position: absolute;
	top: 0;
	left: 0;
	padding: 2rpx;
	box-sizing: border-box;
}

.progress-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	width: 100rpx;
	height: 100rpx;
	background: #ffffff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.1);
}

.progress-percentage {
	font-size: 24rpx;
	font-weight: 600;
	color: #4facfe;
}

.risk-name {
	font-size: 24rpx;
	color: #333;
	text-align: center;
	font-weight: 500;
	line-height: 1.4;
	max-width: 120rpx;
}

/* 需要警惕 */
.warning-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.warning-list {
	margin-top: 30rpx;
}

.warning-item {
	margin-bottom: 30rpx;
	position: relative;
}

.warning-item:last-child {
	margin-bottom: 0;
}

.warning-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 10rpx;
}

.warning-bullet {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
}

.warning-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
}

.warning-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	padding-right: 100rpx;
}

.doctor-avatar {
	position: absolute;
	bottom: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 结果解读 */
.result-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.constitution-chart {
	margin: 40rpx 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 30rpx;
}

.constitution-circle {
	width: 400rpx;
	height: 400rpx;
	border: 4rpx solid #e6f4ff;
	border-radius: 50%;
	position: relative;
	background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
	box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);
}

.constitution-inner {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.constitution-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	background: linear-gradient(135deg, #ffffff 0%, #f8fdff 100%);
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.15);
	border: 2rpx solid #e6f4ff;
}

.constitution-type {
	font-size: 36rpx;
	font-weight: 600;
	color: #1890ff;
	margin-bottom: 5rpx;
}

.constitution-indicator {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.indicator-dot {
	position: absolute;
	width: 20rpx;
	height: 20rpx;
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	border-radius: 50%;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
	0% {
		transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	}
	50% {
		transform: scale(1.2);
		box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.6);
	}
	100% {
		transform: scale(1);
		box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
	}
}

.direction-label {
	position: absolute;
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
	background: rgba(255, 255, 255, 0.9);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 1rpx solid #e6f4ff;
	transform: translate(-50%, -50%);
}

.direction-label.top {
	top: 20rpx;
	left: 50%;
}

.direction-label.right {
	right: 20rpx;
	top: 50%;
	transform: translate(50%, -50%);
}

.direction-label.bottom {
	bottom: 20rpx;
	left: 50%;
	transform: translate(-50%, 50%);
}

.direction-label.left {
	left: 20rpx;
	top: 50%;
	transform: translate(-50%, -50%);
}

.scale-marks {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 100%;
	height: 100%;
}

.scale-mark {
	position: absolute;
	width: 2rpx;
	height: 20rpx;
	background: linear-gradient(to bottom, #1890ff, rgba(24, 144, 255, 0.3));
	top: 10rpx;
	left: 50%;
	transform-origin: 50% 190rpx;
	border-radius: 1rpx;
}

.constitution-status {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
	background: rgba(255, 255, 255, 0.9);
	padding: 12rpx 20rpx;
	border-radius: 25rpx;
	border: 1rpx solid #e6f4ff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}

.status-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
}

.result-description {
	margin: 30rpx 0;
}

.result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
	margin-bottom: 15rpx;
}

.result-text.secondary {
	font-size: 24rpx;
	color: #999;
}

.result-note {
	background-color: #f8f9fa;
	padding: 30rpx;
	border-radius: 15rpx;
	position: relative;
}

.note-content {
	padding-right: 100rpx;
}

.note-text {
	font-size: 24rpx;
	color: #999;
	line-height: 1.6;
}

.doctor-avatar-note {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* VIP会员专享 */
.vip-section {
	margin: 30rpx;
	background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
	border-radius: 15rpx;
	padding: 30rpx;
}

.vip-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.vip-icon {
	font-size: 32rpx;
}

.vip-text {
	font-size: 28rpx;
	color: #8b4513;
	font-weight: 500;
}

.vip-card {
	text-align: center;
}

.vip-title {
	font-size: 32rpx;
	color: #8b4513;
	margin-bottom: 10rpx;
	display: block;
}

.vip-subtitle {
	font-size: 48rpx;
	color: #8b4513;
	font-weight: bold;
}

/* 报告解读 */
.report-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.report-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.report-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.member-badge {
	background-color: #1890ff;
	color: white;
	padding: 5rpx 10rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
}

.symptoms-summary {
	margin-bottom: 30rpx;
}

.summary-text {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
	margin-left: 10rpx;
}

.symptom-highlight {
	font-size: 28rpx;
	font-weight: 500;
	color: #1890ff;
}

.main-symptom-detail {
	margin-bottom: 30rpx;
}

.symptom-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.symptom-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.view-detail-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}

.doctor-section {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.doctor-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.doctor-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
}

/* 调理原则 */
.principle-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.principle-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.principle-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.principle-list {
	margin-bottom: 30rpx;
}

.principle-item {
	margin-bottom: 15rpx;
}

.principle-bullet {
	color: #ff4d4f;
	font-size: 24rpx;
	font-weight: bold;
	margin-right: 10rpx;
}

.principle-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
}

.view-principle-btn {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
	margin-bottom: 30rpx;
}

.custom-plan-text {
	font-size: 28rpx;
	color: #666;
	text-align: center;
}

/* 今日营养目标 */
.nutrition-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.nutrition-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.nutrition-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.chart-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chart-left {
	flex: 1;
}

.pie-chart {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	position: relative;
	margin-bottom: 20rpx;
}

.pie-center {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	text-align: center;
	background-color: white;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.calories-number {
	font-size: 32rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 5rpx;
	color: #333;
}

.calories-unit {
	font-size: 20rpx;
	color: #666;
}

.chart-right {
	flex: 1;
}

.nutrition-item {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.nutrition-color {
	width: 20rpx;
	height: 20rpx;
	border-radius: 4rpx;
	flex-shrink: 0;
}

.nutrition-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
}

.nutrition-percent {
	font-size: 24rpx;
	color: #333;
	width: 60rpx;
	text-align: right;
}

.nutrition-amount {
	font-size: 24rpx;
	color: #666;
	width: 80rpx;
	text-align: right;
}

/* 今日专属膳方 */
.recipe-section {
	margin: 30rpx;
	background-color: white;
	border-radius: 15rpx;
	padding: 30rpx;
}

.recipe-header {
	display: flex;
	align-items: center;
	gap: 10rpx;
	margin-bottom: 20rpx;
}

.recipe-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.adjust-plan {
	background-color: #1890ff;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 25rpx;
	font-size: 28rpx;
	margin-left: auto;
}

.recipe-card {
	background-color: #f0f0f0;
	border-radius: 15rpx;
	padding: 30rpx;
}

.recipe-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.recipe-info {
	flex: 1;
}

.recipe-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 10rpx;
}

.recipe-effect {
	font-size: 28rpx;
	color: #666;
}

.recipe-image-container {
	width: 120rpx;
	height: 120rpx;
	border-radius: 15rpx;
	overflow: hidden;
}

.recipe-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.doctor-recommendation {
	margin-top: 20rpx;
}

/* 底部按钮 - 浮动样式 */
.bottom-buttons {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	gap: 20rpx;
	padding: 20rpx 30rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom)); /* 适配刘海屏 */
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 1) 100%);
	backdrop-filter: blur(20rpx);
	border-top: 1rpx solid rgba(0, 0, 0, 0.08);
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
	z-index: 999;
}

.btn-secondary {
	flex: 1;
	padding: 28rpx 25rpx;
	text-align: center;
	background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 28rpx;
	font-size: 28rpx;
	font-weight: 500;
	color: #64748b;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.btn-secondary:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
	flex: 1;
	padding: 28rpx 25rpx;
	text-align: center;
	background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
	color: white;
	border-radius: 28rpx;
	font-size: 28rpx;
	font-weight: 600;
	border: none;
	box-shadow: 0 6rpx 20rpx rgba(24, 144, 255, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
}

.btn-primary::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transition: left 0.5s;
}

.btn-primary:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.4);
}

.btn-primary:active::before {
	left: 100%;
}

/* 详细舌象特征列表 */
.tongue-features-detail {
	margin-top: 30rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.features-header {
	text-align: center;
	margin-bottom: 30rpx;
}

.features-title {
	font-size: 36rpx;
	font-weight: 700;
	background: linear-gradient(135deg, #667eea, #764ba2);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	margin-bottom: 8rpx;
	letter-spacing: 1rpx;
}

.features-subtitle {
	font-size: 24rpx;
	color: #8a92b2;
	text-align: center;
	margin-bottom: 10rpx;
	font-weight: 500;
}

.features-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
	margin-top: 20rpx;
}

.feature-card {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.7));
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.feature-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
	transition: all 0.3s ease;
}

.feature-card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.normal-card {
	border-left: 4rpx solid transparent;
	background: linear-gradient(135deg, rgba(82, 196, 26, 0.05), rgba(183, 235, 143, 0.05));
}

.normal-card::before {
	background: linear-gradient(90deg, #52c41a, #b7eb8f, #52c41a);
}

.abnormal-card {
	border-left: 4rpx solid transparent;
	background: linear-gradient(135deg, rgba(255, 77, 79, 0.05), rgba(255, 158, 158, 0.05));
}

.abnormal-card::before {
	background: linear-gradient(90deg, #ff4d4f, #ff9e9e, #ff4d4f);
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.feature-badge {
	font-size: 20rpx;
	font-weight: 600;
	padding: 6rpx 14rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	text-transform: uppercase;
}

.normal-badge {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

.abnormal-badge {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.3);
}

.feature-category {
	font-size: 22rpx;
	color: #8a92b2;
	background: rgba(255, 255, 255, 0.6);
	padding: 6rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(5rpx);
}

.feature-name {
	font-size: 28rpx;
	font-weight: 600;
	color: #2c3e50;
	margin-bottom: 12rpx;
	line-height: 1.4;
}

.feature-description {
	font-size: 24rpx;
	color: #64748b;
	line-height: 1.6;
	margin-top: 8rpx;
	font-weight: 400;
}

.no-features {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}

.empty-icon {
	font-size: 48rpx;
	margin-bottom: 16rpx;
	opacity: 0.7;
}

.empty-text {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.empty-hint {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}

/* 动态显示检测到的舌象特征 */
.displayedTongueFeatures {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	pointer-events: none;
}

/* 获取舌象特征的动态位置 */
.getMarkerPosition {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

/* 保养建议样式 */
.care-suggestions-section {
	/* margin: 30rpx;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2); */
}

.care-category {
	margin-bottom: 40rpx;
	background: rgba(255, 255, 255, 0.6);
	border-radius: 16rpx;
	padding: 30rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.04);
}

.care-category:last-child {
	margin-bottom: 0;
}

.category-header {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid rgba(79, 172, 254, 0.1);
}

.category-icon {
	font-size: 48rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.food-icon {
	background: linear-gradient(135deg, #ff9a56, #ffad56);
}

.sport-icon {
	background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.life-icon {
	background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.music-icon {
	background: linear-gradient(135deg, #667eea, #764ba2);
}

.treatment-icon {
	background: linear-gradient(135deg, #f093fb, #f5576c);
}

.category-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
	letter-spacing: 1rpx;
}

.category-subtitle {
	font-size: 24rpx;
	color: #8a92b2;
	font-weight: 500;
	margin-left: auto;
	background: rgba(255, 255, 255, 0.8);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.suggestion-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.suggestion-item {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
	border-radius: 12rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(255, 255, 255, 0.4);
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.suggestion-item:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.item-header {
	margin-bottom: 16rpx;
}

.item-tag {
	display: inline-block;
	font-size: 22rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	letter-spacing: 0.5rpx;
	color: white;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.forbidden-tag {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.recommend-tag {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}

.life-tag {
	background: linear-gradient(135deg, #722ed1, #9254de);
}

.music-tag {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.treatment-tag {
	background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.item-content {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	font-weight: 400;
	letter-spacing: 0.5rpx;
}

.treatment-note {
	margin-top: 16rpx;
	background: rgba(255, 193, 7, 0.1);
	border: 1rpx solid rgba(255, 193, 7, 0.3);
	border-radius: 8rpx;
	padding: 12rpx 16rpx;
}

.note-warning {
	font-size: 24rpx;
	color: #fa8c16;
	font-weight: 500;
}

.care-reminder {
	background: linear-gradient(135deg, rgba(24, 144, 255, 0.05), rgba(64, 169, 255, 0.03));
	border-radius: 16rpx;
	padding: 30rpx;
	border: 2rpx solid rgba(24, 144, 255, 0.1);
	position: relative;
	margin-top: 30rpx;
}

.reminder-header {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
}

.reminder-icon {
	font-size: 40rpx;
	margin-right: 16rpx;
}

.reminder-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1890ff;
}

.reminder-content {
	font-size: 28rpx;
	color: #4a5568;
	line-height: 1.8;
	padding-right: 100rpx;
}

.doctor-avatar-reminder {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.8);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

/* 无保养建议数据提示 */
.no-care-suggestions {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}

.no-care-text {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.no-care-hint {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}



.theory-item {
	margin-bottom: 40rpx;
}

.theory-item:last-child {
	margin-bottom: 0;
}

.theory-header {
	display: flex;
	align-items: baseline;
	gap: 10rpx;
	margin-bottom: 15rpx;
}

.theory-name {
	font-size: 32rpx;
	font-weight: 500;
}

.theory-name.water-wet {
	color: #1890ff;
}

.theory-name.spleen-weak {
	color: #52c41a;
}

.theory-summary {
	font-size: 28rpx;
	color: #666;
}

.theory-subtitle {
	margin-bottom: 20rpx;
}

.theory-subtitle text {
	font-size: 28rpx;
	color: #333;
}

.theory-content {
	position: relative;
}

.theory-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	padding-right: 100rpx;
}

/* 体质分析卡片 */
.theory-card {
	background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid rgba(24, 144, 255, 0.1);
	box-shadow: 0 8rpx 32rpx rgba(24, 144, 255, 0.12);
}

.card-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.theory-tag {
	font-size: 24rpx;
	font-weight: 600;
	padding: 6rpx 12rpx;
	border-radius: 16rpx;
	letter-spacing: 0.5rpx;
	text-transform: uppercase;
}

.theory-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #1890ff;
}

.header-right {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-indicator {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.status-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	box-shadow: 0 0 8rpx rgba(82, 196, 26, 0.4);
}

.status-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
}

.content-wrapper {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.medical-stamp {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.doctor-avatar-small {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	flex-shrink: 0;
}

.stamp-text {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

/* 空状态 */
.empty-theory-state {
	text-align: center;
	padding: 60rpx 40rpx;
	background: linear-gradient(135deg, rgba(248, 250, 252, 0.8), rgba(241, 245, 249, 0.6));
	border-radius: 16rpx;
	border: 2rpx dashed rgba(203, 213, 225, 0.6);
}

.empty-title {
	font-size: 28rpx;
	color: #64748b;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.empty-description {
	font-size: 22rpx;
	color: #94a3b8;
	font-weight: 400;
}

.loading-dots {
	display: flex;
	justify-content: center;
	gap: 10rpx;
	margin-top: 20rpx;
}

.dot {
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #999;
	animation: blink 1s infinite;
}

@keyframes blink {
	0%, 100% { opacity: 1; }
	50% { opacity: 0.5; }
}

/* 舌象特征标记位置定义 - 优化后的紧凑布局 */
.marker-root {
	top: 20% !important;
	left: 50% !important;
}

.marker-tongue {
	top: 38% !important;
	left: 50% !important;
}

.marker-thickness {
	top: 45% !important;
	left: 50% !important;
}

.marker-center {
	top: 50% !important;
	left: 50% !important;
}

.marker-indent {
	top: 45% !important;
	left: 18% !important;
}

.marker-sides {
	top: 40% !important;
	left: 15% !important;
}

.marker-spots {
	top: 50% !important;
	left: 85% !important;
}

.marker-cracks {
	top: 42% !important;
	left: 82% !important;
}

.marker-punctures {
	top: 38% !important;
	left: 28% !important;
}

.marker-mucus {
	top: 38% !important;
	left: 50% !important;
}

.marker-thick {
	top: 35% !important;
	left: 35% !important;
}

.marker-decay {
	top: 35% !important;
	left: 65% !important;
}

.marker-moisture {
	top: 38% !important;
	left: 65% !important;
}

.marker-peeling {
	top: 32% !important;
	left: 50% !important;
}

.marker-color {
	top: 65% !important;
	left: 50% !important;
}

.marker-tip {
	top: 75% !important;
	left: 50% !important;
}

/* 小程序特殊定位 - 优化后的紧凑布局 */
/* #ifdef MP */
.marker-root {
	top: 95rpx !important;
	left: 300rpx !important;
}

.marker-tongue {
	top: 180rpx !important;
	left: 300rpx !important;
}

.marker-thickness {
	top: 216rpx !important;
	left: 300rpx !important;
}

.marker-center {
	top: 240rpx !important;
	left: 300rpx !important;
}

.marker-indent {
	top: 216rpx !important;
	left: 90rpx !important;
}

.marker-sides {
	top: 192rpx !important;
	left: 75rpx !important;
}

.marker-spots {
	top: 240rpx !important;
	left: 510rpx !important;
}

.marker-cracks {
	top: 202rpx !important;
	left: 450rpx !important;
}

.marker-punctures {
	top: 182rpx !important;
	left: 160rpx !important;
}

.marker-mucus {
	top: 182rpx !important;
	left: 300rpx !important;
}

.marker-thick {
	top: 168rpx !important;
	left: 210rpx !important;
}

.marker-decay {
	top: 168rpx !important;
	left: 390rpx !important;
}

.marker-moisture {
	top: 182rpx !important;
	left: 390rpx !important;
}

.marker-peeling {
	top: 154rpx !important;
	left: 300rpx !important;
}

.marker-color {
	top: 330rpx !important;
	left: 300rpx !important;
}

.marker-tip {
	top: 380rpx !important;
	left: 300rpx !important;
}
/* #endif */

/* 推荐商品 - 新增模块 */
.recommend-products-section {
	margin: 30rpx 20rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.recommend-header {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 24rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.recommend-icon-wrapper {
	display: flex;
	align-items: center;
	justify-content: center;
}

.recommend-icon {
	font-size: 24rpx;
	color: white;
	font-weight: 600;
	width: 60rpx;
	height: 60rpx;
	border-radius: 12rpx;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.2);
}

.recommend-title-wrapper {
	flex: 1;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 4rpx;
	line-height: 1.4;
}

.recommend-subtitle {
	font-size: 24rpx;
	color: #999999;
	font-weight: 400;
}

.products-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.product-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #ffffff;
	border-radius: 12rpx;
	border: 1rpx solid #f0f0f0;
	transition: all 0.2s ease;
	position: relative;
	gap: 20rpx;
}

.product-item:active {
	background: #f8f9fa;
	transform: scale(0.98);
}

.product-image-wrapper {
	width: 120rpx;
	height: 120rpx;
	border-radius: 8rpx;
	overflow: hidden;
	background: #f5f5f5;
	position: relative;
	flex-shrink: 0;
}

.product-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.product-type-badge {
	position: absolute;
	top: 4rpx;
	left: 4rpx;
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
	font-size: 18rpx;
	font-weight: 500;
}

.course-badge {
	background: rgba(255, 154, 86, 0.9);
	color: white;
}

.goods-badge {
	background: rgba(79, 172, 254, 0.9);
	color: white;
}

.type-text {
	font-size: 18rpx;
	font-weight: 500;
	color: white;
}

.product-info-wrapper {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	min-width: 0;
}

.product-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	line-height: 1.4;
	margin-bottom: 8rpx;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
}

.product-price-wrapper {
	display: flex;
	align-items: baseline;
	gap: 2rpx;
	margin-bottom: 8rpx;
}

.price-symbol {
	font-size: 24rpx;
	color: #ff6b35;
	font-weight: 600;
}

.price-value {
	font-size: 32rpx;
	color: #ff6b35;
	font-weight: 700;
}

.price-unit {
	font-size: 20rpx;
	color: #ff6b35;
	font-weight: 500;
}

.product-reason {
	margin-bottom: 8rpx;
}

.reason-label {
	font-size: 22rpx;
	color: #666666;
	margin-right: 8rpx;
}

.reason-text {
	font-size: 22rpx;
	color: #4facfe;
	font-weight: 500;
}

.product-sales {
	margin-bottom: 0;
}

.sales-text {
	font-size: 20rpx;
	color: #999999;
}

.product-cart-btn {
	width: 56rpx;
	height: 56rpx;
	border-radius: 28rpx;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(79, 172, 254, 0.3);
	transition: all 0.2s ease;
	flex-shrink: 0;
}

.product-cart-btn:active {
	transform: scale(0.9);
	box-shadow: 0 1rpx 4rpx rgba(79, 172, 254, 0.4);
}

.cart-icon {
	font-size: 28rpx;
	color: white;
}

/* 推荐说明 */
.recommend-notice {
	margin-top: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	border: 1rpx solid #e9ecef;
}

.notice-header {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 12rpx;
}

.notice-icon {
	font-size: 16rpx;
	color: white;
	font-weight: bold;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background: #4facfe;
	display: flex;
	align-items: center;
	justify-content: center;
}

.notice-title {
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;
}

.notice-content {
	font-size: 22rpx;
	color: #666666;
	line-height: 1.6;
}

.free-text {
	font-size: 28rpx;
	color: #52c41a;
	font-weight: 600;
}

/* 视频推荐区域样式 */
.video-recommend-section {
	margin: 30rpx 20rpx;
	background: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.video-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.video-item {
	display: flex;
	flex-direction: column;
	background: #f8f9fa;
	border-radius: 12rpx;
	overflow: hidden;
	margin-bottom: 20rpx;
	transition: all 0.3s ease;
}

.video-item:active {
	transform: scale(0.98);
	background: #e9ecef;
}

.video-player-container {
	width: 100%;
	height: 400rpx;
	position: relative;
	background: #000;
	border-radius: 12rpx 12rpx 0 0;
	overflow: hidden;
}

.video-player {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.video-cover {
	position: relative;
	width: 200rpx;
	height: 120rpx;
	flex-shrink: 0;
	display: none; /* 隐藏，因为现在使用video组件 */
}

.cover-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.play-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-button {
	width: 60rpx;
	height: 60rpx;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.play-icon {
	font-size: 24rpx;
	color: #333;
	margin-left: 4rpx;
}

.video-info {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	background: #fff;
	border-radius: 0 0 12rpx 12rpx;
}

.video-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	line-height: 1.4;
	margin-bottom: 10rpx;
}

.video-reason {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	padding: 8rpx 12rpx;
	background: #e8f5e8;
	color: #4caf50;
	border-radius: 8rpx;
	align-self: flex-start;
}

.video-stats {
	display: flex;
	gap: 20rpx;
}

.stat-item {
	font-size: 22rpx;
	color: #999;
}

/* 视频播放器遮罩层样式 */
.video-player-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.video-player-container {
	background: #fff;
	border-radius: 20rpx;
	overflow: hidden;
	width: 100%;
	max-width: 640rpx;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.video-player-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background: #f8f9fa;
	border-bottom: 1px solid #eee;
}

.video-player-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
	flex: 1;
	margin-right: 20rpx;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	border-radius: 50%;
	cursor: pointer;
}

.close-text {
	font-size: 32rpx;
	color: #666;
	font-weight: bold;
}

.video-player {
	width: 100%;
	height: 400rpx;
	background: #000;
}

.video-player-info {
	padding: 20rpx 30rpx;
	background: #fff;
}

.video-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 15rpx;
	display: block;
}

.video-stats-row {
	display: flex;
	gap: 20rpx;
}

.video-stats-row .stat-item {
	font-size: 24rpx;
	color: #999;
}
</style>